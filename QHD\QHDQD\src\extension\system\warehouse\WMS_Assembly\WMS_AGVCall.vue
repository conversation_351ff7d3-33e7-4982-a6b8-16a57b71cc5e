<template>
    <!-- AGV呼叫弹窗 -->
    <vol-box :lazy="true" v-model="model1" title="AGV呼入" :width="900" :padding="5" :onModelClose="onModelClose">
        <div style="padding: 20px;">
            <h3>主表数据</h3>
            <el-descriptions :column="2" border>
                <el-descriptions-item label="组装编码">{{ mainData.AssembleCode }}</el-descriptions-item>
                <el-descriptions-item label="通知单编码">{{ mainData.AssemblyNo }}</el-descriptions-item>
            </el-descriptions>

            <h3 style="margin-top: 20px;">子表数据</h3>
            <vol-table 
                ref="detailTable"
                :loadKey="true" 
                :columns="detailColumns" 
                :pagination-hide="true" 
                :height="300"
                :tableData="detailData"
                :row-index="true"
                :index="false"
                :ck="false">
            </vol-table>
        </div>

        <template #footer>
            <div>
                <el-button type="primary" size="small" @click="confirmCall">确认呼叫</el-button>
                <el-button type="default" size="small" @click="model1 = false">关闭</el-button>
            </div>
        </template>
    </vol-box>
</template>

<script>
import VolBox from '@/components/basic/VolBox.vue';
import VolTable from '@/components/basic/VolTable.vue';

export default {
    components: { 
        'vol-box': VolBox,
        'vol-table': VolTable
    },
    data() {
        return {
            model1: false,
            mainData: {}, // 主表数据
            detailData: [], // 子表数据
            detailColumns: [
                { field: 'ItemName', title: '物料名称', width: 150 },
                { field: 'ItemCode', title: '物料编号', width: 150 },
                { field: 'ItemModel', title: '规格型号', width: 150 },
                { field: 'ToolNumber', title: '刀号', width: 90 },
                { field: 'BindiToolHandle', title: '刀柄绑定二维码', width: 120 },
                { field: 'Location', title: '刀柄类型', width: 120 }
            ]
        };
    },
    methods: {
        openModel1(mainRow, detailRows) {
            try {
                console.group('AGV呼叫弹窗数据接收');
                console.log('主表数据:', JSON.parse(JSON.stringify(mainRow || {})));
                console.log('子表数据:', JSON.parse(JSON.stringify(detailRows || [])));
                console.groupEnd();
                
                this.mainData = mainRow || {};
                this.detailData = Array.isArray(detailRows) ? detailRows : [];
                this.model1 = true;
                
                // 确保表格数据更新
                this.$nextTick(() => {
                    if (this.$refs.detailTable) {
                        try {
                            // 使用load方法刷新表格数据
                            if (this.$refs.detailTable && this.$refs.detailTable.load) {
                              this.$refs.detailTable.load();
                            } else {
                              console.error('表格组件或load方法不可用');
                              throw new Error('表格刷新失败');
                            }
                            console.log('表格数据刷新成功');
                        } catch (e) {
                            console.error('表格刷新异常:', e);
                            this.$message.error('数据加载异常: ' + (e.message || '未知错误'));
                            this.model1 = false;
                        }
                    } else {
                        console.error('表格组件实例未找到');
                        this.$message.error('组件初始化失败');
                        this.model1 = false;
                    }
                });
            } catch (e) {
                console.error('弹窗打开异常:', e);
                this.$message.error('弹窗初始化失败: ' + (e.message || '未知错误'));
                this.model1 = false;
            }
        },
        confirmCall() {
            try {
                if (!this.detailData || this.detailData.length === 0) {
                    this.$message.error('请先选择要呼叫的物料');
                    return;
                }
                console.group('AGV呼叫请求数据');
                console.log('主表数据:', JSON.parse(JSON.stringify(this.mainData)));
                console.log('子表数据:', JSON.parse(JSON.stringify(this.detailData)));
                console.groupEnd();

                this.$confirm('确认要呼叫AGV运送这些物料吗?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$message.info('正在呼叫AGV...');
                    
                    // 调用父组件方法
                    this.$emit('parentCall', $parent => {
                        try {
                            if (!$parent || !$parent.http) {
                                throw new Error('父组件实例或http方法不可用');
                            }

                            console.log('正在调用AGV接口...');
                            $parent.http.post('api/WMS_Assembly/CallInAGV', {
                                mainData: this.mainData,
                                detailData: this.detailData
                            }, true).then(res => {
                                console.log('AGV接口响应:', res);
                                if (res && res.status) {
                                    this.$message.success(res.message || 'AGV呼叫成功');
                                    if ($parent.search) {
                                        $parent.search(); // 刷新父表格
                                    }
                                } else {
                                    throw new Error(res?.message || 'AGV呼叫失败');
                                }
                            }).catch(err => {
                                console.error('AGV接口调用异常:', err);
                                throw err;
                            });
                        } catch (e) {
                            console.error('父组件处理异常:', e);
                            this.$message.error(e.message || 'AGV呼叫处理失败');
                            throw e;
                        }
                    });
                }).catch(err => {
                    if (err && err !== 'cancel') {
                        console.error('确认对话框异常:', err);
                        this.$message.error(err.message || '操作异常');
                    } else {
                        console.log('用户取消呼叫');
                        this.$message.info('已取消呼叫');
                    }
                }).finally(() => {
                    this.model1 = false;
                });
            } catch (e) {
                console.error('呼叫处理流程异常:', e);
                this.$message.error(e.message || '操作失败，请重试');
                this.model1 = false;
            }
        },
        onModelClose() {
            this.$message.success('弹窗已关闭');
        }
    }
};
</script>

<style scoped>
h3 {
    margin-bottom: 15px;
    color: #409EFF;
}
</style>
