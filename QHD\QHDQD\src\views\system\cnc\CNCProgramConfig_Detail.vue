<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/cnc/CNCProgramConfig_Detail.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/cnc/CNCProgramConfig_Detail.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'ID',
                footer: "Foots",
                cnName: 'CNC程序配置明细表',
                name: 'cnc/CNCProgramConfig_Detail',
                url: "/CNCProgramConfig_Detail/",
                sortName: "ID"
            });
            const editFormFields = ref({"ConfigID":"","MachiningProgram":"","ToolNumber":"","ToolCode":"","ToolDiameter":"","ToolSettingLength":"","SpindleSpeed":"","FeedRate":"","AllowanceL":"","AllowanceLD":"","ToolOffsetNumberH":"","ToolOffsetNumberD":"","Duration":"","Remark":"","ZDepth":""});
            const editFormOptions = ref([[{"title":"加工程序","field":"MachiningProgram"},
                               {"title":"刀号","field":"ToolNumber"},
                               {"title":"刀型编号","field":"ToolCode"},
                               {"title":"刀具直径","field":"ToolDiameter"},
                               {"title":"装刀长度","field":"ToolSettingLength"},
                               {"title":"转速","field":"SpindleSpeed"},
                               {"title":"进给","field":"FeedRate"},
                               {"title":"余量侧","field":"AllowanceL"},
                               {"title":"余量底","field":"AllowanceLD"},
                               {"title":"刀补号H","field":"ToolOffsetNumberH"},
                               {"title":"刀补号D","field":"ToolOffsetNumberD"},
                               {"title":"时间","field":"Duration"},
                               {"title":"备注","field":"Remark"},
                               {"title":"Z深","field":"ZDepth"},
                               {"title":"主表ID","required":true,"field":"ConfigID","type":"number"}]]);
            const searchFormFields = ref({});
            const searchFormOptions = ref([]);
            const columns = ref([{field:'ID',title:'ID',type:'int',width:110,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'ConfigID',title:'主表ID',type:'int',width:110,hidden:true,require:true,align:'left'},
                       {field:'MachiningProgram',title:'加工程序',type:'string',width:220,align:'left',sort:true},
                       {field:'ToolNumber',title:'刀号',type:'string',width:220,align:'left'},
                       {field:'ToolCode',title:'刀型编号',type:'string',width:220,align:'left'},
                       {field:'ToolDiameter',title:'刀具直径',type:'string',width:220,align:'left'},
                       {field:'ToolSettingLength',title:'装刀长度',type:'string',width:220,align:'left'},
                       {field:'SpindleSpeed',title:'转速',type:'string',width:220,align:'left'},
                       {field:'FeedRate',title:'进给',type:'string',width:220,align:'left'},
                       {field:'AllowanceL',title:'余量侧',type:'string',width:220,align:'left'},
                       {field:'AllowanceLD',title:'余量底',type:'string',width:220,align:'left'},
                       {field:'ToolOffsetNumberH',title:'刀补号H',type:'string',width:220,align:'left'},
                       {field:'ToolOffsetNumberD',title:'刀补号D',type:'string',width:220,align:'left'},
                       {field:'Duration',title:'时间',type:'string',width:220,align:'left'},
                       {field:'Remark',title:'备注',type:'string',width:220,align:'left'},
                       {field:'ZDepth',title:'Z深',type:'string',width:220,align:'left'}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
