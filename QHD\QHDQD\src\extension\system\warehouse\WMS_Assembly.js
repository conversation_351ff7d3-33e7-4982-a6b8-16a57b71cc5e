//此js文件是用来自定义扩展业务代码，可以扩展一些自定义页面或者重新配置生成的代码
import gridFooter from  '/src/views/system/warehouse/WMS_AssemblyDetailsRE.vue';
import gridFooter2 from './WMS_Assembly/WMS_AGVCall.vue';
import gridFooter3 from './WMS_Assembly/WMS_AGVCall2.vue';
import QRCode from 'qrcode';
import { ElLoading } from 'element-plus';

let extension = {
  components: {
    //查询界面扩展组件
    gridHeader: '',
    gridBody: '',
    gridFooter: gridFooter,
    gridFooter2: gridFooter2,
    gridFooter3: gridFooter3,
    //新建、编辑弹出框扩展组件
    modelHeader: '',
    modelBody: '',
	modelRight: '',
    modelFooter: ''
  },
  tableAction: '', //指定某张表的权限(这里填写表名,默认不用填写)
  buttons: { view: [], box: [], detail: [] }, //扩展的按钮
  methods: { 
    getFormOption(field) {
      let option;
      this.editFormOptions.forEach(x => {
        x.forEach(item => {
          if (item.field == field) {
            option = item;
          }
        })
      })
      return option;
    },

    // 显示二维码方法
    showQRCode(rowData) {
      let loadingInstance = null;

      try {
        console.log('生成二维码，行数据:', rowData);

        // 显示加载提示
        loadingInstance = ElLoading.service({
          lock: true,
          text: '正在生成二维码...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });

        // 获取明细数据
        let assemblyId = Number(rowData.AssemblyID);
        this.http.get(`api/WMS_Assembly/GetZzDetailed?AssemblyID=${assemblyId}`,
          {},
          true
        ).then(result => {
          try {
            // 验证返回数据
            if (!result || !result.data) {
              throw new Error('未获取到明细数据');
            }

            const detailData = Array.isArray(result.data) ? result.data : [];
            if (detailData.length === 0) {
              if (loadingInstance) loadingInstance.close();
              this.$Message.warning('没有找到明细数据');
              return;
            }

            // 格式化数据并生成二维码数据
            const qrDataArray = detailData.map(item => {
              const containerPoint = item.ContainerPoint || item.containerPoint || '';
              const itemModel = item.ItemModel || item.itemModel || '';
              const bindiToolHandle = item.BindiToolHandle || item.bindiToolHandle || '';
              return `{${containerPoint}/${itemModel}/${bindiToolHandle}}`;
            });

            // 将所有明细数据用换行符连接
            const qrDataString = qrDataArray.join('');

            console.log('二维码数据:', qrDataString);

            // 生成二维码
            QRCode.toDataURL(qrDataString, {
              width: 250,
              margin: 2,
              color: {
                dark: '#000000',
                light: '#FFFFFF'
              }
            }).then(qrCodeDataURL => {
              // 关闭加载提示
              if (loadingInstance) loadingInstance.close();
              // 显示二维码弹窗
              this.showQRCodeDialog(qrCodeDataURL, rowData, detailData.length);
            }).catch(qrError => {
              // 关闭加载提示
              if (loadingInstance) loadingInstance.close();
              console.error('生成二维码失败:', qrError);
              this.$Message.error('生成二维码失败: ' + (qrError.message || '未知错误'));
            });

          } catch (error) {
            if (loadingInstance) loadingInstance.close();
            console.error('数据处理错误:', error);
            this.$Message.error(`数据处理失败: ${error.message}`);
          }
        }).catch(err => {
          if (loadingInstance) loadingInstance.close();
          console.error('获取明细数据失败:', err);
          this.$Message.error(`获取明细数据失败: ${err.message || '未知错误'}`);
        });

      } catch (error) {
        if (loadingInstance) {
          loadingInstance.close();
        }
        console.error('生成二维码失败:', error);
        this.$Message.error(`生成二维码失败: ${error.message}`);
      }
    },

    // 显示二维码弹窗
    showQRCodeDialog(qrCodeImage, rowData, detailCount) {
      // 创建一个临时的div来显示二维码
      const tempDiv = document.createElement('div');
      tempDiv.style.textAlign = 'center';
      tempDiv.style.padding = '20px';

      const img = document.createElement('img');
      img.src = qrCodeImage;
      img.alt = 'QR Code';
      img.style.width = '250px';
      img.style.height = '250px';
      img.style.border = '1px solid #ddd';
      img.style.borderRadius = '4px';

      const p1 = document.createElement('p');
      p1.style.marginTop = '15px';
      p1.style.color = '#666';
      p1.style.fontSize = '14px';
      p1.textContent = `包含 ${detailCount} 条组刀明细数据`;
      tempDiv.appendChild(img);
      tempDiv.appendChild(p1);

      this.$confirm(tempDiv.outerHTML, `${rowData.AssembleCode || ''} 组刀明细信息二维码`, {
        confirmButtonText: '关闭',
        showCancelButton: false,
        type: 'info',
        customClass: 'qr-code-dialog',
        dangerouslyUseHTMLString: true,
        center: true
      }).then(() => {
        console.log('用户确认查看二维码');
      }).catch(() => {
        console.log('用户关闭二维码弹窗');
      });
    },



     //下面这些方法可以保留也可以删除
   onInit() {

    this.columns.forEach((x) => {
         x.normal = true;

    });

    // 添加操作列
    this.columns.push({
      title: '操作',
      hidden: false,
      align: "center",
      fixed: 'right',
      width: 120,
      render: (h, { row, column, index }) => {
        return h(
          "div", { style: { 'font-size': '13px', 'cursor': 'pointer', 'color': '#409eff' } }, [
          h(
            "a", {
            style: { 'margin-right': '15px' },
            onClick: (e) => {
              e.stopPropagation();
              this.showQRCode(row);
            }
          }, "二维码"
          )
        ])
      }
    });
      var AssemblyNo = this.getFormOption('AssemblyNo')
                    console.log(AssemblyNo) 
      AssemblyNo.onChange = (val, option) => {
        if (val !== '') {
          this.$refs.detail.reset();
          this.http.get('api/Workpiece_Notice/GetDetail?id='+option.NoticeID,{},true).then(result=>{
            console.log(result.data);
            
            // 确保返回数据是数组
            const data = Array.isArray(result?.data) ? result.data : [];
            
            // 安全遍历数据
            data.forEach(item => {
              if (item && typeof item === 'object') {
                this.$refs.detail.addRow({
                  ItemType: Number(1),
                  ItemName: item.itemName || '',
                  ItemCode: item.itemCode || '',
                  ItemModel: item.itemModel || '',
                  ItemID: item.itemID || '',
                  ToolNumber: item.toolNumber || ''
                });
              }
            });
          }).catch(error => {
            console.error('获取明细数据失败:', error);
          });
        }
        }
    },
    onInited() {

        this.buttons.splice(3, 0, {
          name: '呼入AGV',
          icon: 'el-icon-phone',
          type: 'primary',
          plain: true,
          onClick: () => {
            try {
              // 修改: 统一获取选中行逻辑，避免重复获取
              const selectedRows = this.getSelectRows();
              // 修改: 检查选中行数量，只允许选择一行
              if (!selectedRows || selectedRows.length === 0) {
                throw new Error('请选择要呼叫的行');
              } else if (selectedRows.length > 1) {
                throw new Error('只能选择一行进行呼叫');
              }
              
              const rowData = selectedRows[0];
              
              // 获取明细数据
              console.log('请求明细数据，AssemblyID:', rowData.AssemblyID);
              // 确保AssemblyID为有效的整数
              let assemblyId = Number(rowData.AssemblyID);
              this.http.get(`api/WMS_Assembly/GetZzDetailed?AssemblyID=${assemblyId}`, 
                {}, 
                true
              ).then(result => {
                try {
                  // 验证返回数据
                  if (!result || !result.data) {
                    throw new Error('未获取到明细数据');
                  }
                  
                  // 处理返回的list集合数据
                  console.log('原始数据格式:', typeof result.data, result.data);
                  const detailData = Array.isArray(result.data) ? result.data : [];
                  if (detailData.length === 0) {
                    this.$Message.warning('没有找到明细数据');
                    return;
                  }

                  // 格式化数据，确保包含必要字段
                  const formattedData = detailData.map((item, index) => {
                    console.log(`处理第${index}条数据:`, item);
                    return {
                      ...item,
                      // 确保关键字段存在
                      ID: item.ID || 0,
                      ItemName: item.ItemName || '未知物料',
                      ItemCode: item.ItemCode || '',
                      // 生成唯一key
                      key: item.ID ? `item_${item.ID}` : `item_${Date.now()}_${index}`
                    };
                  });
                  console.log('格式化后数据:', formattedData);

                  // 按照VolCore文档规范打开弹窗
                  const modal = {
                    title: `AGV呼叫明细 - ${rowData.AssemblyNo}`,
                    width: '80%',
                    height: '500px',
                    data: formattedData,
                    columns: [
                      { field: 'ItemName', title: '物料名称', width: 150 },
                      { field: 'ItemCode', title: '物料编号', width: 150 },
                      { field: 'ItemModel', title: '规格型号', width: 150 },
                      { field: 'ToolNumber', title: '刀号', width: 100 }
                    ],
                    onConfirm: () => {
                      console.log('确认呼叫AGV');
                    }
                  };
                  
                  if (this.$refs.gridFooter2 && this.$refs.gridFooter2.openModel1) {
                    this.$refs.gridFooter2.openModel1(
                      {
                        AssembleCode: rowData.AssembleCode || '',
                        AssemblyNo: rowData.AssemblyNo || ''
                      },
                      formattedData.map(item => ({
                        ItemName: item.itemName || '',
                        ItemCode: item.itemCode || '',
                        ItemModel: item.itemModel || '',
                        ToolNumber: item.toolNumber || '',
                        BindiToolHandle: item.bindiToolHandle || '',
                        Location: item.location || '1'
                      }))
                    );
                  } else {
                    console.error('无法调用WMS_AGVCall组件的openModel1方法');
                    this.$Message.error('弹窗组件调用失败');
                  }

                } catch (error) {
                  console.error('数据处理错误:', error);
                  this.$Message.error(`数据处理失败: ${error.message}`);
                }
              }).catch(err => {
                console.error('获取明细数据失败:', err);
                this.$Message.error(`获取明细数据失败: ${err.message || '未知错误'}`);
              });

            } catch (error) {
              this.$Message.error(error.message);
              console.error('AGV呼叫失败:', error);
            }
          }
        })

        // 添加第二个AGV呼叫按钮
        this.buttons.splice(4, 0, {
          name: '呼出AGV',
          icon: 'el-icon-phone-outline',
          type: 'primary',
          plain: true,
          onClick: () => {
            try {
              // 修改: 统一获取选中行逻辑，避免重复获取
              const selectedRows = this.getSelectRows();
              // 修改: 检查选中行数量，只允许选择一行
              if (!selectedRows || selectedRows.length === 0) {
                throw new Error('请选择要呼叫的行');
              } else if (selectedRows.length > 1) {
                throw new Error('只能选择一行进行呼叫');
              }
              
              const rowData = selectedRows[0];
              
              // 获取明细数据
              console.log('请求明细数据，AssemblyID:', rowData.AssemblyID);
              // 确保AssemblyID为有效的整数
              let assemblyId = Number(rowData.AssemblyID);
              this.http.get(`api/WMS_Assembly/GetZzDetailed?AssemblyID=${assemblyId}`, 
                {}, 
                true
              ).then(result => {
                try {
                  // 验证返回数据
                  if (!result || !result.data) {
                    throw new Error('未获取到明细数据');
                  }
                  
                  // 处理返回的list集合数据
                  console.log('原始数据格式:', typeof result.data, result.data);
                  const detailData = Array.isArray(result.data) ? result.data : [];
                  if (detailData.length === 0) {
                    this.$Message.warning('没有找到明细数据');
                    return;
                  }

                  // 格式化数据，确保包含必要字段
                  const formattedData = detailData.map((item, index) => {
                    console.log(`处理第${index}条数据:`, item);
                    return {
                      ...item,
                      // 确保关键字段存在
                      ID: item.ID || 0,
                      ItemName: item.ItemName || '未知物料',
                      ItemCode: item.ItemCode || '',
                      // 生成唯一key
                      key: item.ID ? `item_${item.ID}` : `item_${Date.now()}_${index}`
                    };
                  });
                  // 按照VolCore文档规范打开弹窗
                  const modal = {
                    title: `AGV呼叫明细 - ${rowData.AssemblyNo}`,
                    width: '80%',
                    height: '500px',
                    data: formattedData,
                    columns: [
                      { field: 'ItemName', title: '物料名称', width: 150 },
                      { field: 'ItemCode', title: '物料编号', width: 150 },
                      { field: 'ItemModel', title: '规格型号', width: 150 },
                      { field: 'ToolNumber', title: '刀号', width: 100 }
                    ],
                    onConfirm: () => {
                      console.log('确认呼叫AGV');
                    }
                  };
                  
                  if (this.$refs.gridFooter3 && this.$refs.gridFooter3.openModel1) {
                    this.$refs.gridFooter3.openModel1(
                      {
                        AssembleCode: rowData.AssembleCode || '',
                        AssemblyNo: rowData.AssemblyNo || ''
                      },
                      formattedData.map(item => ({
                        ItemName: item.itemName || '',
                        ItemCode: item.itemCode || '',
                        ItemModel: item.itemModel || '',
                        ToolNumber: item.toolNumber || '',
                        BindiToolHandle: item.bindiToolHandle || '',
                        Location: item.location || '1'
                      }))
                    );
                  } else {
                    console.error('无法调用WMS_AGVCall组件的openModel1方法');
                    this.$Message.error('弹窗组件调用失败');
                  }

                } catch (error) {
                  console.error('数据处理错误:', error);
                  this.$Message.error(`数据处理失败: ${error.message}`);
                }
              }).catch(err => {
                console.error('获取明细数据失败:', err);
                this.$Message.error(`获取明细数据失败: ${err.message || '未知错误'}`);
              });

            } catch (error) {
              this.$Message.error(error.message);
              console.error('AGV呼叫失败:', error);
            }
          }
        })



      //框架初始化配置后
      //如果要配置明细表,在此方法操作
      //this.detailOptions.columns.forEach(column=>{ });
        this.height = this.height-210-50
    },

      storeComfirm(e){
        //"/api/WMS_Assembly/CellAGV"
          let url =
          'api/Other/GetMachining'
           this.http.post(url, e, true).then((x) => {
           if(x.Status){ 
               this.$Message.success(x.message);
           }
           else{
               this.$Message.error(x.message);
           }
        });
        },

    searchBefore(param) {
      //界面查询前,可以给param.wheres添加查询参数
      //返回false，则不会执行查询
      return true;
    },
     rowClick({ row, column, event }) {
      //查询界面点击行事件

      if (this.$refs.gridFooter && this.$refs.gridFooter.$refs.tableList) {
        this.$refs.gridFooter.$refs.tableList.load({ value: row.AssemblyID, sort: "ID" })
      }
    },
    searchAfter(result) {
      //查询后，result返回的查询数据,可以在显示到表格前处理表格的值
      if (result.length) {
        // this.$nextTick(() => {
        this.$refs.gridFooter.$refs.tableList.load({ value: result[0].AssemblyID, name: "ID" })
        // })
      } else {
        //没有数据时，清空明细数据
        this.$refs.gridFooter.$refs.tableList.rowData.splice(0)
      }
      return true;
    },
    addBefore(formData) {
      //新建保存前formData为对象，包括明细表，可以给给表单设置值，自己输出看formData的值
      return true;
    },
    updateBefore(formData) {
      //编辑保存前formData为对象，包括明细表、删除行的Id
      return true;
    },

    modelOpenAfter(row) {
      this.editFormFields.AssemblyNotemp=row.AssemblyNo
      this.detailOptions.buttons.forEach((btn) => {
              if (btn.name == '添加行' ||btn.name == '删除行'||btn.name == '导入'||btn.name == '导出') {
                  btn.hidden = true;
                  //或者设置只读
                  //btn.readonly=true;
              }
          });
    }
  }
};
export default extension;