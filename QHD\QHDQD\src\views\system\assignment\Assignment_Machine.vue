<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/assignment/Assignment_Machine.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/assignment/Assignment_Machine.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'ID',
                footer: "Foots",
                cnName: '排产设备',
                name: 'assignment/Assignment_Machine',
                url: "/Assignment_Machine/",
                sortName: "ID"
            });
            const editFormFields = ref({});
            const editFormOptions = ref([]);
            const searchFormFields = ref({});
            const searchFormOptions = ref([]);
            const columns = ref([{field:'ID',title:'ID',type:'int',width:110,require:true,align:'left',sort:true},
                       {field:'MachineId',title:'设备编号',type:'string',width:110,align:'left'},
                       {field:'CurrentStatus',title:'设备状态',type:'string',width:110,align:'left'},
                       {field:'NextMaintenanceTime',title:'设备下次保养时间',type:'string',width:110,align:'left'},
                       {field:'AssID',title:'工单ID',type:'int',width:110,hidden:true,align:'left'},
                       {field:'OrderNumber',title:'工单编号',type:'string',width:110,align:'left'}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
