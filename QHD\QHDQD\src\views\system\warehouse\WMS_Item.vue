<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/warehouse/WMS_Item.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/warehouse/WMS_Item.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'ItemID',
                footer: "Foots",
                cnName: '物料管理',
                name: 'warehouse/WMS_Item',
                url: "/WMS_Item/",
                sortName: "ItemID"
            });
            const editFormFields = ref({"ItemType":"","ItemCode":"","ItemName":"","ItemModel":"","MemoryCode":"","MaterialUnit":"","MaterialPrice":"","AuxiliaryProperties":"","MaterialProperties":"","Location":"","ShelfLife":"","MateStates":"","ToolNumber":"","Remark":""});
            const editFormOptions = ref([[{"dataKey":"物料类型","data":[],"title":"物品类型","required":true,"field":"ItemType","type":"select"},
                               {"title":"物品编码","required":true,"field":"ItemCode"},
                               {"title":"物品名称","required":true,"field":"ItemName"}],
                              [{"title":"规格型号","field":"ItemModel"},
                               {"title":"助记码","field":"MemoryCode"},
                               {"dataKey":"物料单位","data":[],"title":"单位","field":"MaterialUnit","type":"select"}],
                              [{"title":"单价","field":"MaterialPrice"},
                               {"title":"辅助属性","field":"AuxiliaryProperties"},
                               {"title":"物料属性","field":"MaterialProperties"}],
                              [{"title":"保质期(天)","field":"ShelfLife"},
                               {"title":"物料状态","field":"MateStates"},
                               {"title":"刀库","field":"Location"}],
                              [{"title":"刀号","field":"ToolNumber"}],
                              [{"title":"备注","field":"Remark","colSize":12,"type":"textarea"}]]);
            const searchFormFields = ref({"ItemCode":"","ItemName":"","ItemModel":"","MemoryCode":"","MaterialUnit":"","MaterialPrice":""});
            const searchFormOptions = ref([[{"title":"物品编码","field":"ItemCode"},{"title":"物品名称","field":"ItemName"},{"title":"规格型号","field":"ItemModel"}],[{"title":"助记码","field":"MemoryCode"},{"dataKey":"物料单位","data":[],"title":"单位","field":"MaterialUnit","type":"select"},{"title":"单价","field":"MaterialPrice"}]]);
            const columns = ref([{field:'ItemType',title:'物品类型',type:'byte',bind:{ key:'物料类型',data:[]},width:110,require:true,align:'left',sort:true},
                       {field:'ItemID',title:'物品ID',type:'int',width:110,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'ItemCode',title:'物品编码',type:'string',link:true,width:180,require:true,align:'left'},
                       {field:'ItemName',title:'物品名称',type:'string',width:180,require:true,align:'left'},
                       {field:'ItemModel',title:'规格型号',type:'string',width:140,align:'left'},
                       {field:'ItemBrand',title:'品牌 / 制造商',type:'string',width:110,hidden:true,align:'left'},
                       {field:'ItemVendorCode',title:'供应商编码',type:'string',width:110,hidden:true,align:'left'},
                       {field:'IsActive',title:'是否启用',type:'int',bind:{ key:'enable',data:[]},width:110,hidden:true,require:true,align:'left'},
                       {field:'CreateID',title:'创建人',type:'int',width:100,hidden:true,align:'left'},
                       {field:'Creator',title:'创建人',type:'string',width:100,hidden:true,align:'left'},
                       {field:'CreateDate',title:'创建日期',type:'datetime',width:150,hidden:true,align:'left',sort:true},
                       {field:'ModifyID',title:'ModifyID',type:'int',width:100,hidden:true,align:'left'},
                       {field:'Modifier',title:'Modifier',type:'string',width:100,hidden:true,align:'left'},
                       {field:'ModifyDate',title:'ModifyDate',type:'datetime',width:150,hidden:true,align:'left',sort:true},
                       {field:'MemoryCode',title:'助记码',type:'string',width:160,align:'left'},
                       {field:'MaterialUnit',title:'单位',type:'string',bind:{ key:'物料单位',data:[]},width:120,align:'left'},
                       {field:'MaterialPrice',title:'单价',type:'string',width:120,align:'left'},
                       {field:'Location',title:'刀库',type:'string',width:120,align:'left'},
                       {field:'ToolNumber',title:'刀号',type:'string',width:120,align:'left'},
                       {field:'AuxiliaryProperties',title:'辅助属性',type:'string',width:180,align:'left'},
                       {field:'MaterialProperties',title:'物料属性',type:'string',width:180,align:'left'},
                       {field:'ShelfLife',title:'保质期(天)',type:'string',width:120,align:'left'},
                       {field:'MateStates',title:'物料状态',type:'string',width:120,align:'left'},
                       {field:'Remark',title:'备注',type:'string',width:200,align:'left'}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
