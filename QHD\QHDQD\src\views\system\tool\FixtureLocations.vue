<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/tool/FixtureLocations.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/tool/FixtureLocations.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'LocationID',
                footer: "Foots",
                cnName: '仓位管理',
                name: 'tool/FixtureLocations',
                url: "/FixtureLocations/",
                sortName: "LocationNumber"
            });
            const editFormFields = ref({"LocationNumber":"","RFID":"","Status":"","Model":""});
            const editFormOptions = ref([[{"title":"仓位序号","field":"LocationNumber","type":"number"},
                               {"title":"RFID标签编号","field":"RFID"}],
                              [{"dataKey":"仓位状态","data":[],"title":"仓位状态","required":true,"field":"Status","type":"select"},
                               {"dataKey":"夹具型号","data":[],"title":"夹具型号","field":"Model","type":"select"}]]);
            const searchFormFields = ref({"LocationNumber":"","RFID":"","Status":"","Model":""});
            const searchFormOptions = ref([[{"title":"仓位序号","field":"LocationNumber","type":"text"},{"title":"RFID标签编号","field":"RFID"}],[{"dataKey":"仓位状态","data":[],"title":"仓位状态","field":"Status","type":"select"},{"dataKey":"夹具型号","data":[],"title":"夹具型号","field":"Model","type":"select"}]]);
            const columns = ref([{field:'LocationID',title:'自增主键，唯一标识每个仓位',type:'int',width:110,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'LocationNumber',title:'仓位序号',type:'byte',width:110,align:'left',sort:true},
                       {field:'RFID',title:'RFID标签编号',type:'string',width:110,align:'left'},
                       {field:'Status',title:'仓位状态',type:'byte',bind:{ key:'仓位状态',data:[]},width:110,require:true,align:'left'},
                       {field:'Name',title:'夹具名称',type:'string',width:120,hidden:true,align:'left'},
                       {field:'Model',title:'夹具型号',type:'string',bind:{ key:'夹具型号',data:[]},width:110,align:'left'},
                       {field:'CreateTime',title:'记录创建时间',type:'datetime',width:150,hidden:true,align:'left',sort:true},
                       {field:'UpdateTime',title:'记录最后更新时间',type:'datetime',width:150,hidden:true,align:'left',sort:true}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
