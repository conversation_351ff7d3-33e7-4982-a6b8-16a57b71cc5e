
let viewgird = [
  {
    path: '/Sys_Log',
    name: 'sys_Log',
    component: () => import('@/views/system/Sys_Log.vue')
  },
  {
    path: '/Sys_User',
    name: 'Sys_User',
    component: () => import('@/views/system/Sys_User.vue')
  },
  {
    path: '/permission',
    name: 'permission',
    component: () => import('@/views/system/Permission.vue')
  },

  {
    path: '/Sys_Dictionary',
    name: 'Sys_Dictionary',
    component: () => import('@/views/system/Sys_Dictionary.vue')
  },
  {
    path: '/Sys_Role',
    name: 'Sys_Role',
    component: () => import('@/views/system/Sys_Role.vue')
  }, {
    path: '/Sys_Role1',
    name: 'Sys_Role1',
    component: () => import('@/views/system/Sys_Role1.vue')
  }
  , {
    path: '/Sys_DictionaryList',
    name: 'Sys_DictionaryList',
    component: () => import('@/views/system/Sys_DictionaryList.vue')
  }, {
    path: '/FormDesignOptions',
    name: 'FormDesignOptions',
    component: () => import('@/views/system/form/FormDesignOptions.vue')
  }, {
    path: '/FormCollectionObject',
    name: 'FormCollectionObject',
    component: () => import('@/views/system/form/FormCollectionObject.vue')
  }, {
    path: '/Sys_WorkFlow',
    name: 'Sys_WorkFlow',
    component: () => import('@/views/system/flow/Sys_WorkFlow.vue')
  }, {
    path: '/Sys_WorkFlowTable',
    name: 'Sys_WorkFlowTable',
    component: () => import('@/views/system/flow/Sys_WorkFlowTable.vue')
  }, {
    path: '/Sys_QuartzOptions',
    name: 'Sys_QuartzOptions',
    component: () => import('@/views/system/quartz/Sys_QuartzOptions.vue')
  }, {
    path: '/Sys_QuartzLog',
    name: 'Sys_QuartzLog',
    component: () => import('@/views/system/quartz/Sys_QuartzLog.vue')
  }, {
    path: '/Sys_Department',
    name: 'Sys_Department',
    component: () => import('@/views/system/system/Sys_Department.vue')
  }    ,{
        path: '/MES_Equip',
        name: 'MES_Equip',
        component: () => import('@/views/system/device/MES_Equip.vue')
    }    ,{
        path: '/MES_Conserve',
        name: 'MES_Conserve',
        component: () => import('@/views/system/device/MES_Conserve.vue')
    }    ,{
        path: '/View_MES_Conserve',
        name: 'View_MES_Conserve',
        component: () => import('@/views/system/device/View_MES_Conserve.vue')
    }    ,{
        path: '/View_MES_Maintenance',
        name: 'View_MES_Maintenance',
        component: () => import('@/views/system/device/View_MES_Maintenance.vue')
    }    ,{
        path: '/Pac_Routing_Op',
        name: 'Pac_Routing_Op',
        component: () => import('@/views/system/product/Pac_Routing_Op.vue')
    }    ,{
        path: '/View_MES_ProductScheduling',
        name: 'View_MES_ProductScheduling',
        component: () => import('@/views/system/product/View_MES_ProductScheduling.vue')
    }    ,{
        path: '/MES_ToolInfo',
        name: 'MES_ToolInfo',
        component: () => import('@/views/system/craft/MES_ToolInfo.vue')
    }    ,{
        path: '/MES_WorkpieceInfo',
        name: 'MES_WorkpieceInfo',
        component: () => import('@/views/system/product/MES_WorkpieceInfo.vue')
    }    ,{
        path: '/MES_WorkpieceInfoDetail',
        name: 'MES_WorkpieceInfoDetail',
        component: () => import('@/views/system/product/MES_WorkpieceInfoDetail.vue')
    }    ,{
        path: '/View_MES_WorkpieceInfo',
        name: 'View_MES_WorkpieceInfo',
        component: () => import('@/views/system/product/View_MES_WorkpieceInfo.vue')
    }    ,{
        path: '/MES_Craft',
        name: 'MES_Craft',
        component: () => import('@/views/system/craft/MES_Craft.vue')
    }    ,{
        path: '/MES_DataGatherAndon',
        name: 'MES_DataGatherAndon',
        component: () => import('@/views/system/craft/MES_DataGatherAndon.vue')
    }    ,{
        path: '/QHD_material',
        name: 'QHD_material',
        component: () => import('@/views/system/warehouse/QHD_material.vue')
    }    ,{
        path: '/WMS_PutStock',
        name: 'WMS_PutStock',
        component: () => import('@/views/system/warehouse/WMS_PutStock.vue')
    }     ,{
        path: '/WMS_InOutTake',
        name: 'WMS_InOutTake',
        component: () => import('@/views/system/task/WMS_InOutTake.vue')
    }    ,{
        path: '/WMS_PutStockDetails',
        name: 'WMS_PutStockDetails',
        component: () => import('@/views/system/warehouse/WMS_PutStockDetails.vue')
    }    ,{
        path: '/WMS_InStock',
        name: 'WMS_InStock',
        component: () => import('@/views/system/warehouse/WMS_InStock.vue')
    }    ,{
        path: '/WMS_InStockDetails',
        name: 'WMS_InStockDetails',
        component: () => import('@/views/system/instock/WMS_InStockDetails.vue')
    }       ,{
        path: '/B_Warehouse',
        name: 'B_Warehouse',
        component: () => import('@/views/system/warehouse/B_Warehouse.vue')
    } ,{
        path: '/MES_Attachment',
        name: 'MES_Attachment',
        component: () => import('@/views/system/craft/MES_Attachment.vue')
    }   ,{
        path: '/View_FMS_cnc1daoku',
        name: 'View_FMS_cnc1daoku',
        component: () => import('@/views/system/craft/View_FMS_cnc1daoku.vue')
    }    ,{
        path: '/MES_ReportWork',
        name: 'MES_ReportWork',
        component: () => import('@/views/system/reportwork/MES_ReportWork.vue')
    }    ,{
        path: '/WMS_InOutRecord',
        name: 'WMS_InOutRecord',
        component: () => import('@/views/system/warehouse/WMS_InOutRecord.vue')
    }    ,{
        path: '/WMS_InOutRecordDetail',
        name: 'WMS_InOutRecordDetail',
        component: () => import('@/views/system/warehouse/WMS_InOutRecordDetail.vue')
    }    ,{
        path: '/WMS_ToolInfo',
        name: 'WMS_ToolInfo',
        component: () => import('@/views/system/warehouse/WMS_ToolInfo.vue')
    }    ,{
        path: '/WMS_Warehouse',
        name: 'WMS_Warehouse',
        component: () => import('@/views/system/warehouse/WMS_Warehouse.vue')
    }    ,{
        path: '/WMS_Inventory',
        name: 'WMS_Inventory',
        component: () => import('@/views/system/warehouse/WMS_Inventory.vue')
    }    ,{
        path: '/View_WMS_InOutRecordDetail_Fixture',
        name: 'View_WMS_InOutRecordDetail_Fixture',
        component: () => import('@/views/system/warehouse/View_WMS_InOutRecordDetail_Fixture.vue')
    }    ,{
        path: '/WMS_InOutRecordDetail_Fixture',
        name: 'WMS_InOutRecordDetail_Fixture',
        component: () => import('@/views/system/warehouse/WMS_InOutRecordDetail_Fixture.vue')
    }    ,{
        path: '/WMS_InOutRecord_Fixture',
        name: 'WMS_InOutRecord_Fixture',
        component: () => import('@/views/system/warehouse/WMS_InOutRecord_Fixture.vue')
    }    ,{
        path: '/WMS_Fixture',
        name: 'WMS_Fixture',
        component: () => import('@/views/system/warehouse/WMS_Fixture.vue')
    }    ,{
        path: '/WMS_InOutRecord_KnifeHandle',
        name: 'WMS_InOutRecord_KnifeHandle',
        component: () => import('@/views/system/warehouse/WMS_InOutRecord_KnifeHandle.vue')
    }    ,{
        path: '/Orders_Table',
        name: 'Orders_Table',
        component: () => import('@/views/system/product/Orders_Table.vue')
    }    ,{
        path: '/Assignment_Table',
        name: 'Assignment_Table',
        component: () => import('@/views/system/product/Assignment_Table.vue')
    }    ,{
        path: '/Assignment_ Detail',
        name: 'Assignment_ Detail',
        component: () => import('@/views/system/product/Assignment_Detail.vue')
    }    ,{
        path: '/Assignment_Detail',
        name: 'Assignment_Detail',
        component: () => import('@/views/system/product/Assignment_Detail.vue')
    }    ,{
        path: '/Supplier',
        name: 'Supplier',
        component: () => import('@/views/system/warehouse/Supplier.vue')
    }    ,{
        path: '/WMS_Item',
        name: 'WMS_Item',
        component: () => import('@/views/system/warehouse/WMS_Item.vue')
    }    ,{
        path: '/WMS_InOutRecord_Fixtured',
        name: 'WMS_InOutRecord_Fixtured',
        component: () => import('@/views/system/warehouse/WMS_InOutRecord_Fixtured.vue')
    }    ,{
        path: '/AGV_Notification_Main',
        name: 'AGV_Notification_Main',
        component: () => import('@/views/system/agv/AGV_Notification_Main.vue')
    }    ,{
        path: '/AGV_Notification_Detail',
        name: 'AGV_Notification_Detail',
        component: () => import('@/views/system/agv/AGV_Notification_Detail.vue')
    }    ,{
        path: '/AGV_Task_Records',
        name: 'AGV_Task_Records',
        component: () => import('@/views/system/agv/AGV_Task_Records.vue')
    }  ,{
        path: '/WMS_InOutRecord_Tool',
        name: 'WMS_InOutRecord_Tool',
        component: () => import('@/views/system/warehouse/WMS_InOutRecord_Tool.vue')
    } ,{
        path: '/Parameter_Settings',
        name: 'Parameter_Settings',
        component: () => import('@/views/system/warehouse/Parameter_Settings.vue')
    }    ,{
        path: '/Interval_Setting',
        name: 'Interval_Setting',
        component: () => import('@/views/system/system/Interval_Setting.vue')
    }    ,{
        path: '/WMS_Stock',
        name: 'WMS_Stock',
        component: () => import('@/views/system/warehouse/WMS_Stock.vue')
    },{
        path: '/Workpiece_Notice',
        name: 'Workpiece_Notice',
        component: () => import('@/views/system/warehouse/Workpiece_Notice.vue')  

    }    ,{
        path: '/WMS_AssemblyDetails',
        name: 'WMS_AssemblyDetails',
        component: () => import('@/views/system/warehouse/WMS_AssemblyDetails.vue')
    }    ,{
        path: '/WMS_Assembly',
        name: 'WMS_Assembly',
        component: () => import('@/views/system/warehouse/WMS_Assembly.vue')
    }    ,{
        path: '/UsageRecord',
        name: 'UsageRecord',
        component: () => import('@/views/system/warehouse/UsageRecord.vue')
    }    ,{
        path: '/Outbound_Order',
        name: 'Outbound_Order',
        component: () => import('@/views/system/warehouse/Outbound_Order.vue')
    }    ,{
        path: '/CodingRules',
        name: 'CodingRules',
        component: () => import('@/views/system/warehouse/CodingRules.vue')
    }    ,{
        path: '/CNCProgramConfig',
        name: 'CNCProgramConfig',
        component: () => import('@/views/system/cnc/CNCProgramConfig.vue')
    }    ,{
        path: '/CNCProgramConfig_Detail',
        name: 'CNCProgramConfig_Detail',
        component: () => import('@/views/system/cnc/CNCProgramConfig_Detail.vue')
    }    ,{
        path: '/Assignment_Material',
        name: 'Assignment_Material',
        component: () => import('@/views/system/assignment/Assignment_Material.vue')
    }    ,{
        path: '/Assignment_Machine',
        name: 'Assignment_Machine',
        component: () => import('@/views/system/assignment/Assignment_Machine.vue')
    }    ,{
        path: '/WMS_StockDetail',
        name: 'WMS_StockDetail',
        component: () => import('@/views/system/stock/WMS_StockDetail.vue')
    }    ,{
        path: '/Assignment_Table_View',
        name: 'Assignment_Table_View',
        component: () => import('@/views/system/produce/Assignment_Table_View.vue')
    }    ,{
        path: '/Assignment_ErrHD',
        name: 'Assignment_ErrHD',
        component: () => import('@/views/system/assignment_errhd/Assignment_ErrHD.vue')
    }    ,{
        path: '/ToolPresetter',
        name: 'ToolPresetter',
        component: () => import('@/views/system/warehouse/ToolPresetter.vue')
    }    ,{
        path: '/AGV_Point_Mapping',
        name: 'AGV_Point_Mapping',
        component: () => import('@/views/system/agv/AGV_Point_Mapping.vue')
    }    ,{
        path: '/ToolWarehouse',
        name: 'ToolWarehouse',
        component: () => import('@/views/system/tool/ToolWarehouse.vue')
    }    ,{
        path: '/MachineToolWarehouse',
        name: 'MachineToolWarehouse',
        component: () => import('@/views/system/tool/MachineToolWarehouse.vue')
    }    ,{
        path: '/FixtureLocations',
        name: 'FixtureLocations',
        component: () => import('@/views/system/tool/FixtureLocations.vue')
    }    ,{
        path: '/Assignment_Task',
        name: 'Assignment_Task',
        component: () => import('@/views/system/product/Assignment_Task.vue')
    }    ,{
        path: '/View_Assignment_Task',
        name: 'View_Assignment_Task',
        component: () => import('@/views/system/product/View_Assignment_Task.vue')
    }]

export default viewgird
