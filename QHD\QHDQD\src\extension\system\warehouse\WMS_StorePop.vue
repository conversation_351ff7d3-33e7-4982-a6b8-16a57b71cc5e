<template>
  <div>
    <vol-box
      v-model="model"
      :padding="30"
      title="入库"
      :width="1000"
      :height="250"
    >
      <!-- <el-alert type="success">
        <h3>
          <span>帐号：{{ row.UserName }}</span>
          <span>用户：{{ row.UserTrueName }}</span>
        </h3>
      </el-alert>
      <div>
        <el-input
          placeholder="请输入密码"
          v-model="password"
          size="large"
          style="width: 100%; margin-top: 15px"
        />
      </div> -->
      <cytable
            :table-data="tableData"
            :column="column"
            :config="tableConfig"
        >
            <template #realNum="{row}">
                <el-input type="number" v-model="row.realNum" placeholder="请输入上货数量" />
            </template>
            <template #storageLocation="{row}">
                <el-tree-select 
                    :props="{ label: 'warehouseName', value: 'id', children: 'children' }"
                    :data="selectOptions.location"
                    v-model="row.storageLocation"
                    placeholder="请选择存储货位"
                    :render-after-expand="false"
                />
            </template>
            <!-- <template #caozuo="{row,$index}">
                <el-button type="primary" size="small" text @click="unpack($index)">拆分</el-button>
                <el-button type="danger" size="small" v-if="row.isChai" text @click="deleteChai($index)">删除</el-button>
            </template> -->
        </cytable>
      <template #footer>
        <el-button
          type="primary"
          @click="savePwd()"
          >确认</el-button
        >
        <el-button
          @click="model = false"
          >关闭</el-button
        >
      </template>
    </vol-box>
  </div>
</template>
<script>
import cytable from '@/components/cytable/cytable.vue'
import { defineComponent, defineAsyncComponent } from "vue";
export default defineComponent({
  components: {
    VolBox: defineAsyncComponent(() => import("@/components/basic/VolBox.vue")),
    cytable
  },
  data() {
    return {
      row: {},
      password: "",
      model: false,
      tableData :[],
        // 表格配置
        tableConfig :{
            isSelection: false,
            border: true,
            showIndex:true,
            height:'500px',
            pageNum:0,
        },
        column:[
            {
            label: "物料名称",
            prop: "stuName",
            //   width: "210px",
            },
            {
            label: "物料编号",
            prop: "codes",
            // width: "140px",
            },
            {
            label: "总数量",
            prop: "initialquantity",
            width: "80px",
            },
            {
            label: "合格数量",
            prop: "qualifiedquantity",
            width: "90px",
            },
            {
            label: "剩余数量",
            prop: "currentquantity",
            width: "90px",
            },
   
            {
            label: "实际上货数量",
            prop: "realNum",
            width: "140px",
            },
            {
            label: "存储库位",
            prop: "storageLocation",
            // width: "190px",
            },
            {
            label: "操作",
            prop: "caozuo",
            width: "110px",
            },
        ],
    };
  },
  methods: {
    
    open(row) {
      console.log('?????????',row)
      this.row = row;
      this.model = true;
      this.getData()
    },
    getData() {
      let url =
        "/api/WMS_InStock/selectMaterialDetail?MID=" +
        this.row.WIID
      this.http.get(url).then((x) => {
        
      });
    },
    savePwd() {
      if (!this.password) return this.$Message.error("请输密码");
      if (this.password.length < 6)
        return this.$Message.error("密码长度至少6位");
      let url =
        "/api/user/modifyUserPwd?password=" +
        this.password +
        "&userName=" +
        this.row.UserName;
      this.http.post(url, {}, true).then((x) => {
        if (!x.status) {
          return this.$message.error(x.message);
        }
        this.model = false;
        this.$Message.success(x.message);
      });
    },
  },
  created() {},
})
</script>
<style lang="less" scoped>
h3 {
  font-weight: 500;
  > span:last-child {
    margin-left: 30px;
  }
}
</style>