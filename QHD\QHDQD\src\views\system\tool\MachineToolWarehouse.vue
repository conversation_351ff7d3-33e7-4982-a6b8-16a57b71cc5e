<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/tool/MachineToolWarehouse.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/tool/MachineToolWarehouse.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'ID',
                footer: "Foots",
                cnName: '机台刀具管理',
                name: 'tool/MachineToolWarehouse',
                url: "/MachineToolWarehouse/",
                sortName: "ID"
            });
            const editFormFields = ref({"MachineName":"","ToolModelNo":"","ToolNo":"","ToolCategory":"","ToolSpecification":"","ToolHolderSpecification":"","ClampingLength":"","TotalToolLength":"","ProcessingMaterial":"","UsageFunction":"","OffsetNoH":"","OffsetNoD":"","MonitoringMethod":"","ReplacementMethod":"","SetValue":"","ActualValue":"","ToolSettingRequirement":"","ToolStatus":""});
            const editFormOptions = ref([[{"dataKey":"设备列表","data":[],"title":"机台名称","field":"MachineName","type":"select"},
                               {"title":"刀型编号","field":"ToolModelNo"},
                               {"title":"刀具编号","field":"ToolNo"}],
                              [{"title":"刀具类别","field":"ToolCategory"},
                               {"title":"刀具规格","field":"ToolSpecification"},
                               {"title":"刀柄规格","field":"ToolHolderSpecification"}],
                              [{"title":"装夹长度","field":"ClampingLength","type":"decimal"},
                               {"title":"刀具总长","field":"TotalToolLength","type":"decimal"},
                               {"title":"加工材料","field":"ProcessingMaterial"}],
                              [{"title":"使用功能","field":"UsageFunction"},
                               {"title":"补刀号H","field":"OffsetNoH"},
                               {"title":"补刀号D","field":"OffsetNoD"}],
                              [{"title":"监控方式","field":"MonitoringMethod"},
                               {"title":"更换方式","field":"ReplacementMethod"},
                               {"title":"设定值","field":"SetValue","type":"decimal"}],
                              [{"title":"实际值","field":"ActualValue","type":"decimal"},
                               {"title":"对刀要求","field":"ToolSettingRequirement"},
                               {"title":"刀具状态","field":"ToolStatus"}]]);
            const searchFormFields = ref({"MachineName":"","ToolModelNo":"","ToolNo":"","ToolCategory":"","ToolSpecification":"","ToolHolderSpecification":"","ClampingLength":"","TotalToolLength":"","ProcessingMaterial":"","UsageFunction":"","OffsetNoH":"","OffsetNoD":"","MonitoringMethod":"","ReplacementMethod":"","SetValue":"","ActualValue":"","ToolSettingRequirement":"","ToolStatus":""});
            const searchFormOptions = ref([[{"dataKey":"设备列表","data":[],"title":"机台名称","field":"MachineName","type":"like"},{"title":"刀型编号","field":"ToolModelNo"},{"title":"刀具编号","field":"ToolNo"}],[{"title":"刀具类别","field":"ToolCategory"},{"title":"刀具规格","field":"ToolSpecification"},{"title":"刀柄规格","field":"ToolHolderSpecification"}],[{"title":"装夹长度","field":"ClampingLength","type":"decimal"},{"title":"刀具总长","field":"TotalToolLength","type":"decimal"},{"title":"加工材料","field":"ProcessingMaterial"}],[{"title":"使用功能","field":"UsageFunction"},{"title":"补刀号H","field":"OffsetNoH"},{"title":"补刀号D","field":"OffsetNoD"}],[{"title":"监控方式","field":"MonitoringMethod"},{"title":"更换方式","field":"ReplacementMethod"},{"title":"设定值","field":"SetValue","type":"decimal"}],[{"title":"实际值","field":"ActualValue","type":"decimal"},{"title":"对刀要求","field":"ToolSettingRequirement"},{"title":"刀具状态","field":"ToolStatus"}]]);
            const columns = ref([{field:'ID',title:'自增主键，唯一标识每条刀具记录',type:'int',width:110,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'MachineName',title:'机台名称',type:'string',bind:{ key:'设备列表',data:[]},width:180,align:'left',sort:true},
                       {field:'ToolPosition',title:'刀位号',type:'string',width:110,align:'left'},
                       {field:'PositionProperty',title:'刀位性质',type:'string',width:110,align:'left'},
                       {field:'ToolModelNo',title:'刀型编号',type:'string',width:110,align:'left'},
                       {field:'ToolNo',title:'刀具编号',type:'string',width:110,align:'left'},
                       {field:'ToolCategory',title:'刀具类别',type:'string',width:110,align:'left'},
                       {field:'ToolSpecification',title:'刀具规格',type:'string',width:110,align:'left'},
                       {field:'ToolHolderSpecification',title:'刀柄规格',type:'string',width:110,align:'left'},
                       {field:'ClampingLength',title:'装夹长度',type:'decimal',width:110,align:'left'},
                       {field:'TotalToolLength',title:'刀具总长',type:'decimal',width:110,align:'left'},
                       {field:'ProcessingMaterial',title:'加工材料',type:'string',width:110,align:'left'},
                       {field:'UsageFunction',title:'使用功能',type:'string',width:110,align:'left'},
                       {field:'OffsetNoH',title:'补刀号H',type:'string',width:110,align:'left'},
                       {field:'OffsetNoD',title:'补刀号D',type:'string',width:110,align:'left'},
                       {field:'MonitoringMethod',title:'监控方式',type:'string',width:110,align:'left'},
                       {field:'ReplacementMethod',title:'更换方式',type:'string',width:110,align:'left'},
                       {field:'SetValue',title:'设定值',type:'decimal',width:110,align:'left'},
                       {field:'ActualValue',title:'实际值',type:'decimal',width:110,align:'left'},
                       {field:'ToolSettingRequirement',title:'对刀要求',type:'string',width:110,align:'left'},
                       {field:'ToolStatus',title:'刀具状态',type:'string',width:110,align:'left'}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
