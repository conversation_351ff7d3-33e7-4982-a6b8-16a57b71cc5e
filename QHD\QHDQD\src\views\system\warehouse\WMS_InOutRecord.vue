<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/warehouse/WMS_InOutRecord.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/warehouse/WMS_InOutRecord.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'RecordID',
                footer: "Foots",
                cnName: '刀具出入库',
                name: 'warehouse/WMS_InOutRecord',
                url: "/WMS_InOutRecord/",
                sortName: "RecordID"
            });
            const editFormFields = ref({"RecordCode":"","RecordType":"","StartWarehouseId":"","EndWarehouseId":"","Remark":""});
            const editFormOptions = ref([[{"title":"出库编号","required":true,"field":"RecordCode"},
                               {"dataKey":"记录类型","data":[],"title":"记录类型","required":true,"field":"RecordType","type":"select"}],
                              [{"dataKey":"仓库库位","data":[],"title":"起始仓库","field":"StartWarehouseId","type":"select"},
                               {"dataKey":"仓库库位","data":[],"title":"目标仓库","field":"EndWarehouseId","type":"select"}],
                              [{"title":"备注","field":"Remark","type":"textarea"}]]);
            const searchFormFields = ref({"RecordCode":"","RecordType":"","Status":""});
            const searchFormOptions = ref([[{"title":"出库编号","field":"RecordCode"},{"dataKey":"记录类型","data":[],"title":"记录类型","field":"RecordType","type":"select"},{"dataKey":"出入库状态","data":[],"title":"状态","field":"Status","type":"select"}]]);
            const columns = ref([{field:'RecordID',title:'记录ID，主键',type:'int',width:110,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'RecordCode',title:'出库编号',type:'string',link:true,width:110,require:true,align:'left',sort:true},
                       {field:'RecordType',title:'记录类型',type:'byte',bind:{ key:'记录类型',data:[]},width:110,require:true,align:'left'},
                       {field:'StartWarehouseId',title:'起始仓库',type:'int',bind:{ key:'仓库库位',data:[]},width:110,align:'left'},
                       {field:'EndWarehouseId',title:'目标仓库',type:'int',bind:{ key:'仓库库位',data:[]},width:110,align:'left'},
                       {field:'Status',title:'状态',type:'int',bind:{ key:'出入库状态',data:[]},width:80,align:'left'},
                       {field:'CreateID',title:'CreateID',type:'int',width:100,hidden:true,align:'left'},
                       {field:'Creator',title:'Creator',type:'string',width:100,hidden:true,align:'left'},
                       {field:'CreateDate',title:'CreateDate',type:'datetime',width:150,hidden:true,align:'left',sort:true},
                       {field:'ModifyID',title:'ModifyID',type:'int',width:100,hidden:true,align:'left'},
                       {field:'Modifier',title:'操作人',type:'string',width:100,align:'left'},
                       {field:'ModifyDate',title:'操作时间',type:'datetime',width:150,align:'left',sort:true},
                       {field:'Remark',title:'备注',type:'string',width:110,align:'left'}]);
            const detail = ref({
                cnName: "刀具出入库明细New",
                table: "WMS_InOutRecordDetail",
                columns: [{field:'DetailID',title:'明细ID，主键',type:'int',width:110,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'RecordCode',title:'出入库编号',type:'string',width:110,require:true,align:'left',sort:true},
                       {field:'RecordID',title:'关联出入库记录ID',type:'int',width:110,hidden:true,require:true,align:'left'},
                       {field:'ItemID',title:'关联物品',type:'int',bind:{ key:'物料编码_刀具',data:[]},width:110,edit:{type:'select'},require:true,align:'left'},
                       {field:'Qty',title:'数量',type:'int',width:110,edit:{type:'text'},require:true,align:'left'},
                       {field:'CreateID',title:'CreateID',type:'int',width:100,hidden:true,align:'left'},
                       {field:'Creator',title:'Creator',type:'string',width:100,hidden:true,align:'left'},
                       {field:'CreateDate',title:'CreateDate',type:'datetime',width:150,hidden:true,align:'left',sort:true},
                       {field:'Remark',title:'备注',type:'string',width:110,edit:{type:''},align:'left'},
                       {field:'ModifyID',title:'ModifyID',type:'int',width:80,hidden:true,align:'left'},
                       {field:'Modifier',title:'Modifier',type:'string',width:130,hidden:true,align:'left'},
                       {field:'ModifyDate',title:'ModifyDate',type:'datetime',width:150,hidden:true,align:'left',sort:true}],
                sortName: "DetailID",
                key: "DetailID"
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
