<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/warehouse/Parameter_Settings.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/warehouse/Parameter_Settings.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'ID',
                footer: "Foots",
                cnName: '参数设置',
                name: 'warehouse/Parameter_Settings',
                url: "/Parameter_Settings/",
                sortName: "ID"
            });
            const editFormFields = ref({"Hilt":"","Length":""});
            const editFormOptions = ref([[{"title":"型号","field":"Hilt","type":"text"},
                               {"title":"区间值","field":"Length","type":"text"}]]);
            const searchFormFields = ref({"Hilt":""});
            const searchFormOptions = ref([[{"title":"型号","field":"Hilt","type":"text"}]]);
            const columns = ref([{field:'ID',title:'ID',type:'int',width:110,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'Hilt',title:'型号',type:'string',width:110,align:'left',sort:true},
                       {field:'Length',title:'区间值',type:'string',width:110,align:'left'}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
