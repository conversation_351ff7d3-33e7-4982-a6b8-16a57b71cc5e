<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/warehouse/B_Warehouse.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/warehouse/B_Warehouse.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'Id',
                footer: "Foots",
                cnName: '库位管理',
                name: 'warehouse/B_Warehouse',
                url: "/B_Warehouse/",
                sortName: "Id"
            });
            const editFormFields = ref({"WarehouseName":"","WarehouseAddress":"","Status":"","IsValid":""});
            const editFormOptions = ref([[{"title":"仓库名称","required":true,"field":"WarehouseName"}],
                              [{"title":"仓库地址","field":"WarehouseAddress"}],
                              [{"title":"状态","field":"Status","type":"number"}],
                              [{"title":"是否启用","required":true,"field":"IsValid","type":"number"}]]);
            const searchFormFields = ref({});
            const searchFormOptions = ref([]);
            const columns = ref([{field:'WarehouseName',title:'仓库名称',type:'string',width:220,require:true,align:'left',sort:true},
                       {field:'Id',title:'ID',type:'int',width:110,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'Sguid',title:'SGUID;SGU',type:'string',width:220,hidden:true,align:'left'},
                       {field:'WarehouseAddress',title:'仓库地址',type:'string',width:220,align:'left'},
                       {field:'Tenant',title:'租户id',type:'string',width:220,hidden:true,align:'left'},
                       {field:'CreatedBy',title:'创建人',type:'int',width:110,align:'left'},
                       {field:'IsValid',title:'是否启用',type:'int',width:110,require:true,align:'left'},
                       {field:'Status',title:'状态',type:'int',width:110,align:'left'},
                       {field:'CreatedTime',title:'创建时间',type:'datetime',width:150,align:'left',sort:true},
                       {field:'UpdatedBy',title:'更新人',type:'int',width:110,align:'left'},
                       {field:'UpdatedTime',title:'更新时间',type:'datetime',width:150,align:'left',sort:true},
                       {field:'ParentId',title:'父id',type:'int',width:110,hidden:true,align:'left'},
                       {field:'Dataownership',title:'Dataownership',type:'string',width:220,hidden:true,align:'left'}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
