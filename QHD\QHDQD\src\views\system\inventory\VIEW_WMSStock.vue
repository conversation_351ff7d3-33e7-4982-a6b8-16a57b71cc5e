<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/inventory/VIEW_WMSStock.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/inventory/VIEW_WMSStock.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'ID',
                footer: "Foots",
                cnName: '库存管理',
                name: 'inventory/VIEW_WMSStock',
                url: "/VIEW_WMSStock/",
                sortName: "ID"
            });
            const editFormFields = ref({});
            const editFormOptions = ref([]);
            const searchFormFields = ref({});
            const searchFormOptions = ref([]);
            const columns = ref([{field:'qty',title:'刀具数量',type:'decimal',width:110,align:'left',sort:true},
                       {field:'static',title:'状态',type:'string',width:220,align:'left'},
                       {field:'BatchesId',title:'批次号',type:'string',width:220,require:true,align:'left'},
                       {field:'KnifePositionNumber',title:'KnifePositionNumber',type:'string',width:110,align:'left'},
                       {field:'KnifePositionNature',title:'KnifePositionNature',type:'string',width:110,align:'left'},
                       {field:'ToolNumber',title:'ToolNumber',type:'string',width:110,align:'left'},
                       {field:'ToolCategory',title:'ToolCategory',type:'string',width:110,align:'left'},
                       {field:'ToolSpecification',title:'ToolSpecification',type:'string',width:110,align:'left'},
                       {field:'WarehouseName',title:'WarehouseName',type:'string',width:220,align:'left'},
                       {field:'ID',title:'ID',type:'int',width:80,hidden:true,require:true,align:'left'}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
