<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/product/Assignment_Task.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/product/Assignment_Task.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'id',
                footer: "Foots",
                cnName: '排产任务明细',
                name: 'product/Assignment_Task',
                url: "/Assignment_Task/",
                sortName: "id"
            });
            const editFormFields = ref({});
            const editFormOptions = ref([]);
            const searchFormFields = ref({"TaskDetailCode":"","TaskCode":"","OName":"","OType":"","OStatus":"","DevNumber":""});
            const searchFormOptions = ref([[{"title":"任务明细编号","field":"TaskDetailCode"},{"title":"任务编号","field":"TaskCode"},{"title":"任务","field":"OName"}],[{"title":"任务类型","field":"OType"},{"title":"状态","field":"OStatus"},{"title":"设备","field":"DevNumber"}]]);
            const columns = ref([{field:'id',title:'id',type:'int',width:110,hidden:true,require:true,align:'left'},
                       {field:'TaskDetailCode',title:'任务明细编号',type:'string',width:110,align:'left',sort:true},
                       {field:'TaskCode',title:'任务编号',type:'string',width:110,align:'left'},
                       {field:'OName',title:'任务',type:'string',width:110,align:'left'},
                       {field:'OType',title:'任务类型',type:'string',width:110,align:'left'},
                       {field:'OStatus',title:'状态',type:'string',width:110,align:'left'},
                       {field:'CW',title:'仓位',type:'string',width:110,align:'left'},
                       {field:'CWRFID',title:'仓位RFID',type:'string',width:110,align:'left'},
                       {field:'DevNumber',title:'设备',type:'string',width:110,align:'left'},
                       {field:'StatreTime',title:'开始时间',type:'string',width:110,align:'left'},
                       {field:'EndTime',title:'结束时间',type:'string',width:110,align:'left'},
                       {field:'MarAuto',title:'MarAuto',type:'string',width:110,hidden:true,align:'left'},
                       {field:'TaskUserTime',title:'任务时间',type:'string',width:110,align:'left'}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
