<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/warehouse/UsageRecord.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/warehouse/UsageRecord.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'ID',
                footer: "Foots",
                cnName: '刀柄/刀具/夹具使用记录',
                name: 'warehouse/UsageRecord',
                url: "/UsageRecord/",
                sortName: "ID"
            });
            const editFormFields = ref({"ToolNumber":"","BarCode":"","TooName":"","ToolType":"","SpecModel":"","Supplier":"","CurrentLocation":"","PicCode":"","UseTime":"","WearCondition":"","MaintenanceTime":"","MaintenanceContent":""});
            const editFormOptions = ref([[{"title":"物料编号","field":"ToolNumber"},
                               {"title":"条形码","field":"BarCode"},
                               {"title":"物料名称","field":"TooName"}],
                              [{"title":"物料类型","field":"ToolType"},
                               {"title":"规格型号","field":"SpecModel"},
                               {"dataKey":"supplierList","data":[],"title":"供应商","field":"Supplier"}],
                              [{"title":"当前位置","field":"CurrentLocation"},
                               {"title":"责任人","field":"PicCode"},
                               {"title":"累计使用时长","field":"UseTime"}],
                              [{"title":"磨损状态","field":"WearCondition"},
                               {"title":"最后维护日期","field":"MaintenanceTime"}],
                              [{"title":"维护内容","field":"MaintenanceContent"}]]);
            const searchFormFields = ref({"ToolNumber":"","TooName":"","ToolType":"","Supplier":"","PicCode":""});
            const searchFormOptions = ref([[{"title":"物料编号","field":"ToolNumber"},{"title":"物料名称","field":"TooName"},{"title":"物料类型","field":"ToolType"},{"dataKey":"supplierList","data":[],"title":"供应商","field":"Supplier","type":"remoteSearch"},{"title":"责任人","field":"PicCode"}]]);
            const columns = ref([{field:'ID',title:'编号',type:'int',width:110,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'ToolNumber',title:'物料编号',type:'string',width:140,align:'left',sort:true},
                       {field:'BarCode',title:'条形码',type:'string',width:160,align:'left'},
                       {field:'TooName',title:'物料名称',type:'string',width:150,align:'left'},
                       {field:'ToolType',title:'物料类型',type:'string',width:110,align:'left'},
                       {field:'SpecModel',title:'规格型号',type:'string',width:170,align:'left'},
                       {field:'Supplier',title:'供应商',type:'string',bind:{ key:'supplierList',data:[]},width:180,hidden:true,align:'left'},
                       {field:'CurrentLocation',title:'当前位置',type:'string',width:140,align:'left'},
                       {field:'PicCode',title:'责任人',type:'string',width:120,align:'left'},
                       {field:'UseTime',title:'累计使用时长',type:'string',width:130,align:'left'},
                       {field:'WearCondition',title:'磨损状态',type:'string',width:160,align:'left'},
                       {field:'MaintenanceTime',title:'最后维护日期',type:'string',width:160,align:'left'},
                       {field:'MaintenanceContent',title:'维护内容',type:'string',width:200,align:'left'}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
