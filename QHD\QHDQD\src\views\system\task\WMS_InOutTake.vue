<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/task/WMS_InOutTake.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/task/WMS_InOutTake.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'ID',
                footer: "Foots",
                cnName: '单据管理',
                name: 'task/WMS_InOutTake',
                url: "/WMS_InOutTake/",
                sortName: "ID"
            });
            const editFormFields = ref({});
            const editFormOptions = ref([]);
            const searchFormFields = ref({});
            const searchFormOptions = ref([]);
            const columns = ref([{field:'ID',title:'ID',type:'int',width:80,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'ApplicationOrder',title:'申请单号',type:'string',width:120,align:'left',sort:true},
                       {field:'ApplicationTime',title:'申请日期',type:'datetime',width:150,align:'left',sort:true},
                       {field:'ApplicationPerson',title:'申请人',type:'string',width:120,align:'left'},
                       {field:'ApplicationDepart',title:'申请部门',type:'string',width:220,align:'left'},
                       {field:'ExpectedOutTime',title:'ExpectedOutTime',type:'datetime',width:150,align:'left',sort:true},
                       {field:'ApplicationQty',title:'ApplicationQty',type:'decimal',width:110,align:'left'},
                       {field:'DescriptionPurpose',title:'DescriptionPurpose',type:'string',width:220,align:'left'},
                       {field:'Remarks',title:'Remarks',type:'string',width:220,align:'left'},
                       {field:'Static',title:'Static',type:'int',width:80,align:'left'},
                       {field:'Sguid',title:'Sguid',type:'string',width:110,align:'left'},
                       {field:'InOutTakeStatic',title:'InOutTakeStatic',type:'int',width:80,align:'left'},
                       {field:'CreateID',title:'CreateID',type:'int',width:80,hidden:true,align:'left'},
                       {field:'Creator',title:'Creator',type:'string',width:130,align:'left'},
                       {field:'CreateDate',title:'CreateDate',type:'datetime',width:150,align:'left',sort:true},
                       {field:'ModifyID',title:'ModifyID',type:'int',width:80,hidden:true,align:'left'},
                       {field:'Modifier',title:'Modifier',type:'string',width:130,align:'left'},
                       {field:'ModifyDate',title:'ModifyDate',type:'datetime',width:150,align:'left',sort:true}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
