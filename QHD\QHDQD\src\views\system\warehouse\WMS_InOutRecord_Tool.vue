<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/warehouse/WMS_InOutRecord_Tool.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/warehouse/WMS_InOutRecord_Tool.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'RecordID',
                footer: "Foots",
                cnName: '刀具入库单',
                name: 'warehouse/WMS_InOutRecord_Tool',
                url: "/WMS_InOutRecord_Tool/",
                sortName: "RecordID"
            });
            const editFormFields = ref({"Name":"","Code":"","Model":"","Brand":"","SupperCode":"","Qty":"","IntoDate":"","WareHouseId":"","ToolType":"","CutParameters":"","ProcessCoat":"","ServiceLife":"","TestResults":"","Inspector":"","Remark":""});
            const editFormOptions = ref([[{"dataKey":"远程 物料 刀具","data":[],"title":"物品编码","field":"Code","type":"remoteSearch"},
                               {"title":"物品名称","field":"Name"}],
                              [{"title":"型号 / 规格","field":"Model"},
                               {"title":"品牌 / 制造商","field":"Brand"}],
                              [{"dataKey":"远程 供应商编码","data":[],"title":"供应商编码","field":"SupperCode","type":"remoteSearch"},
                               {"title":"入库数量","field":"Qty","type":"decimal"}],
                              [{"title":"入库日期","field":"IntoDate","type":"datetime"},
                               {"dataKey":"仓库库位","data":[],"title":"库位编号","field":"WareHouseId","type":"select"}],
                              [{"dataKey":"刀具类型","data":[],"title":"刀具类型","field":"ToolType","type":"select"},
                               {"title":"切削参数","field":"CutParameters"}],
                              [{"title":"涂层工艺","field":"ProcessCoat"},
                               {"title":"使用寿命","field":"ServiceLife"}],
                              [{"dataKey":"检验结果","data":[],"title":"检验结果","field":"TestResults","type":"select"},
                               {"dataKey":"检验员","data":[],"title":"检验员","field":"Inspector","type":"select"}],
                              [{"title":"备注","field":"Remark","type":"textarea"}]]);
            const searchFormFields = ref({});
            const searchFormOptions = ref([]);
            const columns = ref([{field:'RecordID',title:'RecordID',type:'int',width:110,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'Status',title:'入库状态',type:'int',bind:{ key:'入库状态',data:[]},width:110,hidden:true,align:'left'},
                       {field:'Name',title:'物品名称',type:'string',link:true,width:180,align:'left',sort:true},
                       {field:'Code',title:'物品编码',type:'string',bind:{ key:'远程 物料 刀具',data:[]},width:180,align:'left'},
                       {field:'Model',title:'型号 / 规格',type:'string',width:180,align:'left'},
                       {field:'Brand',title:'品牌 / 制造商',type:'string',width:180,align:'left'},
                       {field:'SupperCode',title:'供应商编码',type:'string',bind:{ key:'远程 供应商编码',data:[]},width:180,align:'left'},
                       {field:'Qty',title:'入库数量',type:'int',width:110,align:'left'},
                       {field:'IntoDate',title:'入库日期',type:'datetime',width:150,align:'left',sort:true},
                       {field:'WareHouseId',title:'库位编号',type:'int',bind:{ key:'仓库库位',data:[]},width:110,align:'left'},
                       {field:'ToolType',title:'刀具类型',type:'int',bind:{ key:'刀具类型',data:[]},width:110,align:'left'},
                       {field:'CutParameters',title:'切削参数',type:'string',width:180,align:'left'},
                       {field:'ProcessCoat',title:'涂层工艺',type:'string',width:180,align:'left'},
                       {field:'ServiceLife',title:'使用寿命',type:'string',width:180,align:'left'},
                       {field:'TestResults',title:'检验结果',type:'string',bind:{ key:'检验结果',data:[]},width:180,align:'left'},
                       {field:'Inspector',title:'检验员',type:'int',bind:{ key:'检验员',data:[]},width:110,align:'left'},
                       {field:'Remark',title:'备注',type:'string',width:110,align:'left'}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
