<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/agv/AGV_Notification_Main.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/agv/AGV_Notification_Main.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'MIID',
                footer: "Foots",
                cnName: 'AGV通知',
                name: 'agv/AGV_Notification_Main',
                url: "/AGV_Notification_Main/",
                sortName: "MIID"
            });
            const editFormFields = ref({"TaskCode":"","TaskNumber":"","StartPoint":"","EndPoint":"","Container":"","CreateTime":""});
            const editFormOptions = ref([[{"title":"AGV编号","required":true,"field":"TaskCode"},
                               {"title":"任务编号","field":"TaskNumber"}],
                              [{"dataKey":"agv点位","data":[],"title":"起始点","required":true,"field":"StartPoint","type":"select"},
                               {"dataKey":"agv点位","data":[],"title":"终点","required":true,"field":"EndPoint","type":"select"}],
                              [{"title":"容器编号","required":true,"field":"Container"},
                               {"title":"创建时间","field":"CreateTime","type":"datetime"}]]);
            const searchFormFields = ref({"TaskCode":"","StartPoint":"","EndPoint":"","Container":"","TaskNumber":""});
            const searchFormOptions = ref([[{"title":"AGV编号","field":"TaskCode","type":"text"},{"title":"任务编号","field":"TaskNumber","type":"text"}],[{"dataKey":"agv点位","data":[],"title":"起始点","field":"StartPoint","type":"select"},{"dataKey":"agv点位","data":[],"title":"终点","field":"EndPoint","type":"select"}],[{"title":"容器编号","field":"Container","type":"text"}]]);
            const columns = ref([{field:'TaskCode',title:'AGV编号',type:'string',link:true,width:110,require:true,align:'left',sort:true},
                       {field:'StartPoint',title:'起始点',type:'string',bind:{ key:'agv点位',data:[]},width:120,require:true,align:'left'},
                       {field:'EndPoint',title:'终点',type:'string',bind:{ key:'agv点位',data:[]},width:120,require:true,align:'left'},
                       {field:'Container',title:'容器编号',type:'string',width:110,require:true,align:'left'},
                       {field:'CreateTime',title:'创建时间',type:'datetime',width:150,align:'left',sort:true},
                       {field:'UpdateTime',title:'更新时',type:'datetime',width:150,align:'left',sort:true},
                       {field:'MIID',title:'序号',type:'int',width:80,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'TaskType',title:'配送类型',type:'string',width:110,hidden:true,align:'left'},
                       {field:'TContent',title:'配送信息',type:'string',width:110,align:'left'},
                       {field:'TaskNumber',title:'任务编号',type:'string',width:110,align:'left'}]);
            const detail = ref({
                cnName: "AGV通知明细",
                table: "AGV_Notification_Detail",
                columns: [{field:'DetailID',title:'明细ID，自增主键，唯一标识每条明细记录',type:'int',width:110,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'MaterialCode',title:'物料编码',type:'string',width:120,edit:{type:''},require:true,align:'left',sort:true},
                       {field:'MaterialName',title:'物料名称',type:'string',bind:{ key:'物料列表',data:[]},width:120,edit:{type:'select'},require:true,align:'left'},
                       {field:'MaterialType',title:'物料类型',type:'string',width:110,edit:{type:''},require:true,align:'left'},
                       {field:'SpecModel',title:'规格型号',type:'string',width:180,edit:{type:''},align:'left'},
                       {field:'Unit',title:'单位',type:'string',width:110,edit:{type:''},require:true,align:'left'},
                       {field:'MTCposition',title:'材质',type:'string',width:110,edit:{type:''},align:'left'},
                       {field:'Quantity',title:'数量',type:'int',width:110,edit:{type:''},require:true,align:'left'},
                       {field:'TaskNumber',title:'任务编号',type:'string',width:110,hidden:true,edit:{type:''},align:'left'},
                       {field:'Remark',title:'备注信息',type:'string',width:220,edit:{type:''},align:'left'},
                       {field:'CreateTime',title:'创建时间',type:'datetime',width:150,hidden:true,align:'left',sort:true},
                       {field:'UpdateTime',title:'更新时间',type:'datetime',width:150,hidden:true,align:'left',sort:true},
                       {field:'MIID',title:'MIID',type:'int',width:110,hidden:true,align:'left'}],
                sortName: "DetailID",
                key: "DetailID"
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
