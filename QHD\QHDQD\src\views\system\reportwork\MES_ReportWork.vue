<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/reportwork/MES_ReportWork.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/reportwork/MES_ReportWork.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'ReportID',
                footer: "Foots",
                cnName: '报工管理',
                name: 'reportwork/MES_ReportWork',
                url: "/MES_ReportWork/",
                sortName: "ReportID"
            });
            const editFormFields = ref({"WorkOrderNo":"","OrderNo":"","EquipmentID":"","Shift":"","Qty":"","GoodQty":"","DefectiveQty":"","ScrapQty":"","StartTime":"","ReportStatus":"","EndTime":"","Remark":""});
            const editFormOptions = ref([[{"dataKey":"工单编号","data":[],"title":"工单编号","field":"WorkOrderNo","type":"select"},
                               {"dataKey":"订单编号","data":[],"title":"订单编号","field":"OrderNo","type":"select"}],
                              [{"title":"设备编号","field":"EquipmentID"},
                               {"title":"班次","field":"Shift"}],
                              [{"title":"数量","field":"Qty","type":"number"},
                               {"title":"良品数量","field":"GoodQty","type":"number"}],
                              [{"title":"次品数量","field":"DefectiveQty","type":"number"},
                               {"title":"废品数量","field":"ScrapQty","type":"number"}],
                              [{"title":"开工时间","field":"StartTime","type":"datetime"},
                               {"dataKey":"ReportStatus","data":[],"title":"报工状态","field":"ReportStatus","type":"select"}],
                              [{"title":"完工时间","field":"EndTime","type":"datetime"}],
                              [{"title":"备注","field":"Remark","type":"textarea"}]]);
            const searchFormFields = ref({"WorkOrderNo":"","ProcessNo":"","StartTime":"","ReportStatus":"","EndTime":""});
            const searchFormOptions = ref([[{"dataKey":"工单编号","data":[],"title":"工单编号","field":"WorkOrderNo"},{"title":"工序编号","field":"ProcessNo"},{"dataKey":"ReportStatus","data":[],"title":"报工状态","field":"ReportStatus","type":"select"}],[{"title":"开工时间","field":"StartTime","type":"date"},{"title":"完工时间","field":"EndTime","type":"date"}]]);
            const columns = ref([{field:'ReportID',title:'报工记录ID',type:'int',width:110,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'WorkOrderNo',title:'工单编号',type:'string',bind:{ key:'工单编号',data:[]},width:110,align:'left',sort:true},
                       {field:'OrderNo',title:'订单编号',type:'string',bind:{ key:'订单编号',data:[]},width:110,align:'left'},
                       {field:'ProcessNo',title:'工序编号',type:'string',width:110,hidden:true,align:'left'},
                       {field:'UserID',title:'员工编号',type:'int',width:110,hidden:true,align:'left'},
                       {field:'UserName',title:'员工姓名',type:'string',width:180,align:'left'},
                       {field:'EquipmentID',title:'设备编号',type:'string',width:110,align:'left'},
                       {field:'Shift',title:'班次',type:'string',width:110,align:'left'},
                       {field:'Qty',title:'数量',type:'int',width:110,align:'left'},
                       {field:'GoodQty',title:'良品数量',type:'int',width:110,align:'left'},
                       {field:'DefectiveQty',title:'次品数量',type:'int',width:110,align:'left'},
                       {field:'ScrapQty',title:'废品数量',type:'int',width:110,align:'left'},
                       {field:'StartTime',title:'开工时间',type:'datetime',width:150,align:'left',sort:true},
                       {field:'ReportStatus',title:'报工状态',type:'byte',bind:{ key:'ReportStatus',data:[]},width:110,align:'left'},
                       {field:'EndTime',title:'完工时间',type:'datetime',width:150,align:'left',sort:true},
                       {field:'AuditStatus',title:'审核状态',type:'int',bind:{ key:'AuditStatus',data:[]},width:110,align:'left'},
                       {field:'AuditDate',title:'审核时间',type:'datetime',width:150,align:'left',sort:true},
                       {field:'Auditor',title:'审核人',type:'string',width:180,align:'left'},
                       {field:'Remark',title:'备注',type:'string',width:220,align:'left'},
                       {field:'CreateID',title:'创建人ID',type:'int',width:100,hidden:true,align:'left'},
                       {field:'Creator',title:'创建人',type:'string',width:100,align:'left'},
                       {field:'CreateDate',title:'创建时间',type:'datetime',width:150,align:'left',sort:true},
                       {field:'ModifyID',title:'修改人ID',type:'int',width:100,hidden:true,align:'left'},
                       {field:'Modifier',title:'修改人',type:'string',width:100,align:'left'},
                       {field:'ModifyDate',title:'修改时间',type:'datetime',width:150,align:'left',sort:true},
                       {field:'AuditReason',title:'审核原因',type:'string',width:220,hidden:true,align:'left'}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
