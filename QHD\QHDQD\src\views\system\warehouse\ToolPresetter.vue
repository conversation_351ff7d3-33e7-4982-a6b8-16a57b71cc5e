<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/warehouse/ToolPresetter.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/warehouse/ToolPresetter.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'ID',
                footer: "Foots",
                cnName: '对刀仪数据表',
                name: 'warehouse/ToolPresetter',
                url: "/ToolPresetter/",
                sortName: "ID"
            });
            const editFormFields = ref({});
            const editFormOptions = ref([]);
            const searchFormFields = ref({});
            const searchFormOptions = ref([]);
            const columns = ref([{field:'ID',title:'ID',type:'int',width:110,require:true,align:'left',sort:true},
                       {field:'MaterialNumber',title:'物料编码',type:'string',width:110,align:'left'},
                       {field:'ConvertCover',title:'转换套',type:'string',width:110,align:'left'},
                       {field:'Date',title:'日期',type:'datetime',width:150,align:'left',sort:true},
                       {field:'KnifeNumber',title:'刀具标识号',type:'string',width:110,align:'left'},
                       {field:'Name',title:'名称',type:'string',width:110,align:'left'},
                       {field:'X',title:'X',type:'float',width:110,align:'left'},
                       {field:'Z',title:'Z',type:'float',width:110,align:'left'},
                       {field:'R',title:'R',type:'float',width:110,align:'left'},
                       {field:'A1',title:'A1',type:'float',width:110,align:'left'},
                       {field:'A2',title:'A2',type:'float',width:110,align:'left'},
                       {field:'Assemblyid',title:'Assemblyid',type:'int',width:80,align:'left'}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
