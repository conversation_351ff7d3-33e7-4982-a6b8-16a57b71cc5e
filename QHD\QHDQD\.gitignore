# ===========================================
# 前端专用 .gitignore
# 此文件确保前端开发者只看到前端相关代码
# ===========================================

# 忽略所有后端目录
../QHDHD/
../MESGeneticSchedulerAPI/
../QHDDB/
../OutPut/

# 前端构建输出
dist/
build/
.next/
.nuxt/

# 依赖
node_modules/
.pnp
.pnp.js

# 测试覆盖率
coverage/

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 编辑器
.vscode/
.idea/

# 操作系统
.DS_Store
Thumbs.db

# 临时文件
*.tmp
*.temp
