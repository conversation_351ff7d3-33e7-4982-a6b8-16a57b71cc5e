<template>
  <div>
    <!-- 二维码弹窗 -->
    <vol-box 
      v-model="model.qrCodeBox" 
      title="明细数据二维码" 
      :height="300"
      :width="300" 
      :padding="15">
      <div style="text-align: center; padding: 20px;">
        <img :src="model.qrCodeImage" alt="QR Code" style="width: 200px; height: 200px;"/>
        <p style="margin-top: 10px;">所有明细信息的二维码</p>
      </div>
      
      <template #footer>
        <div style="text-align: center">
          <el-button type="default" size="small" @click="close">关闭</el-button>
        </div>
      </template>
    </vol-box>
  </div>
</template>

<script>
import VolBox from '@/components/basic/VolBox.vue';

export default {
  components: { 
    'vol-box': VolBox
  },
  data() {
    return {
      model: {
        qrCodeBox: false,
        qrCodeImage: ''
      }
    };
  },
  methods: {
    close() {
      console.log('关闭二维码弹窗');
      this.model.qrCodeBox = false;
    },
    open(qrCodeImage) {
      try {
        console.log('打开二维码弹窗，图片数据：', qrCodeImage ? '存在' : '不存在');
        console.log('当前model状态：', JSON.parse(JSON.stringify(this.model)));
        this.model.qrCodeImage = qrCodeImage;
        this.model.qrCodeBox = true;
        console.log('弹窗状态已设置为true');
        this.$nextTick(() => {
          console.log('nextTick后的model状态：', JSON.parse(JSON.stringify(this.model)));
        });
      } catch (e) {
        console.error('二维码弹窗打开异常:', e);
        this.$message.error('弹窗初始化失败: ' + (e.message || '未知错误'));
        this.model.qrCodeBox = false;
      }
    }
  }
};
</script>