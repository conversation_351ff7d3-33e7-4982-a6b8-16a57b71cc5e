<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/warehouse/WMS_Warehouse.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/warehouse/WMS_Warehouse.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'Id',
                footer: "Foots",
                cnName: '库位管理',
                name: 'warehouse/WMS_Warehouse',
                url: "/WMS_Warehouse/",
                sortName: "Id"
            });
            const editFormFields = ref({"WarehouseName":"","WarehouseAddress":"","Status":"","IsValid":""});
            const editFormOptions = ref([[{"title":"仓库名称","required":true,"field":"WarehouseName"}],
                              [{"title":"仓库地址","field":"WarehouseAddress"}],
                              [{"dataKey":"库位状态","data":[],"title":"状态","field":"Status","type":"select"}],
                              [{"dataKey":"enable","data":[],"title":"是否启用","required":true,"field":"IsValid","type":"radio"}]]);
            const searchFormFields = ref({"WarehouseName":"","IsValid":"","Status":""});
            const searchFormOptions = ref([[{"title":"仓库名称","field":"WarehouseName"},{"dataKey":"enable","data":[],"title":"是否启用","field":"IsValid","type":"select"},{"dataKey":"库位状态","data":[],"title":"状态","field":"Status","type":"select"}]]);
            const columns = ref([{field:'WarehouseName',title:'仓库名称',type:'string',link:true,width:220,require:true,align:'left',sort:true},
                       {field:'Id',title:'ID',type:'int',width:110,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'Sguid',title:'SGUID;SGU',type:'string',width:220,hidden:true,align:'left'},
                       {field:'WarehouseAddress',title:'仓库地址',type:'string',width:220,align:'left'},
                       {field:'Tenant',title:'租户id',type:'string',width:220,hidden:true,align:'left'},
                       {field:'IsValid',title:'是否启用',type:'int',bind:{ key:'enable',data:[]},width:110,require:true,align:'left'},
                       {field:'Status',title:'状态',type:'int',bind:{ key:'库位状态',data:[]},width:110,align:'left'},
                       {field:'ParentId',title:'父id',type:'int',width:110,hidden:true,align:'left'},
                       {field:'Dataownership',title:'Dataownership',type:'string',width:220,hidden:true,align:'left'},
                       {field:'CreateID',title:'CreateID',type:'int',width:80,hidden:true,align:'left'},
                       {field:'Creator',title:'创建人',type:'string',width:130,align:'left'},
                       {field:'CreateDate',title:'创建时间',type:'datetime',width:150,align:'left',sort:true},
                       {field:'ModifyID',title:'ModifyID',type:'int',width:80,hidden:true,align:'left'},
                       {field:'Modifier',title:'更新人',type:'string',width:130,align:'left'},
                       {field:'ModifyDate',title:'更新时间',type:'datetime',width:150,align:'left',sort:true}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
