<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/warehouse/WMS_PutStock.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/warehouse/WMS_PutStock.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'WIID',
                footer: "Foots",
                cnName: '出库管理',
                name: 'warehouse/WMS_PutStock',
                url: "/WMS_PutStock/",
                sortName: "WIID"
            });
            const editFormFields = ref({"putno":"","type":"","Static":"","DeliveryOrg":"","DeliveryTime":"","InoutType":"","remark":""});
            const editFormOptions = ref([[{"title":"单据编号","field":"putno"},
                               {"dataKey":"DocumentsType","data":[],"title":"单据类型","field":"type","type":"select"}],
                              [{"dataKey":"出库状态","data":[],"title":"单据状态","field":"Static","type":"select"},
                               {"dataKey":"组织机构","data":[],"title":"发货组织","field":"DeliveryOrg","type":"treeSelect"}],
                              [{"title":"发货时间","field":"DeliveryTime","type":"datetime"},
                               {"title":"出入库类型","field":"InoutType"}],
                              [{"title":"备注","field":"remark","type":"textarea"}]]);
            const searchFormFields = ref({"putno":"","Static":"","DeliveryOrg":""});
            const searchFormOptions = ref([[{"title":"单据编号","field":"putno","type":"like"},{"dataKey":"出库状态","data":[],"title":"单据状态","field":"Static","type":"select"},{"dataKey":"组织机构","data":[],"title":"发货组织","field":"DeliveryOrg","type":"cascader"}]]);
            const columns = ref([{field:'putno',title:'单据编号',type:'string',link:true,width:220,align:'left',sort:true},
                       {field:'type',title:'单据类型',type:'int',bind:{ key:'DocumentsType',data:[]},width:80,align:'left'},
                       {field:'Static',title:'单据状态',type:'int',bind:{ key:'出库状态',data:[]},width:80,align:'left'},
                       {field:'DeliveryOrg',title:'发货组织',type:'string',bind:{ key:'组织机构',data:[]},width:220,align:'left'},
                       {field:'DeliveryTime',title:'发货时间',type:'datetime',width:150,align:'left',sort:true},
                       {field:'InoutType',title:'出入库类型',type:'string',width:220,align:'left'},
                       {field:'remark',title:'备注',type:'string',width:220,align:'left'},
                       {field:'WIID',title:'id',type:'int',width:80,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'Sguid',title:'SGUID',type:'string',width:220,hidden:true,align:'left'},
                       {field:'Tenant',title:'租户id',type:'string',width:220,hidden:true,align:'left'},
                       {field:'CreatedBy',title:'创建人',type:'int',width:80,align:'left'},
                       {field:'CreatedTime',title:'创建时间',type:'datetime',width:150,align:'left',sort:true},
                       {field:'UpdatedBy',title:'更新人',type:'int',width:80,align:'left'},
                       {field:'UpdatedTime',title:'更新时间',type:'datetime',width:150,align:'left',sort:true},
                       {field:'isQualified',title:'isQualified',type:'bool',width:110,align:'left'},
                       {field:'CustomerId',title:'CustomerId',type:'string',width:220,align:'left'},
                       {field:'Dataownership',title:'Dataownership',type:'string',width:220,align:'left'},
                       {field:'CreateID',title:'CreateID',type:'int',width:80,hidden:true,align:'left'},
                       {field:'Creator',title:'Creator',type:'string',width:130,align:'left'},
                       {field:'CreateDate',title:'CreateDate',type:'datetime',width:150,align:'left',sort:true},
                       {field:'ModifyID',title:'ModifyID',type:'int',width:80,hidden:true,align:'left'},
                       {field:'Modifier',title:'Modifier',type:'string',width:130,align:'left'},
                       {field:'ModifyDate',title:'ModifyDate',type:'datetime',width:150,align:'left',sort:true}]);
            const detail = ref({
                cnName: "出库管理明细表",
                table: "WMS_PutStockDetails",
                columns: [{field:'ID',title:'id',type:'int',width:80,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'SGUID',title:'SGUID',type:'string',width:220,hidden:true,align:'left'},
                       {field:'Tenant',title:'租户id',type:'string',width:220,hidden:true,align:'left'},
                       {field:'CreatedBy',title:'创建人',type:'int',width:80,hidden:true,align:'left'},
                       {field:'CreatedTime',title:'创建时间',type:'datetime',width:150,hidden:true,align:'left',sort:true},
                       {field:'UpdatedBy',title:'更新人',type:'int',width:80,hidden:true,align:'left'},
                       {field:'UpdatedTime',title:'更新时间',type:'datetime',width:150,hidden:true,align:'left',sort:true},
                       {field:'WIID',title:'出入库表主表',type:'int',width:80,hidden:true,align:'left'},
                       {field:'StuId',title:'物料id',type:'int',width:80,edit:{type:''},align:'left',sort:true},
                       {field:'Static',title:'状态',type:'string',width:220,edit:{type:''},align:'left'},
                       {field:'qty',title:'数量',type:'decimal',width:110,edit:{type:'decimal'},align:'left'},
                       {field:'pice',title:'单价',type:'decimal',width:110,edit:{type:''},align:'left'},
                       {field:'remark',title:'备注',type:'string',width:220,edit:{type:'textarea'},align:'left'},
                       {field:'CreateID',title:'CreateID',type:'int',width:80,hidden:true,align:'left'},
                       {field:'Creator',title:'Creator',type:'string',width:130,hidden:true,align:'left'},
                       {field:'CreateDate',title:'CreateDate',type:'datetime',width:150,hidden:true,align:'left',sort:true},
                       {field:'ModifyID',title:'ModifyID',type:'int',width:80,hidden:true,align:'left'},
                       {field:'Modifier',title:'Modifier',type:'string',width:130,hidden:true,align:'left'},
                       {field:'ModifyDate',title:'ModifyDate',type:'datetime',width:150,hidden:true,align:'left',sort:true}],
                sortName: "ID",
                key: "ID"
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
