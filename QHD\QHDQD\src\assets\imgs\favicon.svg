<?xml version="1.0" standalone="yes"?>
<svg xmlns="http://www.w3.org/2000/svg" width="1500" height="1500">
<path style="fill:#ffffff; stroke:none;" d="M0 0L0 1500L1500 1500L1500 0L0 0z"/>
<path style="fill:#d4fff1; stroke:none;" d="M736 98C740.72 99.9806 746.92 99 752 99L784 99C779.28 97.0194 773.08 98 768 98L736 98z"/>
<path style="fill:#edfff7; stroke:none;" d="M784 98C788.933 100.215 794.67 99.9994 800 100L800 98L784 98z"/>
<path style="fill:#e8f4ea; stroke:none;" d="M728 99C730.613 100.098 733.164 99.9928 736 100C733.387 98.9022 730.835 99.0072 728 99z"/>
<path style="fill:#a7dec1; stroke:none;" d="M736 99C738.613 100.098 741.164 99.9928 744 100C741.387 98.9023 738.835 99.0072 736 99z"/>
<path style="fill:#71ac8e; stroke:none;" d="M744 99C747.698 100.552 752.017 100 756 100L776 100C772.301 98.448 767.983 99 764 99L744 99z"/>
<path style="fill:#a2ddbf; stroke:none;" d="M776 99C778.613 100.098 781.164 99.9928 784 100C781.387 98.9023 778.836 99.0072 776 99z"/>
<path style="fill:#d8f4e5; stroke:none;" d="M784 99C786.052 99.8743 787.747 99.9528 790 100C787.948 99.1257 786.253 99.0472 784 99z"/>
<path style="fill:#cfffe7; stroke:none;" d="M702 100C705.959 101.661 710.737 101 715 101C711.041 99.3389 706.263 100 702 100z"/>
<path style="fill:#a0eac5; stroke:none;" d="M715 100C716.769 100.779 718.036 100.912 720 101C718.231 100.221 716.964 100.088 715 100z"/>
<path style="fill:#42bd84; stroke:none;" d="M720 100C721.769 100.779 723.036 100.912 725 101C723.231 100.221 721.964 100.088 720 100z"/>
<path style="fill:#149255; stroke:none;" d="M720 101C726.919 103.903 737.52 102 745 102L800 102C792.965 98.26 782.763 100 775 100C756.641 100 738.277 99.5574 720 101z"/>
<path style="fill:#57c892; stroke:none;" d="M796 100C797.506 100.683 798.315 100.826 800 101C798.494 100.317 797.685 100.174 796 100z"/>
<path style="fill:#79cea7; stroke:none;" d="M800 100C801.248 100.685 801.549 100.749 803 101C801.752 100.315 801.452 100.251 800 100z"/>
<path style="fill:#c0ffe5; stroke:none;" d="M803 100C805.89 101.213 808.874 100.998 812 101C809.11 99.7871 806.126 100.002 803 100z"/>
<path style="fill:#e6fff8; stroke:none;" d="M812 100C816.548 101.962 822.079 101.986 827 102L827 100L812 100z"/>
<path style="fill:#bbdeca; stroke:none;" d="M702.667 101.333C702.222 101.778 703.278 101.722 703.333 101.667C703.777 101.223 702.722 101.278 702.667 101.333z"/>
<path style="fill:#90c7a8; stroke:none;" d="M704.667 101.333C704.222 101.778 705.278 101.722 705.333 101.667C705.777 101.223 704.722 101.278 704.667 101.333z"/>
<path style="fill:#75ae8e; stroke:none;" d="M706 101C707.248 101.685 707.548 101.749 709 102C707.752 101.315 707.452 101.251 706 101z"/>
<path style="fill:#2f7954; stroke:none;" d="M709 101C712.432 102.44 716.3 102 720 102C716.568 100.56 712.7 101 709 101z"/>
<path style="fill:#2f845d; stroke:none;" d="M800 101C803.432 102.44 807.3 102 811 102C807.568 100.56 803.7 101 800 101z"/>
<path style="fill:#539675; stroke:none;" d="M811.667 101.333C811.222 101.778 812.278 101.722 812.333 101.667C812.777 101.223 811.722 101.278 811.667 101.333z"/>
<path style="fill:#6db08f; stroke:none;" d="M813 101C814.248 101.685 814.548 101.749 816 102C814.752 101.315 814.452 101.251 813 101z"/>
<path style="fill:#b0dac4; stroke:none;" d="M816 101C817.248 101.685 817.548 101.749 819 102C817.751 101.315 817.452 101.251 816 101z"/>
<path style="fill:#e2fff2; stroke:none;" d="M677 102L677 104L688 103C684.568 101.56 680.7 102 677 102z"/>
<path style="fill:#b3ffde; stroke:none;" d="M688 102C689.248 102.685 689.548 102.749 691 103C689.752 102.315 689.452 102.251 688 102z"/>
<path style="fill:#8deabb; stroke:none;" d="M691.667 102.333C691.222 102.778 692.278 102.722 692.333 102.667C692.777 102.223 691.722 102.278 691.667 102.333z"/>
<path style="fill:#6fd1a0; stroke:none;" d="M693 102C694.248 102.685 694.548 102.749 696 103C694.752 102.315 694.452 102.251 693 102z"/>
<path style="fill:#218c58; stroke:none;" d="M688 103C692.724 104.982 698.91 104 704 104C699.332 100.815 693.367 102.956 688 103z"/>
<path style="fill:#049b4e; stroke:none;" d="M702 102C702.65 103.3 702.147 103 704 103C696.032 105.355 687.219 103.384 679 104.17C665.942 105.418 653.083 109.883 640 110L640 128L616 128C610.996 128 606.846 128.64 604 124L608 124L608 122C603.934 122 599.126 121.299 595.17 122.313C592.197 123.075 590.164 125.442 586.996 125.778C577.784 126.754 568.336 124.761 560 130C561.248 130.685 561.548 130.749 563 131C557.452 132.644 546.198 131.748 544 138C533.439 138.036 524.388 143.692 514.714 147.237C512.24 148.143 509.549 148.009 507.09 148.991C504.426 150.055 502.366 152.192 499.7 153.239C483.419 159.635 468.158 167.801 452 175C451.435 180.003 447.315 179.952 443 180L444 182C438.446 182.289 433.021 183.967 432 190C426.054 190.792 421.972 195.338 416 196C414.391 200.382 406.91 202.237 407 206C403.298 206.041 400.459 206.765 397 208C395.817 211.794 393.12 213.292 389.885 215.445C383.221 219.881 375.798 223.153 369.285 227.787C365.418 230.539 360.923 232.94 361 238C355.407 238.182 353.217 240.786 348.91 243.961C343.133 248.22 335.056 252.723 333 260C326.986 260.928 320.99 265.861 320 272C314.592 272.79 311.554 276.155 307.69 279.748C302.055 284.988 296.458 290.37 291.17 295.961C287.877 299.442 284.325 302.307 283 307L287 303L288 306C286.062 306.736 283.921 307.436 286 309C283.894 309.644 282.205 309.906 280 310C278.38 312.955 276.151 313.359 273.799 315.565C265.24 323.59 256.452 330.979 255 343C253.814 341.814 253.416 341.472 252 341L251 344C252.434 345.393 253.045 346.233 254 348L250 350L250 346C246.127 347.314 244.888 350.143 244 354C242.813 352.964 242.397 352.661 241 352C238.084 357.069 234.016 361.195 230.72 365.996C229.24 368.151 228.695 370.878 227.122 372.911C220.691 381.218 210.626 388.395 214 400L208 398C207.545 402.07 207.949 405.967 207 410L206 406C201.387 407.366 199.73 410.267 200 415C197.031 417.012 196.132 419.932 193.907 422.676C189.318 428.337 184.645 434.107 181.927 441.001C180.672 444.182 180.717 447.764 179.426 450.87C178.322 453.525 175.624 454.896 174.184 457.339C169.406 465.447 166.974 474.709 162.726 482.907C156.464 494.99 148.133 503.669 147 518L144 518C143.769 522.44 141.72 525.202 140.502 529.285C139.68 532.036 140.207 535.157 140 538C138.977 536.466 139.195 536.598 138 536C134.145 541.438 134.83 547.03 132.454 552.94C126.632 567.417 120.787 582.501 117.218 598C116.147 602.65 116.677 607.424 115.676 612C115.026 614.972 112.942 617.238 112.139 620.134C110.103 627.479 111.083 634.715 109.681 642C108.74 646.887 106.08 651.264 105.139 656.018C102.275 670.482 103.473 685.426 101.921 699.982C101.657 702.46 100.35 704.589 100.079 707.06C98.8148 718.573 99 730.404 99 742C99 758.681 99.9616 775.407 100 792C100.008 795.286 100.272 807.543 103 808C100.902 816.81 102.439 825.249 103.7 834C105.279 844.96 106.562 856.011 108.065 867C108.674 871.457 109.959 875.446 110 880L128 880L128 896L123 896L123 900L122 896L120 896C120 900.65 118.748 906.204 119.549 910.714C120.285 914.86 122.417 918.876 123.402 923C124.182 926.264 123.819 929.667 124.708 932.911C125.798 936.883 127.77 939.752 128 944L144 944L144 948C137.362 949.153 138.493 956.637 143 960C141.436 965.392 138.574 970.4 138 976L144 976C143.35 982.15 141.755 990.05 143 996C145.6 994.907 147.286 995.447 150 996C150.378 1000.76 150.79 1006.18 152.311 1010.71C153.624 1014.63 156.418 1018.26 157.451 1022.17C158.546 1026.31 157.128 1030.69 158.435 1034.99C159.461 1038.35 161.898 1040.84 163.33 1044C167.739 1053.72 171.769 1063.71 177 1073L180 1072C182.442 1080.96 185.792 1096.44 196 1099L196 1106C200.049 1109.64 201.929 1114.57 204.503 1119.27C206.019 1122.04 208.406 1123.73 210.162 1126.26C212.02 1128.93 212.624 1132.26 214.479 1134.98C217.945 1140.07 223.472 1143.41 227.251 1148.29C230.99 1153.11 232.608 1160.21 239 1162C239.192 1169.96 243.537 1172.45 247.972 1178.26C251.043 1182.28 253.523 1186.64 257.015 1190.37C258.891 1192.37 261.468 1193.54 263.281 1195.54C266.418 1199.01 267.616 1203.41 271.576 1206.4C273.475 1207.83 275.864 1208.18 277.815 1209.51C282.124 1212.44 282.864 1218.23 288 1220L288 1224C294.935 1225.81 296.799 1233.97 304 1236C303.014 1237.48 303 1237.2 303 1239C306.969 1240.89 308.056 1243.79 311.274 1246.42C313.189 1247.98 315.784 1248.31 317.687 1249.89C320.121 1251.91 320.874 1255.3 323.147 1257.56C326.322 1260.71 330.172 1262.34 333.625 1264.96C336.4 1267.06 338.283 1270.33 341.019 1272.6C345.333 1276.19 350.263 1278.98 354.545 1282.54C357.684 1285.16 360.247 1288.53 363.961 1290.43C366.846 1291.91 370.127 1289.67 372.697 1291.66C374.946 1293.4 374.424 1296.35 376.023 1298.42C377.207 1299.94 379.299 1300.32 380.876 1301.29C385.522 1304.17 388.616 1306.32 394 1308C392.964 1309.19 392.661 1309.6 392 1311C395.992 1312.07 399.901 1313.5 404 1314C402.466 1315.02 402.598 1314.8 402 1316L408 1319L407 1320C410.515 1320.43 413.49 1321.61 417 1321C418.081 1323.12 418.879 1323.92 421 1325L420 1328C423.068 1329.48 425.602 1329.92 429 1330L428 1332C432.981 1333.17 436.02 1336.75 440.428 1339.03C442.705 1340.21 445.527 1340.4 448 1341C447.014 1342.48 447 1342.2 447 1344C449.238 1344.37 448.825 1345.33 450 1346C452.242 1347.28 455.724 1346.86 458 1348L459 1350C463.756 1350.79 463.93 1351.61 467 1354C468.924 1355.5 471.499 1355.64 474 1356L475 1358C475.775 1358.65 478.078 1357.54 479 1358L480 1360C480.903 1360.15 483.281 1359.49 484 1360L485 1362C488.112 1363.04 488.42 1363.49 492 1364L493 1366C494.539 1366.86 497.42 1365.21 499 1366L500 1368C503.589 1368.6 505.12 1368.7 508 1371L507 1372C509.928 1371.81 512.256 1370.98 515 1370C515.295 1374.84 518.642 1375.38 522.573 1377.05C529.113 1379.84 535.057 1382.17 541.87 1384.4C545.153 1385.47 548.601 1385.47 551.816 1386.51C554.655 1387.43 556.84 1389.8 559.616 1390.95C564.023 1392.77 569.22 1393.15 573.83 1394.44C578.373 1395.7 582.492 1398.37 587.015 1399.47C590.297 1400.26 593.728 1399.65 596.996 1400.47C599.913 1401.21 602.269 1403.3 605.17 1403.99C607.721 1404.6 610.342 1403.83 612.911 1404.28C615.828 1404.78 618.162 1406.8 621.015 1407.53C624.509 1408.42 628.421 1408 632 1408L631 1410C639.462 1410.03 647.609 1413.33 656 1414.57C665.35 1415.96 674.67 1415.04 683.961 1416.13C692.512 1417.13 701.377 1419.85 710.004 1420C728.657 1420.31 747.344 1420 766 1420C776.101 1420 786.883 1421.08 796.911 1419.87C805.544 1418.83 814.267 1416.8 823 1416.09C829.631 1415.56 836.389 1416.61 843 1415.82C851.988 1414.74 859.755 1412 869 1412C867.752 1411.32 867.451 1411.25 866 1411C873.887 1408.33 882.779 1408.87 891 1407.7C896.906 1406.86 901.552 1403.47 907.089 1402.28C912.008 1401.22 917.015 1401.94 921.996 1400.62C926.909 1399.33 931.1 1396.47 936 1395.02C948.452 1391.32 960.513 1388.74 973 1385.28C978.391 1383.78 983.478 1380.72 989 1380L988 1378C993.574 1377.41 997.116 1373.4 1002.06 1372.22C1004.72 1371.58 1008.49 1373.07 1009 1370L1012 1371C1018.52 1362.09 1029.88 1362.61 1039 1358.92C1042.08 1357.67 1048.08 1355.4 1048 1352L1054 1352C1053.35 1350.7 1053.85 1351 1052 1351C1064.07 1346.41 1075.73 1340.33 1087 1333.85C1091.2 1331.43 1097.36 1328.76 1099 1324C1102.74 1323.75 1105.6 1322.51 1109 1321C1109.48 1317.31 1113.48 1318.26 1113 1314C1116.43 1313.96 1118.94 1313.59 1122 1312L1122 1310C1120.26 1307.03 1122.11 1306.06 1125 1305C1123.96 1306.19 1123.66 1306.6 1123 1308C1133.66 1307.68 1136.04 1296.65 1146 1296C1149.36 1289.85 1157.73 1290.06 1159 1282C1165.92 1281.54 1170.14 1276.88 1174.86 1272.24C1177.18 1269.97 1179.36 1269.25 1180 1266C1182.36 1265.74 1188.53 1263.84 1186 1261C1191.65 1258.91 1196.78 1255.63 1200.69 1250.91C1203.54 1247.46 1204.43 1243.39 1208.17 1240.53C1210.89 1238.46 1214.29 1237.77 1217 1235.69C1232.4 1223.88 1245.41 1206.98 1258.13 1192.42C1263.87 1185.85 1270.52 1179.33 1274.66 1171.58C1275.73 1169.57 1275.93 1167.27 1277 1165.28C1278.45 1162.63 1280.16 1160.97 1281 1158C1288.94 1157.4 1295.42 1147.59 1296 1140L1299 1140L1299 1136C1306.16 1134.79 1307.79 1127.2 1309 1121L1306 1122C1308.29 1120.57 1309.46 1121.1 1312 1122C1312.7 1120.82 1313.54 1119.61 1314.17 1118.39C1317.29 1112.41 1310.58 1111.93 1308.31 1107.7C1307.18 1105.58 1307.19 1103.18 1305.83 1101.09C1301.61 1094.61 1295.19 1089.9 1290.41 1083.96C1288.12 1081.11 1286.65 1077.81 1284.11 1075.11C1280.91 1071.71 1276.68 1070.44 1273.87 1066.57C1268.61 1059.29 1265.08 1047.56 1255 1046C1253.78 1034.17 1241.68 1028.06 1234.68 1019.85C1228.19 1012.24 1224.37 1002.94 1216 997C1212.7 999.008 1211.53 992.267 1210.44 990.023C1209.06 987.2 1206.63 986.651 1204.59 984.565C1200.73 980.623 1201.33 975.525 1195 974L1196 972L1190 968C1188.97 961.344 1180.8 954.018 1176.41 949.018C1174.34 946.656 1171.75 944.871 1169.68 942.545C1166.25 938.678 1164.07 933.94 1160.25 930.299C1155.84 926.092 1150.32 923.237 1146.69 918.102C1140.51 909.34 1134.27 895.095 1123 892L1123 888C1120.26 887.019 1117.93 886.195 1115 886C1115.99 876.887 1110.98 873.932 1105.54 867.781C1102.4 864.222 1100.2 860.509 1096 858C1095.37 853.047 1091.79 850.35 1088.59 846.729C1083.43 840.876 1078.17 835.773 1072.67 830.326C1069.38 827.067 1067.82 822.829 1063 822C1061.82 817.151 1059.09 813.764 1054 816L1055 810C1049.27 806.737 1050.25 799.809 1043 798L1042 792C1039.83 791.536 1038.04 790.895 1036 790C1036 812.995 1036.55 836.07 1034.95 858.985C1034.64 863.362 1032.84 867.595 1032.28 872C1030.62 885.307 1029.95 897.651 1031 911L1030 907C1025.71 911.15 1026.52 916.479 1025.7 922C1025 926.675 1023.38 931.399 1022.26 936C1020.48 943.281 1019.46 950.716 1017.77 958C1016.96 961.441 1015.16 964.511 1014.47 968C1013.35 973.645 1013.23 980.632 1011.27 985.961C1009.97 989.508 1007.42 992.336 1006.17 996.004C1004.04 1002.25 1003.42 1008.73 1001.27 1015C999.436 1020.33 996.22 1025.26 997 1031C992.246 1033.67 990.693 1038.76 990 1044C983.769 1046.18 982.794 1055.51 980.571 1060.91C979.585 1063.3 977.517 1064.96 976.458 1067.31C971.484 1078.35 966.877 1091.25 957 1099C957.215 1102.03 956.868 1102.95 954 1104C952.768 1110.37 948.518 1114.27 944.785 1119.29C936.277 1130.72 926.243 1142.03 916.385 1152.42C911.514 1157.56 900.889 1164.55 901 1172C895.425 1172.44 892.085 1176.35 888 1179.87C880.366 1186.44 873.325 1195.86 864 1200C864.013 1182.84 869.738 1166.11 870.911 1149C872.052 1132.34 871.92 1115.71 872.001 1099C872.049 1089.06 872.781 1078.95 871.83 1069C870.999 1060.3 867.345 1052.62 866.286 1044C865.887 1040.75 866.324 1037.39 865.741 1034.17C864.516 1027.4 861.518 1021.39 859.545 1014.91C858.225 1010.58 858.634 1006.87 856.374 1002.71C854.242 998.797 854.549 993.599 850 992C849.895 987.509 848.441 983.428 848 979L846 982C843.348 973.638 840.748 965.281 837.575 957.089C835.849 952.634 833.085 948.676 832 944L828 944C827.279 937.02 820.716 932.902 821 926C818.507 923.353 817.884 920.246 816.323 917C812.919 909.92 808.198 900.876 802 896C802.389 890.132 798.045 889.342 795.565 884.857C793.449 881.03 793.88 876.018 789 875C788.684 869.152 786.556 863.79 780 864C778.552 856.684 775.05 850.151 771.012 843.975C768.593 840.275 766.54 836.74 762 836C764.423 828.133 760.498 823.387 756.59 816.714C754.229 812.682 752.94 808.847 748 808C747.855 797.401 736.693 788.543 736 778L732 778C730.855 763.516 722.408 751.117 716.905 738C711.372 724.813 707.403 710.751 703.586 697.089C702.963 694.856 701.444 693.084 700.698 690.911C699.82 688.352 700.167 685.618 699.61 683C698.057 675.696 696.005 669.636 696 662L693 662C693 640.332 691.089 618.703 692.039 597C692.62 583.745 695.253 571.13 696.845 558C698.055 548.024 699.788 538.44 703.035 529C704.676 524.229 704.115 520.708 708 517C706.966 513.32 706.167 509.827 706 506L702 506L701 494L696 492C696.273 494.124 697.393 496.678 696.976 498.804C695.994 503.805 691.172 507.573 689 512C688.356 509.894 688.094 508.205 688 506C685.158 509.447 684.819 512.87 683.506 516.985C681.896 522.033 679.519 526.864 677.886 532C676.42 536.608 676.478 541.393 672 544C671.899 553.148 666.199 560.225 664.616 569C663.957 572.649 664.302 576.372 663.625 580C662.689 585.016 660.736 590.051 660.129 595.089C659.089 603.721 660.379 610.728 657 619C658.797 619 658.522 619.014 660 620C658.814 621.186 658.472 621.584 658 623C662.017 623.956 665.887 624 670 624L670 608L672 608C672 632.61 672.887 657.404 671.982 681.996C671.841 685.81 670.037 689.188 670.037 693C670.037 698.666 672 704.142 672 710L670 710C669.782 702.615 668 695.588 668 688L662 688C662 692.969 662.605 698.089 662.5 703C662.457 704.999 661.566 707.222 660 706C658.385 708.895 660.519 710.156 661.5 713.028C662.254 715.234 661.979 717.711 662 720C662.093 730.054 666.637 737.835 669.528 747C673.087 758.285 676.738 768.947 681.139 780C683.463 785.838 683.711 792.271 686.25 798C687.461 800.733 688.03 804.049 691 802C694.657 819.484 704.038 835.128 711.895 851C714.218 855.693 717.629 865.833 723 867C723.612 872.313 726.433 873.772 728.7 878.188C730.971 882.613 731.954 887.6 734.392 891.946C737.365 897.245 741.511 901.891 744.577 907.114C749.983 916.322 753.093 926.695 758.427 936C760.794 940.128 763.931 943.609 766.425 947.615C768.476 950.909 769.117 953.706 773 955C771.597 960.964 772.406 967.938 779 970L778 976L782 976C782.552 981.358 785.708 983.58 787.747 988.17C788.859 990.675 787.826 993.252 788.341 995.83C788.913 998.689 791.065 1001.21 792 1004L798 1002C797.997 1005.73 798.303 1008.96 796 1012C796.92 1016.89 798.317 1021.4 799.542 1026.17C800.526 1030 799.808 1032.61 804 1034C804.001 1039.91 805.811 1044.34 806.696 1050C807.203 1053.25 806.606 1056.62 807.274 1059.83C809.417 1070.13 812.809 1080.31 813.83 1091C815.052 1103.79 814 1117.15 814 1130C814 1140.92 814.708 1152.1 813.946 1162.99C813.726 1166.12 812.278 1168.91 812.054 1172.04C811.521 1179.5 812.233 1187.67 810.701 1195C810.048 1198.13 808.256 1200.92 807.393 1204C805.815 1209.64 805.488 1215.48 803.434 1221.01C800.06 1230.11 795.224 1239.36 790.777 1248C782.755 1263.59 773.415 1277.27 761.169 1289.95C757.656 1293.58 750.458 1301.67 745 1301.67C742.447 1301.68 740.167 1299.47 738.174 1298.12C731.622 1293.71 728.742 1286.67 721 1284C724.002 1279.33 728.392 1275.23 730.449 1270C731.667 1266.9 730.623 1263.05 732.12 1260.21C733.687 1257.25 737.309 1256.07 738.847 1252.74C740.126 1249.97 739.852 1246.92 740.472 1244C742.098 1236.34 743.19 1228.88 742.995 1221C742.863 1215.65 741.632 1208.1 739.94 1203C739.106 1200.49 738.522 1198.37 736 1198L736 1192C732.658 1190.1 731.677 1187.73 731 1184L727 1184C728.839 1176.16 724.499 1171.94 720 1166C716.501 1167.17 714.741 1167.6 712 1165C713.478 1165.99 713.203 1166 715 1166C714.438 1158.48 708.573 1155.25 704 1150L699 1150C698.165 1144.75 692.953 1141.36 688 1140C687.09 1134.57 682.323 1131.04 678 1128L678 1130C674.758 1125.93 672.543 1121.51 667 1121C663.585 1114.42 657.527 1110.31 652 1105.57C645.747 1100.21 640.779 1095.09 633 1092C633.986 1090.52 634 1090.8 634 1089C630.632 1087.06 627.88 1084.64 624 1084C624.177 1079.04 621.558 1078.15 617 1078C616.57 1074.26 615.814 1072.66 612 1072C611.676 1069.26 610.633 1068.74 608 1068C607.42 1064.57 606.482 1063.34 603 1063C601.527 1053.73 592.149 1046.78 586.17 1040C581.675 1034.91 579.277 1030.22 572 1030C571.048 1025.45 569.771 1021.35 565 1020C566.712 1017.02 565.786 1015.8 563 1014C562.771 1009 559.375 1008.19 555 1007C556.251 1002.59 555.162 999.931 553 996C551.018 996.465 550.594 996.703 549 998C548.754 995.879 548.77 994.126 549 992C543.7 987.225 542.155 979.167 538.576 973C530.856 959.698 522.609 946.421 515.552 932.911C513.486 928.953 512.935 924.475 511.201 920.424C508.503 914.118 506.833 907.537 504.204 901.174C502.329 896.636 499.653 892.773 498.156 888C497.022 884.386 497.106 880.596 496.008 877C494.432 871.842 491.719 867.145 490.455 861.83C489.766 858.932 490.17 855.915 489.468 853.015C488.774 850.152 486.861 847.788 486.274 844.911C485.285 840.063 485.66 834.914 484.686 830C483.547 824.253 481.274 818.687 480.259 812.911C479.571 808.993 480.349 804.95 479.812 801.015C479.221 796.69 476.077 793.368 475.522 789.014C475.043 785.248 477.131 781.843 476.826 777.996C475.638 763.043 476 748.021 476 733C476 727.077 474.843 720.841 475.213 715C475.447 711.294 476.696 707.718 477 704L480 704C480 696.199 481.706 689.617 482.711 682C483.544 675.678 483.441 669.258 484.889 663.001C487.228 652.893 491.542 644.677 489 634L491 635C491 633.203 490.986 633.479 490 632C492.727 629.925 492.846 627.323 493 624L496 624C496.004 618.417 497.455 613.47 498 608C503.349 606.437 503.279 598.727 504.065 594C504.595 590.812 507.076 588.881 507.782 585.866C508.523 582.705 507.373 579.145 507 576L512 576C512.585 571.531 514.924 568.677 516.801 564.714C518.996 560.081 520.779 554.803 522.644 550.004C523.807 547.01 522.007 542.972 523.718 540.515C525.169 538.43 527.832 537.558 529.327 535.446C532.625 530.787 533.905 524.97 536.799 520.17C540.468 514.085 543.973 511.498 544 504C551.892 503.378 553.355 495.892 556.905 490.039C559.194 486.265 561.92 481.764 564.546 478.259C566.487 475.668 569.663 474.198 570.792 470.983C571.542 468.847 571.017 466.221 571 464L575 466C575.722 463.582 575.948 461.528 576 459C578.201 459.481 578.799 459.481 581 459L579 452C582.434 451.962 584.94 451.592 588 450L588 448L586 448C586.925 445.66 587.228 444.47 587 442C593.285 441.978 597.924 440.945 599 434C603.049 432.661 604.702 428.075 605 424C610.454 425.9 614.843 422.959 614 417C620.406 413.847 627.178 409.909 632.316 404.83C636.391 400.803 636.204 395.883 643 396C645.825 392.386 651.152 390.566 652 386C654.33 385.684 655.918 385.098 658 384L658 382C656.733 380.457 656.511 379.952 656 378C661.172 379.844 665.113 380.502 668 375L664 374C665.218 371.882 665.867 371.155 668 370L668 374C672.941 373.169 675.846 368.936 681 368L680 365C682.414 364.416 685.381 364.289 687.545 363.016C689.568 361.826 690.866 359.857 693.053 358.842C696.348 357.313 698.692 357.721 700 354C709.301 352.763 717.984 346.427 726 341.912C730.323 339.477 734.988 337.986 739 335C737.751 334.315 737.452 334.251 736 334L736 330L740 330L739 332L748 332L747 328C751.514 328 761.152 328.355 761 322C766.233 321.942 776.161 321.54 778 316C784.428 315.809 789.433 312.073 796 312L795 310L807 308L806 306L817 306L816 304C820.422 303.997 824.623 303.553 829 303C829 301.204 828.985 301.478 828 300C834.945 300 841.174 298.919 848 298L848 294L859 294L858 292C863.403 292.045 867.619 292.686 872 296C870.733 297.543 870.511 298.048 870 300C857.248 300.413 857.132 315.018 848 320L848 326C843.792 327.226 842.618 331.966 842 336C835.491 337.119 834.141 344.545 831.711 349.83C825.468 363.413 820 374.747 820 390C813.226 392.248 814.814 407.782 812.911 414C810.398 422.21 811 430.469 811 439C811 449.224 810.2 459.821 811.17 470C812.318 482.057 815.991 493.878 816 506L820 506C820 512.755 820.179 519.428 822.684 525.83C824.043 529.306 826.703 532.065 827.763 535.714C832.183 550.936 837.201 565.128 844.828 578.985C846.698 582.383 849.893 584.689 851.924 587.95C855.461 593.631 858.177 599.463 862.34 604.871C868.85 613.326 876.298 621.593 884 628.981C886.915 631.777 892.386 633.843 893 638C899.628 640.459 904.118 647.516 911 648C911.52 652.19 916.694 652.632 919.581 655.383C923.408 659.031 927.258 660.22 931.714 662.8C940.156 667.687 947.418 672.824 956.911 676.046C969.561 680.34 981.745 685.148 995 687.625C1003.21 689.159 1011.68 689.157 1019.96 690.129C1030.62 691.381 1041.01 692.728 1051.96 691.946C1054.78 691.745 1057.26 690.455 1060.04 690.129C1070.35 688.918 1080.61 689.316 1090.91 687.867C1093.78 687.463 1095.53 685.359 1098.18 684.509C1101.51 683.447 1105.55 684 1109 684C1108.35 682.7 1108.85 683 1107 683C1113.06 680.183 1118.42 680 1125 680L1124 678C1132.08 676.93 1139.2 673.1 1147 671L1146 669C1151.75 667.779 1158.94 668.588 1161 662L1157 662L1157 658C1161.2 658 1162.43 658.433 1165 661L1164 662C1167.89 660.891 1177.24 655.775 1178 652C1182.55 651.66 1185.37 649.645 1189 647C1187.49 646.317 1186.69 646.174 1185 646C1186.65 644.487 1187.89 643.819 1190 643C1188.89 638.269 1185.18 628.648 1190.43 625.028C1192.93 623.306 1197.13 624 1200 624L1200 608L1216 608L1216 592C1227.17 592.766 1220.07 601.6 1220 609L1224 609L1219 611L1220 616C1226.74 614.106 1230.98 607.326 1235.12 602C1244.95 589.351 1259.94 572.866 1260 556C1264.71 554.348 1265.55 548.557 1266 544C1270.17 542.534 1269.72 538.925 1271.46 535.286C1275.97 525.863 1277.44 516.27 1277 506L1278 508C1282.03 500.422 1278.83 491.227 1280.26 483.089C1282.58 469.868 1282 456.516 1282 443C1282 435.599 1283.02 427.341 1281.78 420.039C1280.84 414.574 1277.54 409.336 1276.62 403.87C1276.13 400.93 1277.77 398.068 1277.39 394.985C1276.5 387.77 1272.99 381.671 1270.67 374.911C1267.49 365.664 1264.48 356.966 1260.48 347.961C1258.43 343.33 1256.05 337.667 1251 336L1252 332C1249.14 330.505 1247.27 328.548 1244 328C1243.74 315.906 1234.68 310.116 1227.75 301.701C1225.86 299.405 1225 296.438 1223.31 294.004C1221.01 290.693 1217.82 288.052 1215.29 284.911C1211.37 280.042 1206.45 273.039 1200 272C1199.05 265.767 1193.27 260.489 1187 260C1187.99 258.521 1188 258.797 1188 257C1180.84 252.301 1177.09 246.698 1168 245L1170 241C1167.27 240.19 1164.85 240.021 1162 240L1163 237C1160.1 236.263 1157.95 236.471 1155 237C1155.68 235.42 1155.8 235.223 1157 234L1157 232C1155.72 230.663 1154.97 229.6 1154 228C1152.69 228.768 1152.77 228.686 1152 230C1150.33 228.589 1149.03 227.825 1147 227C1146.63 218.497 1137.06 217.631 1131 214.606C1124.54 211.383 1120.04 206.28 1113 204C1114.09 201.283 1114.09 203.086 1112 201L1113 200L1109 200C1106.04 190.517 1090.92 189.516 1083.27 185.006C1079.11 182.553 1077.07 180.277 1072 180C1071.54 171.264 1056.57 173.29 1051 174L1052 168C1040.96 163.41 1032.62 154.269 1020 154C1019.3 148.678 1008.48 148.003 1004 148C1003.19 144.206 999.436 143.488 996 142.134C988.196 139.056 979.668 138.059 972.039 135.161C968.01 133.631 963.64 129.835 960 134C957.494 131.944 953.84 131.29 953 128C947.374 128 939.38 129.363 936 124C931.074 124.395 929.319 121.033 924.896 120.129C919.303 118.986 913.65 116.995 908 116.209C901.836 115.352 895.218 116 889 116C890.207 112.982 887.105 113.724 890 113C887.449 111.929 884.857 112.183 882.17 111.545C873.239 109.422 863.993 107.224 854.911 106.129C850.651 105.616 846.301 106.371 842.039 105.871C839.579 105.582 837.367 104.425 834.911 104.129C829.992 103.536 824.96 104.449 820.039 103.871C817.258 103.545 814.776 102.255 811.961 102.054C799.768 101.183 787.225 102 775 102L702 102z"/>
<path style="fill:#278757; stroke:none;" d="M816 102L816 104L832 104C827.252 101.989 821.138 102.014 816 102z"/>
<path style="fill:#52b886; stroke:none;" d="M824 102C825.248 102.685 825.548 102.749 827 103C825.752 102.315 825.452 102.251 824 102z"/>
<path style="fill:#8ceebd; stroke:none;" d="M827.667 102.333C827.222 102.778 828.278 102.722 828.333 102.667C828.778 102.223 827.722 102.278 827.667 102.333z"/>
<path style="fill:#aaffda; stroke:none;" d="M829 102C830.248 102.685 830.548 102.749 832 103C830.752 102.315 830.452 102.251 829 102z"/>
<path style="fill:#a9d0bd; stroke:none;" d="M682 103C683.248 103.685 683.548 103.749 685 104C683.752 103.315 683.452 103.251 682 103z"/>
<path style="fill:#608d78; stroke:none;" d="M685 103C686.248 103.685 686.548 103.749 688 104C686.752 103.315 686.452 103.251 685 103z"/>
<path style="fill:#4f7a66; stroke:none;" d="M832 103C833.248 103.685 833.548 103.749 835 104C833.752 103.315 833.452 103.251 832 103z"/>
<path style="fill:#96bdaa; stroke:none;" d="M835.667 103.333C835.222 103.778 836.278 103.722 836.333 103.667C836.778 103.223 835.722 103.278 835.667 103.333z"/>
<path style="fill:#c5e6d5; stroke:none;" d="M837 103C838.248 103.685 838.549 103.749 840 104C838.751 103.315 838.452 103.251 837 103z"/>
<path style="fill:#dffff4; stroke:none;" d="M661 105C664.494 106.364 668.293 105.159 672 105C668.418 103.497 664.671 103.915 661 105z"/>
<path style="fill:#98ffca; stroke:none;" d="M672 104C673.248 104.685 673.548 104.749 675 105C673.752 104.315 673.452 104.251 672 104z"/>
<path style="fill:#50c286; stroke:none;" d="M675.667 104.333C675.222 104.778 676.278 104.722 676.333 104.667C676.777 104.223 675.722 104.278 675.667 104.333z"/>
<path style="fill:#1e8e53; stroke:none;" d="M838 104L838 106L851 106C847.133 104.362 842.207 104.181 838 104z"/>
<path style="fill:#5ece93; stroke:none;" d="M843.667 104.333C843.222 104.778 844.278 104.722 844.333 104.667C844.777 104.223 843.722 104.278 843.667 104.333z"/>
<path style="fill:#8cf9bf; stroke:none;" d="M845 104C846.248 104.685 846.549 104.749 848 105C846.752 104.315 846.452 104.251 845 104z"/>
<path style="fill:#96cbb1; stroke:none;" d="M666.667 105.333C666.222 105.778 667.278 105.722 667.333 105.667C667.777 105.223 666.722 105.278 666.667 105.333z"/>
<path style="fill:#5c987c; stroke:none;" d="M668.667 105.333C668.222 105.778 669.278 105.722 669.333 105.667C669.777 105.223 668.722 105.278 668.667 105.333z"/>
<path style="fill:#87c2a6; stroke:none;" d="M851.667 105.333C851.222 105.778 852.278 105.722 852.333 105.667C852.777 105.223 851.722 105.278 851.667 105.333z"/>
<path style="fill:#bfecd5; stroke:none;" d="M853 105C854.248 105.685 854.548 105.749 856 106C854.752 105.315 854.452 105.251 853 105z"/>
<path style="fill:#d5fffa; stroke:none;" d="M649 106L649 108L659 107C655.837 105.673 652.414 106 649 106z"/>
<path style="fill:#70cd9e; stroke:none;" d="M659.667 106.333C659.222 106.778 660.278 106.722 660.333 106.667C660.777 106.223 659.722 106.278 659.667 106.333z"/>
<path style="fill:#358b5e; stroke:none;" d="M655 107C657.852 108.188 660.06 107.865 663 107C660.384 106.371 657.717 106.89 655 107z"/>
<path style="fill:#70c99b; stroke:none;" d="M858.667 106.333C858.222 106.778 859.278 106.722 859.333 106.667C859.777 106.223 858.722 106.278 858.667 106.333z"/>
<path style="fill:#d5fff5; stroke:none;" d="M860 106C863.367 107.453 867.337 107.711 871 108L871 106L860 106z"/>
<path style="fill:#6aac90; stroke:none;" d="M653.667 107.333C653.222 107.778 654.278 107.722 654.333 107.667C654.778 107.223 653.722 107.278 653.667 107.333z"/>
<path style="fill:#36895d; stroke:none;" d="M858 107C860.331 107.984 862.457 107.981 865 108C862.668 107.015 860.543 107.019 858 107z"/>
<path style="fill:#90d3b2; stroke:none;" d="M865.667 107.333C865.222 107.778 866.278 107.722 866.333 107.667C866.777 107.223 865.722 107.278 865.667 107.333z"/>
<path style="fill:#daffee; stroke:none;" d="M632 109C636.461 110.871 641.321 109.369 646 109C641.683 107.188 636.473 107.935 632 109z"/>
<path style="fill:#73cfa0; stroke:none;" d="M646.667 108.333C646.222 108.778 647.278 108.722 647.333 108.667C647.778 108.222 646.722 108.278 646.667 108.333z"/>
<path style="fill:#3aa672; stroke:none;" d="M648.667 108.333C648.222 108.778 649.278 108.722 649.333 108.667C649.778 108.222 648.722 108.278 648.667 108.333z"/>
<path style="fill:#53b785; stroke:none;" d="M870.667 108.333C870.222 108.778 871.278 108.722 871.333 108.667C871.778 108.223 870.722 108.278 870.667 108.333z"/>
<path style="fill:#a3f6ca; stroke:none;" d="M872.667 108.333C872.222 108.778 873.278 108.722 873.333 108.667C873.778 108.222 872.722 108.278 872.667 108.333z"/>
<path style="fill:#defff2; stroke:none;" d="M874 108C878.629 110.461 884.813 110.591 890 111C885.07 107.902 879.65 108.001 874 108z"/>
<path style="fill:#99cfad; stroke:none;" d="M641 109L642 110L641 109z"/>
<path style="fill:#347f56; stroke:none;" d="M642 109C644.052 109.874 645.747 109.953 648 110C645.948 109.126 644.253 109.047 642 109z"/>
<path style="fill:#307c55; stroke:none;" d="M871 109C873.052 109.874 874.747 109.953 877 110C874.948 109.126 873.253 109.047 871 109z"/>
<path style="fill:#7db494; stroke:none;" d="M877 109L878 110L877 109z"/>
<path style="fill:#c6fce5; stroke:none;" d="M630 110C631.769 110.779 633.036 110.912 635 111C633.231 110.221 631.964 110.088 630 110z"/>
<path style="fill:#61c498; stroke:none;" d="M635.667 110.333C635.222 110.778 636.278 110.722 636.333 110.667C636.777 110.223 635.722 110.278 635.667 110.333z"/>
<path style="fill:#2e8a63; stroke:none;" d="M632 111C634.92 112.223 637.837 112.476 640 110L632 111z"/>
<path style="fill:#307d59; stroke:none;" d="M880 110C881.925 112.208 884.125 111.968 887 112C884.749 110.911 882.465 110.481 880 110z"/>
<path style="fill:#5fba8f; stroke:none;" d="M882 110L883 111L882 110z"/>
<path style="fill:#aefbd7; stroke:none;" d="M883 110C884.248 110.685 884.548 110.749 886 111C884.752 110.315 884.452 110.251 883 110z"/>
<path style="fill:#9dcab6; stroke:none;" d="M629 111C630.248 111.685 630.549 111.749 632 112C630.752 111.315 630.452 111.251 629 111z"/>
<path style="fill:#689880; stroke:none;" d="M887 111L888 112L887 111z"/>
<path style="fill:#c1dccd; stroke:none;" d="M888.667 111.333C888.222 111.778 889.278 111.722 889.333 111.667C889.777 111.223 888.722 111.278 888.667 111.333z"/>
<path style="fill:#e8ffee; stroke:none;" d="M614 113C617.249 114.268 620.568 113.271 624 113C620.65 111.597 617.479 112.172 614 113z"/>
<path style="fill:#61e09d; stroke:none;" d="M624.667 112.333C624.222 112.778 625.278 112.722 625.333 112.667C625.777 112.223 624.722 112.278 624.667 112.333z"/>
<path style="fill:#039b50; stroke:none;" d="M626 112C627.248 112.685 627.548 112.749 629 113C622.26 116.533 615.413 116 608 116C609.506 116.683 610.315 116.826 612 117C608.699 117.976 605.516 117.653 602.17 118.259C596.727 119.246 591.55 121.64 586 122C586.598 123.196 586.466 122.977 588 124L584 125C589.122 127.124 591.42 123.476 596.089 122.313C599.793 121.391 604.202 122 608 122L608 124L604 124C605.851 128.604 609.532 127.998 614 128L640 128L640 112L626 112z"/>
<path style="fill:#189553; stroke:none;" d="M888 113C889.364 114.819 889.492 114.031 889 116L903 116C898.99 112.844 892.964 112.213 888 113z"/>
<path style="fill:#67d89a; stroke:none;" d="M892.667 112.333C892.222 112.778 893.278 112.722 893.333 112.667C893.777 112.223 892.722 112.278 892.667 112.333z"/>
<path style="fill:#a5ffd2; stroke:none;" d="M894.667 112.333C894.222 112.778 895.278 112.722 895.333 112.667C895.777 112.223 894.722 112.278 894.667 112.333z"/>
<path style="fill:#eafff4; stroke:none;" d="M896 112C900.68 114.506 906.71 115.758 912 116L912 114C906.52 113.644 901.624 112.001 896 112z"/>
<path style="fill:#94c1a2; stroke:none;" d="M619.667 113.333C619.222 113.778 620.278 113.722 620.333 113.667C620.778 113.222 619.722 113.278 619.667 113.333z"/>
<path style="fill:#4d805f; stroke:none;" d="M621 113C622.248 113.685 622.548 113.749 624 114C622.752 113.315 622.451 113.251 621 113z"/>
<path style="fill:#118b4e; stroke:none;" d="M617 115C621.059 116.687 624.963 115.28 629 114C625.066 112.468 621.046 114.405 617 115z"/>
<path style="fill:#749a81; stroke:none;" d="M897 113L898 114L897 113z"/>
<path style="fill:#c7e9d1; stroke:none;" d="M898.667 113.333C898.222 113.778 899.278 113.722 899.333 113.667C899.777 113.223 898.722 113.278 898.667 113.333z"/>
<path style="fill:#d4fffa; stroke:none;" d="M608 116L612 115C609.954 114.249 609.404 114.344 608 116z"/>
<path style="fill:#a6ffd6; stroke:none;" d="M612 114C613.506 114.683 614.315 114.826 616 115C614.494 114.317 613.685 114.174 612 114z"/>
<path style="fill:#25965e; stroke:none;" d="M616 116L621 115C618.7 114.084 617.623 114.121 616 116z"/>
<path style="fill:#87e2b9; stroke:none;" d="M902.667 114.333C902.222 114.778 903.278 114.722 903.333 114.667C903.777 114.223 902.722 114.278 902.667 114.333z"/>
<path style="fill:#81c8a8; stroke:none;" d="M610.667 115.333C610.222 115.778 611.278 115.722 611.333 115.667C611.777 115.223 610.722 115.278 610.667 115.333z"/>
<path style="fill:#388863; stroke:none;" d="M612.667 115.333C612.222 115.778 613.278 115.722 613.333 115.667C613.777 115.223 612.722 115.278 612.667 115.333z"/>
<path style="fill:#187348; stroke:none;" d="M614.667 115.333C614.222 115.778 615.278 115.722 615.333 115.667C615.777 115.223 614.722 115.278 614.667 115.333z"/>
<path style="fill:#297652; stroke:none;" d="M903 115C904.248 115.685 904.549 115.749 906 116C904.751 115.315 904.452 115.251 903 115z"/>
<path style="fill:#6fa78a; stroke:none;" d="M906 115L907 116L906 115z"/>
<path style="fill:#dbfff5; stroke:none;" d="M599 117C601.164 117.676 602.732 117.387 605 117C602.677 116.078 601.42 116.313 599 117z"/>
<path style="fill:#88d6af; stroke:none;" d="M605 116C606.248 116.685 606.548 116.749 608 117C606.751 116.315 606.452 116.251 605 116z"/>
<path style="fill:#58cf99; stroke:none;" d="M910.667 116.333C910.222 116.778 911.278 116.722 911.333 116.667C911.777 116.223 910.722 116.278 910.667 116.333z"/>
<path style="fill:#b9f6d4; stroke:none;" d="M912 116C913.248 116.685 913.548 116.749 915 117C913.751 116.315 913.452 116.251 912 116z"/>
<path style="fill:#8ec5a8; stroke:none;" d="M602 117L603 118L602 117z"/>
<path style="fill:#398760; stroke:none;" d="M603 117C604.769 117.779 606.036 117.912 608 118C606.231 117.221 604.964 117.088 603 117z"/>
<path style="fill:#12a161; stroke:none;" d="M608 117C609.506 117.683 610.315 117.826 612 118C610.494 117.317 609.685 117.174 608 117z"/>
<path style="fill:#3f7c5a; stroke:none;" d="M912 117C913.248 117.685 913.548 117.749 915 118C913.751 117.315 913.452 117.251 912 117z"/>
<path style="fill:#a1d2b5; stroke:none;" d="M915.667 117.333C915.222 117.778 916.278 117.722 916.333 117.667C916.777 117.223 915.722 117.278 915.667 117.333z"/>
<path style="fill:#71c89b; stroke:none;" d="M597.667 118.333C597.222 118.778 598.278 118.722 598.333 118.667C598.778 118.222 597.722 118.278 597.667 118.333z"/>
<path style="fill:#338a5d; stroke:none;" d="M599 118L595 119C596.553 119.891 600.08 120.615 599 118z"/>
<path style="fill:#43a977; stroke:none;" d="M918.667 118.333C918.222 118.778 919.278 118.722 919.333 118.667C919.778 118.222 918.722 118.278 918.667 118.333z"/>
<path style="fill:#aaf6d0; stroke:none;" d="M920.667 118.333C920.222 118.778 921.278 118.722 921.333 118.667C921.778 118.223 920.722 118.278 920.667 118.333z"/>
<path style="fill:#e5ffef; stroke:none;" d="M922 118C924.042 119.416 925.511 119.745 928 120L928 118L922 118z"/>
<path style="fill:#92c0a3; stroke:none;" d="M594 119L595 120L594 119z"/>
<path style="fill:#2d8658; stroke:none;" d="M919 119C920.506 119.683 921.315 119.826 923 120C921.494 119.317 920.685 119.174 919 119z"/>
<path style="fill:#73a88c; stroke:none;" d="M923 119L924 120L923 119z"/>
<path style="fill:#cbffeb; stroke:none;" d="M586 120C587.506 120.683 588.315 120.826 590 121C588.494 120.317 587.685 120.174 586 120z"/>
<path style="fill:#7dc9a3; stroke:none;" d="M590 120L591 121L590 120z"/>
<path style="fill:#3e8460; stroke:none;" d="M591 120L587 121C588.553 121.891 592.08 122.616 591 120z"/>
<path style="fill:#8dc4a7; stroke:none;" d="M586 121L587 122L586 121z"/>
<path style="fill:#3e785f; stroke:none;" d="M928 121C929.248 121.685 929.549 121.749 931 122C929.751 121.315 929.452 121.251 928 121z"/>
<path style="fill:#81b79f; stroke:none;" d="M931 121L932 122L931 121z"/>
<path style="fill:#d5fff8; stroke:none;" d="M576 124L582 123C579.471 121.96 577.824 121.907 576 124z"/>
<path style="fill:#59c190; stroke:none;" d="M582.667 122.333C582.222 122.778 583.278 122.722 583.333 122.667C583.777 122.223 582.722 122.278 582.667 122.333z"/>
<path style="fill:#2a8a5c; stroke:none;" d="M579 123C581.587 124.065 583.321 123.781 586 123C583.66 122.434 581.429 122.824 579 123z"/>
<path style="fill:#149051; stroke:none;" d="M929 122C930.994 124.614 932.87 124.652 936 124C937.239 129.813 947.305 128 952 128C946.09 124.459 935.897 122.147 929 122z"/>
<path style="fill:#80e2b3; stroke:none;" d="M935 122L936 123L935 122z"/>
<path style="fill:#defff2; stroke:none;" d="M936 122C938.603 123.561 940.978 123.862 944 124L944 122L936 122z"/>
<path style="fill:#9de0bf; stroke:none;" d="M578 123L579 124L578 123z"/>
<path style="fill:#0f9359; stroke:none;" d="M576 124L576 126C580.302 125.943 583.941 125.451 588 124C584.347 122.876 579.881 123.989 576 124z"/>
<path style="fill:#71b793; stroke:none;" d="M938 123L939 124L938 123z"/>
<path style="fill:#e1fff2; stroke:none;" d="M565 124C567.972 126.241 570.46 125.832 574 125C571.11 123.787 568.126 124.002 565 124z"/>
<path style="fill:#97deb6; stroke:none;" d="M574.667 124.333C574.222 124.778 575.278 124.722 575.333 124.667C575.777 124.223 574.722 124.278 574.667 124.333z"/>
<path style="fill:#86f1b9; stroke:none;" d="M942.667 124.333C942.222 124.778 943.278 124.722 943.333 124.667C943.777 124.223 942.722 124.278 942.667 124.333z"/>
<path style="fill:#dcfff0; stroke:none;" d="M944 124C947.038 126.164 950.311 126.698 954 127C950.912 124.336 947.997 124.045 944 124z"/>
<path style="fill:#7ab692; stroke:none;" d="M572 125L573 126L572 125z"/>
<path style="fill:#347650; stroke:none;" d="M573 125C574.248 125.685 574.548 125.749 576 126C574.751 125.315 574.452 125.251 573 125z"/>
<path style="fill:#2f9a62; stroke:none;" d="M942 125C943.506 125.683 944.315 125.826 946 126C944.494 125.317 943.685 125.174 942 125z"/>
<path style="fill:#b3fedd; stroke:none;" d="M566 126C567.248 126.685 567.548 126.749 569 127C567.752 126.315 567.452 126.251 566 126z"/>
<path style="fill:#4db786; stroke:none;" d="M569 126L570 127L569 126z"/>
<path style="fill:#7ecca2; stroke:none;" d="M949 126L950 127L949 126z"/>
<path style="fill:#94c3b1; stroke:none;" d="M565 127L566 128L565 127z"/>
<path style="fill:#c1d5ca; stroke:none;" d="M952.667 127.333C952.222 127.778 953.278 127.722 953.333 127.667C953.777 127.223 952.722 127.278 952.667 127.333z"/>
<path style="fill:#f2fff6; stroke:none;" d="M550 132L560 129C556.014 127.448 552.781 128.991 550 132z"/>
<path style="fill:#86febe; stroke:none;" d="M560.667 128.333C560.222 128.778 561.278 128.722 561.333 128.667C561.777 128.223 560.722 128.278 560.667 128.333z"/>
<path style="fill:#52cd8c; stroke:none;" d="M562 128L563 129L562 128z"/>
<path style="fill:#1d8957; stroke:none;" d="M952 129L959 134C961.075 132.83 962.637 132.308 965 132C961.197 129.677 956.397 128.347 952 129z"/>
<path style="fill:#49be87; stroke:none;" d="M955 128L956 129L955 128z"/>
<path style="fill:#c3fff1; stroke:none;" d="M956 128C957.506 128.683 958.315 128.826 960 129C958.494 128.317 957.685 128.174 956 128z"/>
<path style="fill:#e9fffe; stroke:none;" d="M988 128L988 130L992 130L992 128L988 128z"/>
<path style="fill:#b0cdb7; stroke:none;" d="M558 129L559 130L558 129z"/>
<path style="fill:#769c83; stroke:none;" d="M559 129L560 130L559 129z"/>
<path style="fill:#76cea4; stroke:none;" d="M958.667 129.333C958.222 129.778 959.278 129.722 959.333 129.667C959.777 129.223 958.722 129.278 958.667 129.333z"/>
<path style="fill:#81e1b3; stroke:none;" d="M555 130L556 131L555 130z"/>
<path style="fill:#19965c; stroke:none;" d="M554 131C556.89 132.213 559.874 131.998 563 132C559.929 130.172 557.448 130.251 554 131z"/>
<path style="fill:#6ed3a7; stroke:none;" d="M962 130L963 131L962 130z"/>
<path style="fill:#e3fff4; stroke:none;" d="M963 130C965.535 131.502 967.131 131.608 970 131C967.668 130.015 965.543 130.019 963 130z"/>
<path style="fill:#5ea180; stroke:none;" d="M552.667 131.333C552.222 131.778 553.278 131.722 553.333 131.667C553.778 131.223 552.722 131.278 552.667 131.333z"/>
<path style="fill:#76b99a; stroke:none;" d="M965 131L966 132L965 131z"/>
<path style="fill:#85d6ad; stroke:none;" d="M549 132L550 133L549 132z"/>
<path style="fill:#208251; stroke:none;" d="M547 133C549.587 134.065 551.321 133.781 554 133C551.54 132.112 549.585 132.533 547 133z"/>
<path style="fill:#95e2be; stroke:none;" d="M968.667 132.333C968.222 132.778 969.278 132.722 969.333 132.667C969.777 132.223 968.722 132.278 968.667 132.333z"/>
<path style="fill:#e3fff0; stroke:none;" d="M970 132C973.577 134.676 977.616 135.913 982 135C978.233 133.244 974.17 132.123 970 132z"/>
<path style="fill:#96c1a5; stroke:none;" d="M546 133L547 134L546 133z"/>
<path style="fill:#208d54; stroke:none;" d="M968 133C970.814 135.428 973.326 135.892 977 136C974.18 134.228 971.286 133.485 968 133z"/>
<path style="fill:#85ba9e; stroke:none;" d="M971 133L972 134L971 133z"/>
<path style="fill:#d8fff4; stroke:none;" d="M528 140C533.287 138.279 538.521 135.943 544 135C538.867 132.85 530.802 135.198 528 140z"/>
<path style="fill:#2ba667; stroke:none;" d="M544.667 134.333C544.222 134.778 545.278 134.722 545.333 134.667C545.777 134.223 544.722 134.278 544.667 134.333z"/>
<path style="fill:#5bc88f; stroke:none;" d="M974 134L975 135L974 134z"/>
<path style="fill:#a7dcc0; stroke:none;" d="M540 135L541 136L540 135z"/>
<path style="fill:#71a98c; stroke:none;" d="M541 135L542 136L541 135z"/>
<path style="fill:#306d4c; stroke:none;" d="M542.667 135.333C542.222 135.778 543.278 135.722 543.333 135.667C543.778 135.222 542.722 135.278 542.667 135.333z"/>
<path style="fill:#71ae8f; stroke:none;" d="M977 135L978 136L977 135z"/>
<path style="fill:#62c493; stroke:none;" d="M538 136L539 137L538 136z"/>
<path style="fill:#1d975a; stroke:none;" d="M536 137C538.613 138.098 541.164 137.993 544 138L544 136C541.27 136.117 538.686 136.499 536 137z"/>
<path style="fill:#70cd9e; stroke:none;" d="M980 136L981 137L980 136z"/>
<path style="fill:#eafff5; stroke:none;" d="M981 136C987.035 139.816 995.122 142.123 1002 144C999.531 140.388 997.244 140.051 993 140L997 139C991.738 136.236 986.846 136 981 136z"/>
<path style="fill:#83c2a5; stroke:none;" d="M535 137L536 138L535 137z"/>
<path style="fill:#83b099; stroke:none;" d="M982.667 137.333C982.222 137.778 983.278 137.722 983.333 137.667C983.778 137.222 982.722 137.278 982.667 137.333z"/>
<path style="fill:#7fd0a7; stroke:none;" d="M532 138L533 139L532 138z"/>
<path style="fill:#2d865a; stroke:none;" d="M530 139C532.323 139.922 533.581 139.687 536 139C533.837 138.324 532.268 138.613 530 139z"/>
<path style="fill:#87e2b6; stroke:none;" d="M986 138L987 139L986 138z"/>
<path style="fill:#a4dfc1; stroke:none;" d="M529 139L530 140L529 139z"/>
<path style="fill:#74af8f; stroke:none;" d="M988 139L989 140L988 139z"/>
<path style="fill:#d6fff1; stroke:none;" d="M519 143L526 141C522.897 139.831 521.251 140.698 519 143z"/>
<path style="fill:#6dca9b; stroke:none;" d="M526.667 140.333C526.222 140.778 527.278 140.722 527.333 140.667C527.778 140.222 526.722 140.278 526.667 140.333z"/>
<path style="fill:#1a9054; stroke:none;" d="M520 143C523.912 144.357 528.133 142.119 532 141C528.006 139.583 523.888 141.735 520 143z"/>
<path style="fill:#62bb85; stroke:none;" d="M991 140L992 141L991 140z"/>
<path style="fill:#87cea6; stroke:none;" d="M524 141L525 142L524 141z"/>
<path style="fill:#8cd0a9; stroke:none;" d="M994 141L995 142L994 141z"/>
<path style="fill:#a0e6c1; stroke:none;" d="M521 142L522 143L521 142z"/>
<path style="fill:#80cba2; stroke:none;" d="M997 142L998 143L997 142z"/>
<path style="fill:#b2d8c3; stroke:none;" d="M518.667 143.333C518.222 143.778 519.278 143.722 519.333 143.667C519.778 143.223 518.722 143.278 518.667 143.333z"/>
<path style="fill:#78a28a; stroke:none;" d="M998.667 143.333C998.222 143.778 999.278 143.722 999.333 143.667C999.777 143.223 998.722 143.278 998.667 143.333z"/>
<path style="fill:#dbfff6; stroke:none;" d="M512 146L516 145C513.954 144.249 513.404 144.344 512 146z"/>
<path style="fill:#97efc5; stroke:none;" d="M516 144L517 145L516 144z"/>
<path style="fill:#52bc8c; stroke:none;" d="M517 144L518 145L517 144z"/>
<path style="fill:#1a8b57; stroke:none;" d="M510 147C513.782 148.546 516.599 146.849 520 145C516.586 144.283 513.283 145.932 510 147z"/>
<path style="fill:#71d7a7; stroke:none;" d="M1002 144L1003 145L1002 144z"/>
<path style="fill:#e0fff3; stroke:none;" d="M1003 144C1004.75 145.255 1005.86 145.614 1008 146C1006.46 144.197 1005.4 144.168 1003 144z"/>
<path style="fill:#8ac2a5; stroke:none;" d="M514 145L515 146L514 145z"/>
<path style="fill:#71b590; stroke:none;" d="M1004 145L1005 146L1004 145z"/>
<path style="fill:#d4fff6; stroke:none;" d="M504 150L512 147C508.32 145.516 505.869 146.61 504 150z"/>
<path style="fill:#267c4f; stroke:none;" d="M1004 147C1005.77 147.779 1007.04 147.912 1009 148C1007.07 146.909 1006.23 146.835 1004 147z"/>
<path style="fill:#77cda0; stroke:none;" d="M1006.67 146.333C1006.22 146.778 1007.28 146.722 1007.33 146.667C1007.78 146.223 1006.72 146.278 1006.67 146.333z"/>
<path style="fill:#c1ffed; stroke:none;" d="M1008 146C1009.14 147.015 1009.61 147.309 1011 148C1010.02 146.216 1010.09 146.406 1008 146z"/>
<path style="fill:#defff0; stroke:none;" d="M1011 148L1014 147C1012.22 146.044 1011.98 146.234 1011 148z"/>
<path style="fill:#8ed5b7; stroke:none;" d="M509 147L510 148L509 147z"/>
<path style="fill:#5faa8b; stroke:none;" d="M1009 147L1010 148L1009 147z"/>
<path style="fill:#f4fff4; stroke:none;" d="M493 154C497.151 152.865 501.557 151.698 504 148C499.863 148.436 495.132 150.309 493 154z"/>
<path style="fill:#5ac493; stroke:none;" d="M507 148L508 149L507 148z"/>
<path style="fill:#66d19d; stroke:none;" d="M1012 148L1013 149L1012 148z"/>
<path style="fill:#d4fff4; stroke:none;" d="M1013 148C1014.14 149.016 1014.61 149.309 1016 150C1015.02 148.216 1015.09 148.406 1013 148z"/>
<path style="fill:#63a685; stroke:none;" d="M505 149L506 150L505 149z"/>
<path style="fill:#2b8057; stroke:none;" d="M1012.67 149.333C1012.22 149.778 1013.28 149.722 1013.33 149.667C1013.78 149.222 1012.72 149.278 1012.67 149.333z"/>
<path style="fill:#6aad8a; stroke:none;" d="M1014 149L1015 150L1014 149z"/>
<path style="fill:#78deae; stroke:none;" d="M502 150L503 151L502 150z"/>
<path style="fill:#1d8353; stroke:none;" d="M503 150L501 151C502.482 151.791 503.796 151.87 503 150z"/>
<path style="fill:#6ed4a5; stroke:none;" d="M1017 150L1018 151L1017 150z"/>
<path style="fill:#d9fff8; stroke:none;" d="M1018 150C1019.14 151.015 1019.61 151.309 1021 152C1020.02 150.217 1020.09 150.406 1018 150z"/>
<path style="fill:#7fb79a; stroke:none;" d="M500 151L501 152L500 151z"/>
<path style="fill:#74af93; stroke:none;" d="M1019 151L1020 152L1019 151z"/>
<path style="fill:#9ce7c0; stroke:none;" d="M497 152L498 153L497 152z"/>
<path style="fill:#1e8250; stroke:none;" d="M496 153C498.3 153.916 499.377 153.879 501 152L496 153z"/>
<path style="fill:#52a87b; stroke:none;" d="M1021 152L1022 153L1021 152z"/>
<path style="fill:#91e2b7; stroke:none;" d="M1022 152L1023 153L1022 152z"/>
<path style="fill:#ecfef2; stroke:none;" d="M1023 152C1025.5 153.807 1028.11 154.947 1031 156C1029.16 152.487 1026.81 152.116 1023 152z"/>
<path style="fill:#b0dccd; stroke:none;" d="M495 153L496 154L495 153z"/>
<path style="fill:#22784b; stroke:none;" d="M1020 153C1021.51 153.683 1022.31 153.826 1024 154C1022.49 153.317 1021.69 153.174 1020 153z"/>
<path style="fill:#c9ead9; stroke:none;" d="M1024.67 153.333C1024.22 153.778 1025.28 153.722 1025.33 153.667C1025.78 153.222 1024.72 153.278 1024.67 153.333z"/>
<path style="fill:#d5ffec; stroke:none;" d="M488 155C489.943 155.622 490.949 155.436 493 155C490.956 154.226 490.155 154.408 488 155z"/>
<path style="fill:#68d8a6; stroke:none;" d="M493 154L494 155L493 154z"/>
<path style="fill:#53ce97; stroke:none;" d="M1026 154L1027 155L1026 154z"/>
<path style="fill:#70b795; stroke:none;" d="M491 155L492 156L491 155z"/>
<path style="fill:#a2ddbd; stroke:none;" d="M1029 155L1030 156L1029 155z"/>
<path style="fill:#a4fbce; stroke:none;" d="M487.667 156.333C487.222 156.777 488.278 156.722 488.333 156.667C488.778 156.222 487.722 156.278 487.667 156.333z"/>
<path style="fill:#248c59; stroke:none;" d="M488 158C490.092 157.594 490.02 157.783 491 156C489.42 156.684 489.223 156.805 488 158z"/>
<path style="fill:#6acc9d; stroke:none;" d="M1031 156L1032 157L1031 156z"/>
<path style="fill:#c4ffe6; stroke:none;" d="M1032 156C1033.14 157.015 1033.61 157.309 1035 158C1034.02 156.216 1034.09 156.406 1032 156z"/>
<path style="fill:#99ceb0; stroke:none;" d="M486 157L487 158L486 157z"/>
<path style="fill:#5fa581; stroke:none;" d="M487 157L488 158L487 157z"/>
<path style="fill:#78bb9a; stroke:none;" d="M1033 157L1034 158L1033 157z"/>
<path style="fill:#7ecba7; stroke:none;" d="M484 158L485 159L484 158z"/>
<path style="fill:#5ac192; stroke:none;" d="M1035 158L1036 159L1035 158z"/>
<path style="fill:#d1ffef; stroke:none;" d="M1036 158C1037.38 158.911 1038.44 159.404 1040 160C1038.69 158.382 1038.12 158.307 1036 158z"/>
<path style="fill:#9fc0af; stroke:none;" d="M482 159L483 160L482 159z"/>
<path style="fill:#609d7e; stroke:none;" d="M1037 159L1038 160L1037 159z"/>
<path style="fill:#a5dabe; stroke:none;" d="M1038 159L1039 160L1038 159z"/>
<path style="fill:#ddfff8; stroke:none;" d="M469 166L480 161C475.521 159.304 471.098 161.939 469 166z"/>
<path style="fill:#1a8f5a; stroke:none;" d="M471 165C475.257 166.493 479.65 163.456 482 160L471 165z"/>
<path style="fill:#95d3ac; stroke:none;" d="M1040 160L1041 161L1040 160z"/>
<path style="fill:#8db7a1; stroke:none;" d="M478 161L479 162L478 161z"/>
<path style="fill:#548164; stroke:none;" d="M1040.67 161.333C1040.22 161.778 1041.28 161.722 1041.33 161.667C1041.78 161.223 1040.72 161.278 1040.67 161.333z"/>
<path style="fill:#83b091; stroke:none;" d="M1042 161L1043 162L1042 161z"/>
<path style="fill:#59ba8d; stroke:none;" d="M476 162L477 163L476 162z"/>
<path style="fill:#1d995d; stroke:none;" d="M1041 162C1043.4 165.006 1047.28 165.629 1051 166C1048 163.73 1044.7 162.637 1041 162z"/>
<path style="fill:#57b787; stroke:none;" d="M1044 162L1045 163L1044 162z"/>
<path style="fill:#d8fff4; stroke:none;" d="M1045 162C1046.46 163.081 1047.25 163.436 1049 164C1047.69 162.382 1047.12 162.307 1045 162z"/>
<path style="fill:#6eab8a; stroke:none;" d="M1046 163L1047 164L1046 163z"/>
<path style="fill:#39a470; stroke:none;" d="M472.667 164.333C472.222 164.777 473.278 164.722 473.333 164.667C473.778 164.222 472.722 164.278 472.667 164.333z"/>
<path style="fill:#d9fff4; stroke:none;" d="M1049 164C1051.25 165.799 1053.16 166.493 1056 167C1053.76 164.884 1052.07 164.333 1049 164z"/>
<path style="fill:#82b99c; stroke:none;" d="M470 165L471 166L470 165z"/>
<path style="fill:#d1fffa; stroke:none;" d="M464 168L468 167C465.954 166.249 465.404 166.344 464 168z"/>
<path style="fill:#60bf93; stroke:none;" d="M468 166L469 167L468 166z"/>
<path style="fill:#14934e; stroke:none;" d="M1049 166C1049.98 167.783 1049.91 167.594 1052 168L1051 174L1058.92 172.714L1066 174C1062.09 170.102 1054.46 166.87 1049 166z"/>
<path style="fill:#48b37f; stroke:none;" d="M1052 166L1053 167L1052 166z"/>
<path style="fill:#70ba99; stroke:none;" d="M466 167L467 168L466 167z"/>
<path style="fill:#8ddfb7; stroke:none;" d="M1055 167L1056 168L1055 167z"/>
<path style="fill:#ebfff6; stroke:none;" d="M458 168C458 170.121 457.93 169.755 456 171C458.906 171.487 461.276 170.107 464 169C461.948 168.126 460.253 168.047 458 168z"/>
<path style="fill:#2b9d69; stroke:none;" d="M463 170C464.58 169.316 464.777 169.195 466 168C464.42 168.684 464.223 168.805 463 170z"/>
<path style="fill:#2e855a; stroke:none;" d="M1056 168C1056.98 169.783 1056.91 169.594 1059 170C1057.86 168.985 1057.39 168.691 1056 168z"/>
<path style="fill:#8fd0b0; stroke:none;" d="M1057 168L1058 169L1057 168z"/>
<path style="fill:#81af95; stroke:none;" d="M462 169L463 170L462 169z"/>
<path style="fill:#adddc3; stroke:none;" d="M1059 169L1060 170L1059 169z"/>
<path style="fill:#54c38e; stroke:none;" d="M460 170L461 171L460 170z"/>
<path style="fill:#94e2ba; stroke:none;" d="M1061 170L1062 171L1061 170z"/>
<path style="fill:#85c5a2; stroke:none;" d="M458 171L459 172L458 171z"/>
<path style="fill:#b2d2bd; stroke:none;" d="M1063 171L1064 172L1063 171z"/>
<path style="fill:#dcfff2; stroke:none;" d="M449 175C451.738 174.76 452.521 174.332 454 172C451.896 172.801 450.663 173.496 449 175z"/>
<path style="fill:#bcffdf; stroke:none;" d="M454 172L454 174C455.635 173.455 455.455 173.635 456 172L454 172z"/>
<path style="fill:#7fdbac; stroke:none;" d="M456 172L457 173L456 172z"/>
<path style="fill:#97e3bd; stroke:none;" d="M1065 172L1066 173L1065 172z"/>
<path style="fill:#cffff0; stroke:none;" d="M1066 172C1067.88 173.909 1069.54 174.911 1072 176C1070.43 173.281 1069.08 172.627 1066 172z"/>
<path style="fill:#167243; stroke:none;" d="M455.667 173.333C455.222 173.777 456.278 173.722 456.333 173.667C456.778 173.222 455.722 173.278 455.667 173.333z"/>
<path style="fill:#609b7b; stroke:none;" d="M1066 173L1067 174L1066 173z"/>
<path style="fill:#8ce3b9; stroke:none;" d="M452 174L453 175L452 174z"/>
<path style="fill:#268357; stroke:none;" d="M1066 174C1067.54 175.267 1068.05 175.489 1070 176C1068.46 174.733 1067.95 174.511 1066 174z"/>
<path style="fill:#5ea182; stroke:none;" d="M451 175L452 176L451 175z"/>
<path style="fill:#64a585; stroke:none;" d="M1070 175L1071 176L1070 175z"/>
<path style="fill:#86f7b9; stroke:none;" d="M448 176L449 177L448 176z"/>
<path style="fill:#46be7e; stroke:none;" d="M449 176L450 177L449 176z"/>
<path style="fill:#159653; stroke:none;" d="M446 180C449.198 179.77 450.766 179.296 453 177C449.653 176.609 447.893 177.151 446 180z"/>
<path style="fill:#59bd8b; stroke:none;" d="M1072 176L1073 177L1072 176z"/>
<path style="fill:#dcfff6; stroke:none;" d="M1073 176C1075.58 178.124 1077.66 179.026 1081 179C1078.46 176.807 1076.34 176.221 1073 176z"/>
<path style="fill:#f1fff7; stroke:none;" d="M1078 176C1079.22 177.195 1079.42 177.316 1081 178C1082.09 186.929 1093.54 182.354 1099 182L1099 180C1093.9 180.057 1090.03 181.122 1085 180L1086 178L1078 176z"/>
<path style="fill:#99b0a0; stroke:none;" d="M447 177L448 178L447 177z"/>
<path style="fill:#29915e; stroke:none;" d="M1072 177L1072 180C1076.7 180.52 1080.36 183.326 1085 184C1081.23 180.724 1076.8 178.384 1072 177z"/>
<path style="fill:#68b48d; stroke:none;" d="M1074 177L1075 178L1074 177z"/>
<path style="fill:#81e2b5; stroke:none;" d="M445 178L446 179L445 178z"/>
<path style="fill:#64b78d; stroke:none;" d="M1076 178L1077 179L1076 178z"/>
<path style="fill:#127346; stroke:none;" d="M444.667 179.333C444.222 179.778 445.278 179.722 445.333 179.667C445.778 179.222 444.722 179.278 444.667 179.333z"/>
<path style="fill:#7fb699; stroke:none;" d="M1078 179L1079 180L1078 179z"/>
<path style="fill:#9de8bf; stroke:none;" d="M441 180L442 181L441 180z"/>
<path style="fill:#3b865d; stroke:none;" d="M440 181C441.506 181.683 442.315 181.826 444 182C442.276 180.945 442.079 180.964 440 181z"/>
<path style="fill:#b9f8db; stroke:none;" d="M1080.67 180.333C1080.22 180.778 1081.28 180.722 1081.33 180.667C1081.78 180.222 1080.72 180.278 1080.67 180.333z"/>
<path style="fill:#68a78a; stroke:none;" d="M1081 181L1082 182L1081 181z"/>
<path style="fill:#69cf9f; stroke:none;" d="M438 182L439 183L438 182z"/>
<path style="fill:#58ae81; stroke:none;" d="M1083 182L1084 183L1083 182z"/>
<path style="fill:#9ce7c0; stroke:none;" d="M1084 182L1085 183L1084 182z"/>
<path style="fill:#8dc4a5; stroke:none;" d="M436 183L437 184L436 183z"/>
<path style="fill:#77b492; stroke:none;" d="M1085 183L1086 184L1085 183z"/>
<path style="fill:#c9ffeb; stroke:none;" d="M422 188C422.973 189.27 423.73 190.027 425 191L434 185C429.952 183.925 426.053 187.219 422 188z"/>
<path style="fill:#95e2b8; stroke:none;" d="M434 184L435 185L434 184z"/>
<path style="fill:#188a4f; stroke:none;" d="M1082 184L1082 186C1087.16 186.339 1090.87 189.654 1096 190C1091.88 186.423 1087.47 184.284 1082 184z"/>
<path style="fill:#63c791; stroke:none;" d="M1087 184L1088 185L1087 184z"/>
<path style="fill:#d9fff1; stroke:none;" d="M1088 184C1090.31 185.966 1091.99 186.797 1095 187C1092.84 184.723 1091.12 184.216 1088 184z"/>
<path style="fill:#64aa85; stroke:none;" d="M433 185L434 186L433 185z"/>
<path style="fill:#9bd3b8; stroke:none;" d="M1089 185L1090 186L1089 185z"/>
<path style="fill:#6ab594; stroke:none;" d="M431 186L432 187L431 186z"/>
<path style="fill:#9deec3; stroke:none;" d="M1091 186L1092 187L1091 186z"/>
<path style="fill:#9ad6ba; stroke:none;" d="M429 187L430 188L429 187z"/>
<path style="fill:#168c52; stroke:none;" d="M424 191C427.131 192.747 431.274 190.406 432 187C429.126 188.045 426.618 189.414 424 191z"/>
<path style="fill:#6eaa86; stroke:none;" d="M1092 187L1093 188L1092 187z"/>
<path style="fill:#eafff4; stroke:none;" d="M417 193C419.782 192.962 421.474 192.231 424 191L424 189C420.573 189.12 419.125 190.344 417 193z"/>
<path style="fill:#48b07b; stroke:none;" d="M428 188L429 189L428 188z"/>
<path style="fill:#6ac695; stroke:none;" d="M1094 188L1095 189L1094 188z"/>
<path style="fill:#7ec6a0; stroke:none;" d="M426 189L427 190L426 189z"/>
<path style="fill:#56b889; stroke:none;" d="M1097 190L1098 191L1097 190z"/>
<path style="fill:#a2edc6; stroke:none;" d="M1098 190L1099 191L1098 190z"/>
<path style="fill:#b7d8c5; stroke:none;" d="M422 191L423 192L422 191z"/>
<path style="fill:#73a588; stroke:none;" d="M423 191L424 192L423 191z"/>
<path style="fill:#83b399; stroke:none;" d="M1099 191L1100 192L1099 191z"/>
<path style="fill:#ecfff9; stroke:none;" d="M406 192L406 194L410 194L408 200C411.687 198.648 414.451 196.995 417 194L406 192z"/>
<path style="fill:#5eba91; stroke:none;" d="M421 192L422 193L421 192z"/>
<path style="fill:#158e58; stroke:none;" d="M417 195C420.334 196.147 422.284 194.94 424 192C421.432 192.739 419.327 193.682 417 195z"/>
<path style="fill:#79e4ac; stroke:none;" d="M1101 192L1102 193L1101 192z"/>
<path style="fill:#c2fff0; stroke:none;" d="M1102 192C1102.55 193.635 1102.36 193.455 1104 194L1104 192L1102 192z"/>
<path style="fill:#99c6b1; stroke:none;" d="M419 193L420 194L419 193z"/>
<path style="fill:#9ce6c3; stroke:none;" d="M417 194L418 195L417 194z"/>
<path style="fill:#66b796; stroke:none;" d="M1104 194L1105 195L1104 194z"/>
<path style="fill:#d5ffe7; stroke:none;" d="M1105 194C1107.43 196.322 1109.86 197.807 1113 199C1110.66 196.049 1108.7 194.745 1105 194z"/>
<path style="fill:#82ba9d; stroke:none;" d="M416 195L417 196L416 195z"/>
<path style="fill:#9ad9be; stroke:none;" d="M1106 195L1107 196L1106 195z"/>
<path style="fill:#119452; stroke:none;" d="M415 196C412.63 197.879 410.555 199.418 409 202C412.489 201.492 415.907 199.988 415 196z"/>
<path style="fill:#6eb394; stroke:none;" d="M413 197L414 198L413 197z"/>
<path style="fill:#88c9a9; stroke:none;" d="M1109 197L1110 198L1109 197z"/>
<path style="fill:#8ddbb1; stroke:none;" d="M411 198L412 199L411 198z"/>
<path style="fill:#2b8c59; stroke:none;" d="M1109 198C1109.98 199.783 1109.91 199.594 1112 200C1110.78 198.805 1110.58 198.684 1109 198z"/>
<path style="fill:#edfff3; stroke:none;" d="M1113 198C1114.77 200.893 1116.79 201.966 1120 203C1118.2 199.824 1116.63 198.531 1113 198z"/>
<path style="fill:#70a986; stroke:none;" d="M410 199L411 200L410 199z"/>
<path style="fill:#82bf9e; stroke:none;" d="M1112 199L1113 200L1112 199z"/>
<path style="fill:#eefffc; stroke:none;" d="M395 202C394.837 204.818 393.768 207.286 393 210C398.11 207.437 403.12 204.028 408 201C404.099 199.715 399.227 201.953 395 202z"/>
<path style="fill:#72c79d; stroke:none;" d="M408 200L409 201L408 200z"/>
<path style="fill:#288a57; stroke:none;" d="M1112 201C1114.26 202.618 1116.28 203.415 1119 204C1116.69 201.875 1115.13 201.061 1112 201z"/>
<path style="fill:#7ad3a7; stroke:none;" d="M1114 200L1115 201L1114 200z"/>
<path style="fill:#42976d; stroke:none;" d="M407.667 201.333C407.222 201.778 408.278 201.722 408.333 201.667C408.778 201.222 407.722 201.278 407.667 201.333z"/>
<path style="fill:#76c8a0; stroke:none;" d="M405 202L406 203L405 202z"/>
<path style="fill:#12854c; stroke:none;" d="M401 205C403.052 205.874 404.747 205.953 407 206C407 203.86 408.21 203.939 409 202C406.048 202.564 403.684 203.645 401 205z"/>
<path style="fill:#119752; stroke:none;" d="M1113 203C1118.78 208.087 1128.16 215.431 1136 216C1133.17 212.784 1130.13 211.867 1126 211C1127.53 209.977 1127.4 210.196 1128 209L1115 202L1113 203z"/>
<path style="fill:#6ebb8f; stroke:none;" d="M1117 202L1118 203L1117 202z"/>
<path style="fill:#afe0c2; stroke:none;" d="M1119 203L1120 204L1119 203z"/>
<path style="fill:#b7ffe2; stroke:none;" d="M400 204L400 206C401.635 205.455 401.455 205.635 402 204L400 204z"/>
<path style="fill:#6dc69a; stroke:none;" d="M402 204L403 205L402 204z"/>
<path style="fill:#6ec39a; stroke:none;" d="M1120 204L1121 205L1120 204z"/>
<path style="fill:#e4fff1; stroke:none;" d="M1121 204C1122.88 205.909 1124.54 206.911 1127 208C1125.53 205.186 1124.12 204.456 1121 204z"/>
<path style="fill:#64a784; stroke:none;" d="M1121 205L1122 206L1121 205z"/>
<path style="fill:#97c0b2; stroke:none;" d="M398 206L398 208C399.635 207.455 399.455 207.635 400 206L398 206z"/>
<path style="fill:#63c296; stroke:none;" d="M1123 206L1124 207L1123 206z"/>
<path style="fill:#bcdad0; stroke:none;" d="M397 207L398 208L397 207z"/>
<path style="fill:#3b6456; stroke:none;" d="M399 207L400 208L399 207z"/>
<path style="fill:#a4d9bd; stroke:none;" d="M1125 207L1126 208L1125 207z"/>
<path style="fill:#5fcb97; stroke:none;" d="M396 208L397 209L396 208z"/>
<path style="fill:#5ccc94; stroke:none;" d="M1126 208L1127 209L1126 208z"/>
<path style="fill:#e4fff4; stroke:none;" d="M1127 208C1129.62 210.726 1132.51 212.55 1136 214C1134.65 209.826 1131.16 208.554 1127 208z"/>
<path style="fill:#a2dfbd; stroke:none;" d="M394 209L395 210L394 209z"/>
<path style="fill:#168d57; stroke:none;" d="M384 217C388.624 218.618 395.542 213.283 397 209C392.216 210.84 388.225 214.126 384 217z"/>
<path style="fill:#6cc198; stroke:none;" d="M393 210L394 211L393 210z"/>
<path style="fill:#187b4c; stroke:none;" d="M1126 210C1127.14 211.016 1127.61 211.309 1129 212C1128.02 210.216 1128.09 210.406 1126 210z"/>
<path style="fill:#69bb95; stroke:none;" d="M1129 210L1130 211L1129 210z"/>
<path style="fill:#539474; stroke:none;" d="M1129.67 211.333C1129.22 211.778 1130.28 211.722 1130.33 211.667C1130.78 211.223 1129.72 211.278 1129.67 211.333z"/>
<path style="fill:#b0e0ca; stroke:none;" d="M1131 211L1132 212L1131 211z"/>
<path style="fill:#6dcfa0; stroke:none;" d="M390 212L391 213L390 212z"/>
<path style="fill:#70c79c; stroke:none;" d="M1132 212L1133 213L1132 212z"/>
<path style="fill:#b8eacd; stroke:none;" d="M388 213L389 214L388 213z"/>
<path style="fill:#579f7a; stroke:none;" d="M389 213L390 214L389 213z"/>
<path style="fill:#80c8a0; stroke:none;" d="M387 214L388 215L387 214z"/>
<path style="fill:#74e5a7; stroke:none;" d="M1135 214L1136 215L1135 214z"/>
<path style="fill:#edfff6; stroke:none;" d="M1136 214C1139.49 217.815 1143.67 221.208 1149 221C1145.67 217.413 1140.95 214.348 1136 214z"/>
<path style="fill:#85b897; stroke:none;" d="M386 215L387 216L386 215z"/>
<path style="fill:#96b1a0; stroke:none;" d="M1136 215L1137 216L1136 215z"/>
<path style="fill:#f0fdf6; stroke:none;" d="M371 223C376.061 223.205 380.768 219.731 384 216C377.621 216.058 374.777 217.904 371 223z"/>
<path style="fill:#6ae0a4; stroke:none;" d="M384 216L385 217L384 216z"/>
<path style="fill:#7dd5ab; stroke:none;" d="M1138 216L1139 217L1138 216z"/>
<path style="fill:#97b8ad; stroke:none;" d="M383 217L384 218L383 217z"/>
<path style="fill:#73bb96; stroke:none;" d="M1139 217L1140 218L1139 217z"/>
<path style="fill:#247c54; stroke:none;" d="M381 220C383.092 219.594 383.02 219.783 384 218C382.42 218.683 382.223 218.805 381 220z"/>
<path style="fill:#4caf79; stroke:none;" d="M1140 218L1141 219L1140 218z"/>
<path style="fill:#a4ecc4; stroke:none;" d="M1141 218L1142 219L1141 218z"/>
<path style="fill:#8bcaab; stroke:none;" d="M380 219L381 220L380 219z"/>
<path style="fill:#94c7a8; stroke:none;" d="M1142 219L1143 220L1142 219z"/>
<path style="fill:#b1ffd8; stroke:none;" d="M377 220L377 222C378.635 221.455 378.455 221.635 379 220L377 220z"/>
<path style="fill:#51b583; stroke:none;" d="M379 220L380 221L379 220z"/>
<path style="fill:#57b382; stroke:none;" d="M1143 220L1144 221L1143 220z"/>
<path style="fill:#1b8e53; stroke:none;" d="M375 223C377.585 223.893 378.451 223.175 380 221L375 223z"/>
<path style="fill:#99d4b4; stroke:none;" d="M1145 221L1146 222L1145 221z"/>
<path style="fill:#72bd96; stroke:none;" d="M376 222L377 223L376 222z"/>
<path style="fill:#74c9a0; stroke:none;" d="M1146 222L1147 223L1146 222z"/>
<path style="fill:#b9e0cd; stroke:none;" d="M374 223L375 224L374 223z"/>
<path style="fill:#71ac90; stroke:none;" d="M1147 223L1148 224L1147 223z"/>
<path style="fill:#7ad1a6; stroke:none;" d="M373 224L374 225L373 224z"/>
<path style="fill:#1b8c5e; stroke:none;" d="M1148 224C1147.01 225.479 1147 225.203 1147 227L1152 230L1153 227L1148 224z"/>
<path style="fill:#84edc4; stroke:none;" d="M1149 224L1150 225L1149 224z"/>
<path style="fill:#bdfff7; stroke:none;" d="M1150 224C1150.55 225.635 1150.36 225.455 1152 226L1152 224L1150 224z"/>
<path style="fill:#6fac8a; stroke:none;" d="M372 225L373 226L372 225z"/>
<path style="fill:#5ec6a1; stroke:none;" d="M1150 225L1151 226L1150 225z"/>
<path style="fill:#a3e5c1; stroke:none;" d="M370 226L371 227L370 226z"/>
<path style="fill:#53a67c; stroke:none;" d="M371 226L372 227L371 226z"/>
<path style="fill:#d8fff0; stroke:none;" d="M1152 226C1153.59 227.752 1154.95 228.794 1157 230C1155.63 227.453 1154.78 226.812 1152 226z"/>
<path style="fill:#8bcba9; stroke:none;" d="M369 227L370 228L369 227z"/>
<path style="fill:#6cd8a4; stroke:none;" d="M1154 228L1155 229L1154 228z"/>
<path style="fill:#a7dcc2; stroke:none;" d="M366 229L367 230L366 229z"/>
<path style="fill:#5cae8a; stroke:none;" d="M367 229L368 230L367 229z"/>
<path style="fill:#68ae8a; stroke:none;" d="M1155 229L1156 230L1155 229z"/>
<path style="fill:#7cd5a9; stroke:none;" d="M365 230L366 231L365 230z"/>
<path style="fill:#10934d; stroke:none;" d="M1155 230C1155.77 231.314 1155.69 231.232 1157 232L1157 234C1155.22 234.98 1155.41 234.908 1155 237C1157.8 236.703 1160.22 236.555 1163 237C1162.01 238.478 1162 238.203 1162 240L1168 240C1164.51 236.014 1159.79 232.274 1155 230z"/>
<path style="fill:#95e8be; stroke:none;" d="M1157 230L1158 231L1157 230z"/>
<path style="fill:#7ebe9b; stroke:none;" d="M364 231L365 232L364 231z"/>
<path style="fill:#95ccad; stroke:none;" d="M1158 231L1159 232L1158 231z"/>
<path style="fill:#d4fff4; stroke:none;" d="M360 234L363 233C361.222 232.044 360.979 232.234 360 234z"/>
<path style="fill:#379767; stroke:none;" d="M1159 232L1159 234L1161 234C1160.45 232.365 1160.64 232.545 1159 232z"/>
<path style="fill:#8fd2af; stroke:none;" d="M361 233L362 234L361 233z"/>
<path style="fill:#65c798; stroke:none;" d="M360 234L361 235L360 234z"/>
<path style="fill:#84ffc8; stroke:none;" d="M1162 234L1163 235L1162 234z"/>
<path style="fill:#d2fff0; stroke:none;" d="M1163 234C1164.33 235.753 1165.25 236.67 1167 238C1165.85 235.751 1165.25 235.154 1163 234z"/>
<path style="fill:#1c985c; stroke:none;" d="M359 235C357.814 236.186 357.472 236.584 357 238L361 238C360.308 236.611 360.016 236.14 359 235z"/>
<path style="fill:#84dcb4; stroke:none;" d="M1163 235L1164 236L1163 235z"/>
<path style="fill:#a1efc8; stroke:none;" d="M357 236L358 237L357 236z"/>
<path style="fill:#5cbf91; stroke:none;" d="M1164 236L1165 237L1164 236z"/>
<path style="fill:#91c9ae; stroke:none;" d="M356 237L357 238L356 237z"/>
<path style="fill:#5ca182; stroke:none;" d="M1165 237L1166 238L1165 237z"/>
<path style="fill:#9bffda; stroke:none;" d="M1167 238L1168 239L1167 238z"/>
<path style="fill:#cfd7cc; stroke:none;" d="M353 239L354 240L353 239z"/>
<path style="fill:#effff5; stroke:none;" d="M345 241C347.924 243.69 349.516 242.885 352 240C349.472 240.052 347.418 240.278 345 241z"/>
<path style="fill:#67e29f; stroke:none;" d="M352 240L353 241L352 240z"/>
<path style="fill:#5ae4a6; stroke:none;" d="M1169 240L1170 241L1169 240z"/>
<path style="fill:#e9fff7; stroke:none;" d="M1170 240C1171.62 242.044 1172.64 242.896 1175 244C1173.6 241.725 1172.49 241.013 1170 240z"/>
<path style="fill:#a3b1a0; stroke:none;" d="M351 241L352 242L351 241z"/>
<path style="fill:#5ebe8e; stroke:none;" d="M1170 241L1171 242L1170 241z"/>
<path style="fill:#2c8d60; stroke:none;" d="M349 244C350.58 243.316 350.777 243.195 352 242C350.42 242.684 350.223 242.805 349 244z"/>
<path style="fill:#198e59; stroke:none;" d="M1168 245L1177 248C1174.66 244.789 1171.05 240.514 1168 245z"/>
<path style="fill:#a9e6c4; stroke:none;" d="M347 244L348 245L347 244z"/>
<path style="fill:#91f2c7; stroke:none;" d="M1174 244L1175 245L1174 244z"/>
<path style="fill:#aec9b6; stroke:none;" d="M346 245L347 246L346 245z"/>
<path style="fill:#8ed5b7; stroke:none;" d="M1175 245L1176 246L1175 245z"/>
<path style="fill:#64b98f; stroke:none;" d="M345 246L346 247L345 246z"/>
<path style="fill:#abf6d7; stroke:none;" d="M1176.67 246.333C1176.22 246.777 1177.28 246.722 1177.33 246.667C1177.78 246.223 1176.72 246.277 1176.67 246.333z"/>
<path style="fill:#edfffc; stroke:none;" d="M1178 246C1181 252.293 1188.34 258.015 1194 262C1191.68 254.177 1185.08 249.45 1178 246z"/>
<path style="fill:#70bb9c; stroke:none;" d="M1177 247L1178 248L1177 247z"/>
<path style="fill:#a3ebc6; stroke:none;" d="M342 248L343 249L342 248z"/>
<path style="fill:#45be89; stroke:none;" d="M1178 248L1179 249L1178 248z"/>
<path style="fill:#9fcfb5; stroke:none;" d="M341 249L342 250L341 249z"/>
<path style="fill:#66cc9c; stroke:none;" d="M340 250L341 251L340 250z"/>
<path style="fill:#65a583; stroke:none;" d="M339 251L340 252L339 251z"/>
<path style="fill:#90e6b9; stroke:none;" d="M1183 252L1184 253L1183 252z"/>
<path style="fill:#98c2b4; stroke:none;" d="M1184 253L1185 254L1184 253z"/>
<path style="fill:#b3d1c7; stroke:none;" d="M335 254L336 255L335 254z"/>
<path style="fill:#386d5b; stroke:none;" d="M1184 254L1184 256L1186 256C1185.45 254.365 1185.64 254.545 1184 254z"/>
<path style="fill:#98c1b3; stroke:none;" d="M1185 254L1186 255L1185 254z"/>
<path style="fill:#a3bdb4; stroke:none;" d="M334 255L335 256L334 255z"/>
<path style="fill:#5c7a70; stroke:none;" d="M335 255L336 256L335 255z"/>
<path style="fill:#91baac; stroke:none;" d="M1186 255L1187 256L1186 255z"/>
<path style="fill:#5fc38f; stroke:none;" d="M333 256L334 257L333 256z"/>
<path style="fill:#62c292; stroke:none;" d="M1187 256L1188 257L1187 256z"/>
<path style="fill:#5ea47f; stroke:none;" d="M332 257L333 258L332 257z"/>
<path style="fill:#74b08e; stroke:none;" d="M1188 257L1189 258L1188 257z"/>
<path style="fill:#238053; stroke:none;" d="M330 260C332.092 259.594 332.02 259.783 333 258C331.42 258.684 331.223 258.805 330 260z"/>
<path style="fill:#1e8653; stroke:none;" d="M1187 260L1190 260L1189 258C1187.36 258.545 1187.55 258.365 1187 260z"/>
<path style="fill:#60b28a; stroke:none;" d="M1189 258L1190 259L1189 258z"/>
<path style="fill:#70ab8d; stroke:none;" d="M1190 259L1191 260L1190 259z"/>
<path style="fill:#93ebc5; stroke:none;" d="M328 260L329 261L328 260z"/>
<path style="fill:#97cfb6; stroke:none;" d="M327 261L328 262L327 261z"/>
<path style="fill:#65a487; stroke:none;" d="M1192 261L1193 262L1192 261z"/>
<path style="fill:#79cba7; stroke:none;" d="M326 262L327 263L326 262z"/>
<path style="fill:#53ab83; stroke:none;" d="M1193 262L1194 263L1193 262z"/>
<path style="fill:#7ab299; stroke:none;" d="M325 263L326 264L325 263z"/>
<path style="fill:#5b9c7e; stroke:none;" d="M1194 263L1195 264L1194 263z"/>
<path style="fill:#eafffd; stroke:none;" d="M319 268L324 265C321.184 264.389 320.325 265.556 319 268z"/>
<path style="fill:#5cc398; stroke:none;" d="M324 264L325 265L324 264z"/>
<path style="fill:#61ae8e; stroke:none;" d="M323 265L324 266L323 265z"/>
<path style="fill:#9bedc7; stroke:none;" d="M321 266L322 267L321 266z"/>
<path style="fill:#4cb185; stroke:none;" d="M322 266L323 267L322 266z"/>
<path style="fill:#51a87e; stroke:none;" d="M1197 266L1198 267L1197 266z"/>
<path style="fill:#a9ddc6; stroke:none;" d="M320 267L321 268L320 267z"/>
<path style="fill:#f1fff6; stroke:none;" d="M308 272L311 276C314.245 273.709 316.709 271.245 319 268C314.856 268.694 312.356 271.667 308 272z"/>
<path style="fill:#a4f3d6; stroke:none;" d="M319 268L320 269L319 268z"/>
<path style="fill:#96cbb7; stroke:none;" d="M318 269L319 270L318 269z"/>
<path style="fill:#4b9a7d; stroke:none;" d="M319 269L320 270L319 269z"/>
<path style="fill:#6da08f; stroke:none;" d="M1200 269L1201 270L1200 269z"/>
<path style="fill:#71d1ac; stroke:none;" d="M317 270L318 271L317 270z"/>
<path style="fill:#0f7e54; stroke:none;" d="M317 272C319.092 271.594 319.02 271.783 320 270C318.42 270.684 318.223 270.805 317 272z"/>
<path style="fill:#388269; stroke:none;" d="M1200 270C1200.98 271.783 1200.91 271.594 1203 272C1201.78 270.805 1201.58 270.684 1200 270z"/>
<path style="fill:#84caae; stroke:none;" d="M316 271L317 272L316 271z"/>
<path style="fill:#77c595; stroke:none;" d="M315 272L316 273L315 272z"/>
<path style="fill:#81b08e; stroke:none;" d="M314 273L315 274L314 273z"/>
<path style="fill:#66c293; stroke:none;" d="M313 274L314 275L313 274z"/>
<path style="fill:#56b58b; stroke:none;" d="M1205 274L1206 275L1205 274z"/>
<path style="fill:#e2fffb; stroke:none;" d="M1206 274L1207 276C1208.63 275.455 1208.45 275.635 1209 274L1206 274z"/>
<path style="fill:#64ab8b; stroke:none;" d="M1206 275L1207 276L1206 275z"/>
<path style="fill:#61c098; stroke:none;" d="M1207 276L1208 277L1207 276z"/>
<path style="fill:#63987e; stroke:none;" d="M310 277L311 278L310 277z"/>
<path style="fill:#77b198; stroke:none;" d="M1208 277L1209 278L1208 277z"/>
<path style="fill:#a8fdd6; stroke:none;" d="M308 278L309 279L308 278z"/>
<path style="fill:#72c29f; stroke:none;" d="M1209 278L1210 279L1209 278z"/>
<path style="fill:#4ea37c; stroke:none;" d="M308 279L309 280L308 279z"/>
<path style="fill:#86bca4; stroke:none;" d="M1210 279L1211 280L1210 279z"/>
<path style="fill:#9feac3; stroke:none;" d="M306 280L307 281L306 280z"/>
<path style="fill:#12944e; stroke:none;" d="M315 280L317 286L320 286C319.57 282.259 318.814 280.661 315 280z"/>
<path style="fill:#6ac69f; stroke:none;" d="M1211 280L1212 281L1211 280z"/>
<path style="fill:#a2d7b9; stroke:none;" d="M305 281L306 282L305 281z"/>
<path style="fill:#87c6a9; stroke:none;" d="M1212 281L1213 282L1212 281z"/>
<path style="fill:#79f7b8; stroke:none;" d="M304 282L305 283L304 282z"/>
<path style="fill:#8ad6b2; stroke:none;" d="M1213 282L1214 283L1213 282z"/>
<path style="fill:#aed8c0; stroke:none;" d="M1214 283L1215 284L1214 283z"/>
<path style="fill:#92e7c0; stroke:none;" d="M302 284L303 285L302 284z"/>
<path style="fill:#92e5bb; stroke:none;" d="M1215 284L1216 285L1215 284z"/>
<path style="fill:#9bdabb; stroke:none;" d="M301 285L302 286L301 285z"/>
<path style="fill:#4da27b; stroke:none;" d="M302 285L303 286L302 285z"/>
<path style="fill:#88d8b3; stroke:none;" d="M300 286L301 287L300 286z"/>
<path style="fill:#84ad9f; stroke:none;" d="M1216 286L1217 287L1216 286z"/>
<path style="fill:#93c0a9; stroke:none;" d="M299 287L300 288L299 287z"/>
<path style="fill:#30594b; stroke:none;" d="M1216 287L1217 288L1216 287z"/>
<path style="fill:#80a095; stroke:none;" d="M1217 287L1218 288L1217 287z"/>
<path style="fill:#cfffe8; stroke:none;" d="M296 288L296 290C297.635 289.455 297.455 289.635 298 288L296 288z"/>
<path style="fill:#61e1a4; stroke:none;" d="M298 288L299 289L298 288z"/>
<path style="fill:#4fc186; stroke:none;" d="M1218 288L1219 289L1218 288z"/>
<path style="fill:#74cfa3; stroke:none;" d="M297 289L298 290L297 289z"/>
<path style="fill:#6fb18b; stroke:none;" d="M1219 289L1220 290L1219 289z"/>
<path style="fill:#7ac8a1; stroke:none;" d="M296 290L297 291L296 290z"/>
<path style="fill:#83d4a9; stroke:none;" d="M1220 290L1221 291L1220 290z"/>
<path style="fill:#9cc9b2; stroke:none;" d="M295 291L296 292L295 291z"/>
<path style="fill:#99d0b1; stroke:none;" d="M1221 291L1222 292L1221 291z"/>
<path style="fill:#82cdac; stroke:none;" d="M294 292L295 293L294 292z"/>
<path style="fill:#169053; stroke:none;" d="M858 292L859 294L848 294L848 296C853.721 295.996 859.979 296.027 865 293C862.582 292.278 860.528 292.052 858 292z"/>
<path style="fill:#45916b; stroke:none;" d="M864 292C865.223 293.195 865.42 293.316 867 294C865.777 292.805 865.58 292.684 864 292z"/>
<path style="fill:#8afac8; stroke:none;" d="M1222 292L1223 293L1222 292z"/>
<path style="fill:#8fc5ad; stroke:none;" d="M293 293L294 294L293 293z"/>
<path style="fill:#9affda; stroke:none;" d="M867 293C867.545 294.635 867.365 294.455 869 295L869 293L867 293z"/>
<path style="fill:#2dab6e; stroke:none;" d="M869.333 293.667C869.278 293.722 869.222 294.778 869.667 294.333C869.723 294.277 869.778 293.222 869.333 293.667z"/>
<path style="fill:#a3edcc; stroke:none;" d="M1223 293L1224 294L1223 293z"/>
<path style="fill:#79d1a9; stroke:none;" d="M292 294L293 295L292 294z"/>
<path style="fill:#addbbf; stroke:none;" d="M862.667 294.333C862.222 294.778 863.278 294.722 863.333 294.667C863.778 294.223 862.722 294.278 862.667 294.333z"/>
<path style="fill:#dcfff3; stroke:none;" d="M858 295C860.577 296.217 862.42 296.536 864 299C866.044 297.376 866.896 296.357 868 294L858 295z"/>
<path style="fill:#8bc3aa; stroke:none;" d="M291 295L292 296L291 295z"/>
<path style="fill:#59bb8a; stroke:none;" d="M857 295L858 296L857 295z"/>
<path style="fill:#169457; stroke:none;" d="M868 295L868 300C870.023 298.597 870.838 297.2 872 295L868 295z"/>
<path style="fill:#79c09e; stroke:none;" d="M1224 295L1225 296L1224 295z"/>
<path style="fill:#7dc9a2; stroke:none;" d="M290 296L291 297L290 296z"/>
<path style="fill:#3c7e62; stroke:none;" d="M848 296C849.769 296.779 851.036 296.912 853 297C851.231 296.221 849.964 296.088 848 296z"/>
<path style="fill:#86c2a8; stroke:none;" d="M853 296L854 297L853 296z"/>
<path style="fill:#d6fff8; stroke:none;" d="M850 297C852.587 298.065 854.321 297.781 857 297C854.607 296.259 852.507 296.698 850 297z"/>
<path style="fill:#227f52; stroke:none;" d="M867 296L865 300C867.649 299.432 868.355 298.412 867 296z"/>
<path style="fill:#4a9870; stroke:none;" d="M1225.33 296.667C1225.28 296.722 1225.22 297.778 1225.67 297.333C1225.72 297.278 1225.78 296.222 1225.33 296.667z"/>
<path style="fill:#edfffa; stroke:none;" d="M1226 296C1227.69 298.909 1229.34 300.95 1232 303C1231.08 299.321 1229.68 297.145 1226 296z"/>
<path style="fill:#97c9ac; stroke:none;" d="M289 297L290 298L289 297z"/>
<path style="fill:#95d7bb; stroke:none;" d="M848.667 297.333C848.222 297.778 849.278 297.722 849.333 297.667C849.778 297.222 848.722 297.278 848.667 297.333z"/>
<path style="fill:#6cab8e; stroke:none;" d="M866 297L867 298L866 297z"/>
<path style="fill:#83ba9d; stroke:none;" d="M1226 297L1227 298L1226 297z"/>
<path style="fill:#99e8b9; stroke:none;" d="M288 298L289 299L288 298z"/>
<path style="fill:#70a088; stroke:none;" d="M844 298L845 299L844 298z"/>
<path style="fill:#a8cfba; stroke:none;" d="M845 298L846 299L845 298z"/>
<path style="fill:#209858; stroke:none;" d="M1225 298C1226.59 301.089 1227.65 302.928 1231 304C1229.41 301.01 1228.04 299.486 1225 298z"/>
<path style="fill:#93e4bb; stroke:none;" d="M1227 298L1228 299L1227 298z"/>
<path style="fill:#b6e0ca; stroke:none;" d="M287 299L288 300L287 299z"/>
<path style="fill:#4e9d6e; stroke:none;" d="M288 299L289 300L288 299z"/>
<path style="fill:#93e0bc; stroke:none;" d="M840.667 299.333C840.222 299.778 841.278 299.722 841.333 299.667C841.777 299.223 840.722 299.278 840.667 299.333z"/>
<path style="fill:#73b492; stroke:none;" d="M864 299L865 300L864 299z"/>
<path style="fill:#a2f4ce; stroke:none;" d="M286 300L287 301L286 300z"/>
<path style="fill:#208253; stroke:none;" d="M828 300C830.7 301.969 832.757 301.568 836 301C833.387 299.902 830.835 300.007 828 300z"/>
<path style="fill:#61ae8c; stroke:none;" d="M836 300L837 301L836 300z"/>
<path style="fill:#d9fff4; stroke:none;" d="M834 301C836.331 301.984 838.457 301.981 841 302L841 300L834 301z"/>
<path style="fill:#85d1b5; stroke:none;" d="M863 300L864 301L863 300z"/>
<path style="fill:#a1debf; stroke:none;" d="M285 301L286 302L285 301z"/>
<path style="fill:#79daaf; stroke:none;" d="M833 301L834 302L833 301z"/>
<path style="fill:#9bcebb; stroke:none;" d="M862 301L863 302L862 301z"/>
<path style="fill:#97e4c0; stroke:none;" d="M284 302L285 303L284 302z"/>
<path style="fill:#87c5a0; stroke:none;" d="M829 302L830 303L829 302z"/>
<path style="fill:#d3ffec; stroke:none;" d="M826 303C828.587 304.065 830.321 303.781 833 303C830.607 302.259 828.507 302.698 826 303z"/>
<path style="fill:#89e2b6; stroke:none;" d="M861 302L862 303L861 302z"/>
<path style="fill:#6fd39d; stroke:none;" d="M1230 302L1231 303L1230 302z"/>
<path style="fill:#b4e0c9; stroke:none;" d="M283 303L284 304L283 303z"/>
<path style="fill:#149253; stroke:none;" d="M286 303C285.043 304.516 284.613 305.339 284 307C279.48 306.89 277.414 309.925 276 314C278.627 313.161 279.161 312.627 280 310L286 310C285.402 308.805 285.534 309.022 284 308C286.345 306.375 287.603 305.563 286 303z"/>
<path style="fill:#54c48c; stroke:none;" d="M825 303L826 304L825 303z"/>
<path style="fill:#50a97d; stroke:none;" d="M861 303L862 304L861 303z"/>
<path style="fill:#9ae5bc; stroke:none;" d="M1231 303L1232 304L1231 303z"/>
<path style="fill:#97fdcd; stroke:none;" d="M282 304L283 305L282 304z"/>
<path style="fill:#2c8764; stroke:none;" d="M816 304C818.129 305.25 819.494 305.16 822 305C819.948 304.126 818.253 304.047 816 304z"/>
<path style="fill:#a1ccbb; stroke:none;" d="M822 304L823 305L822 304z"/>
<path style="fill:#d4fbe9; stroke:none;" d="M1232 304C1233.1 305.457 1233.54 305.897 1235 307C1234.1 304.876 1234.12 304.899 1232 304z"/>
<path style="fill:#43a979; stroke:none;" d="M282 305L283 306L282 305z"/>
<path style="fill:#6ec9a6; stroke:none;" d="M818 305L819 306L818 305z"/>
<path style="fill:#5caa83; stroke:none;" d="M859 305L860 306L859 305z"/>
<path style="fill:#469870; stroke:none;" d="M1232 305L1233 306L1232 305z"/>
<path style="fill:#2b8955; stroke:none;" d="M806 306C806.208 307.661 806.495 307.54 805 308C808.092 308.939 810.921 307.864 814 307C811.387 305.902 808.835 306.007 806 306z"/>
<path style="fill:#7ebc97; stroke:none;" d="M814.667 306.333C814.222 306.778 815.278 306.722 815.333 306.667C815.778 306.222 814.722 306.278 814.667 306.333z"/>
<path style="fill:#75cda3; stroke:none;" d="M858 306L859 307L858 306z"/>
<path style="fill:#5db18d; stroke:none;" d="M1233 306L1234 307L1233 306z"/>
<path style="fill:#8edbb1; stroke:none;" d="M812 307L813 308L812 307z"/>
<path style="fill:#afeace; stroke:none;" d="M857 307L858 308L857 307z"/>
<path style="fill:#8ec6ab; stroke:none;" d="M1234 307L1235 308L1234 307z"/>
<path style="fill:#168b54; stroke:none;" d="M795 310C798.262 312.234 801.294 310.625 805 310L805 308L795 310z"/>
<path style="fill:#aad6bb; stroke:none;" d="M808.667 308.333C808.222 308.778 809.277 308.722 809.333 308.667C809.777 308.223 808.722 308.278 808.667 308.333z"/>
<path style="fill:#e4fff3; stroke:none;" d="M810 308C806.732 309.251 803.412 310.218 800 311C802.881 312.388 810.734 312.549 810 308z"/>
<path style="fill:#cefbe6; stroke:none;" d="M855 308L855 310C856.635 309.455 856.455 309.635 857 308L855 308z"/>
<path style="fill:#a0e8c3; stroke:none;" d="M1235 308L1236 309L1235 308z"/>
<path style="fill:#60aa89; stroke:none;" d="M278 309L279 310L278 309z"/>
<path style="fill:#60c090; stroke:none;" d="M805 309L806 310L805 309z"/>
<path style="fill:#51ad86; stroke:none;" d="M856 309L857 310L856 309z"/>
<path style="fill:#608772; stroke:none;" d="M801 310L802 311L801 310z"/>
<path style="fill:#94bba6; stroke:none;" d="M802 310L803 311L802 310z"/>
<path style="fill:#6fcea4; stroke:none;" d="M855 310L856 311L855 310z"/>
<path style="fill:#298959; stroke:none;" d="M1236.33 310.667C1236.28 310.722 1236.22 311.778 1236.67 311.333C1236.72 311.278 1236.78 310.222 1236.33 310.667z"/>
<path style="fill:#7bab91; stroke:none;" d="M276 311L277 312L276 311z"/>
<path style="fill:#4cbc8a; stroke:none;" d="M799 311L800 312L799 311z"/>
<path style="fill:#a6d6c0; stroke:none;" d="M854 311L855 312L854 311z"/>
<path style="fill:#73b08f; stroke:none;" d="M1237 311L1238 312L1237 311z"/>
<path style="fill:#3f976d; stroke:none;" d="M275.333 312.667C275.277 312.723 275.223 313.778 275.667 313.333C275.722 313.278 275.778 312.222 275.333 312.667z"/>
<path style="fill:#218954; stroke:none;" d="M784 316L796 313C791.716 311.207 786.739 312.15 784 316z"/>
<path style="fill:#7cb79b; stroke:none;" d="M796 312L797 313L796 312z"/>
<path style="fill:#e4fff4; stroke:none;" d="M783 317C786.529 318.704 794.332 318.691 795 314C797.403 313.832 798.46 313.803 800 312C794.096 312.872 788.724 315.438 783 317z"/>
<path style="fill:#b3eed0; stroke:none;" d="M853 312L854 313L853 312z"/>
<path style="fill:#5dab83; stroke:none;" d="M1238.33 312.667C1238.28 312.722 1238.22 313.778 1238.67 313.333C1238.72 313.278 1238.78 312.222 1238.33 312.667z"/>
<path style="fill:#84c4a2; stroke:none;" d="M274 313L275 314L274 313z"/>
<path style="fill:#6dbe95; stroke:none;" d="M793 313L794 314L793 313z"/>
<path style="fill:#b8dbc5; stroke:none;" d="M1239 313L1240 314L1239 313z"/>
<path style="fill:#84c8a1; stroke:none;" d="M273 314L274 315L273 314z"/>
<path style="fill:#6eb08c; stroke:none;" d="M790 314L791 315L790 314z"/>
<path style="fill:#68c89a; stroke:none;" d="M852 314L853 315L852 314z"/>
<path style="fill:#298458; stroke:none;" d="M1239.33 314.667C1239.28 314.722 1239.22 315.778 1239.67 315.333C1239.72 315.278 1239.78 314.222 1239.33 314.667z"/>
<path style="fill:#96c1a3; stroke:none;" d="M272 315L273 316L272 315z"/>
<path style="fill:#55b683; stroke:none;" d="M787 315L788 316L787 315z"/>
<path style="fill:#96d6b4; stroke:none;" d="M851 315L852 316L851 315z"/>
<path style="fill:#8bc0a2; stroke:none;" d="M1240 315L1241 316L1240 315z"/>
<path style="fill:#f2fff6; stroke:none;" d="M1243 315C1242.63 321.117 1247.8 327.175 1253 330C1253 327.144 1250.89 321.541 1250 326C1248.32 321.109 1247.3 318.142 1243 315z"/>
<path style="fill:#9ee8cf; stroke:none;" d="M271 316L272 317L271 316z"/>
<path style="fill:#20854d; stroke:none;" d="M272 316L270 320C271.859 318.735 272.369 318.223 272 316z"/>
<path style="fill:#36875e; stroke:none;" d="M778 319L785 317C782.121 315.341 778.772 315.398 778 319z"/>
<path style="fill:#93c0a9; stroke:none;" d="M785 316L786 317L785 316z"/>
<path style="fill:#d1f5db; stroke:none;" d="M849 316L849 318C850.635 317.455 850.455 317.635 851 316L849 316z"/>
<path style="fill:#1d864d; stroke:none;" d="M851 316L849 320C850.996 318.756 851.558 318.308 851 316z"/>
<path style="fill:#a9f4cd; stroke:none;" d="M1241 316L1242 317L1241 316z"/>
<path style="fill:#a9d9c9; stroke:none;" d="M270 317L271 318L270 317z"/>
<path style="fill:#6ebf96; stroke:none;" d="M782 317L783 318L782 317z"/>
<path style="fill:#5eab81; stroke:none;" d="M850 317L851 318L850 317z"/>
<path style="fill:#5ea982; stroke:none;" d="M1241 317L1242 318L1241 317z"/>
<path style="fill:#abe6d2; stroke:none;" d="M269 318L270 319L269 318z"/>
<path style="fill:#5da18a; stroke:none;" d="M270 318L271 319L270 318z"/>
<path style="fill:#6aab8b; stroke:none;" d="M779 318L780 319L779 318z"/>
<path style="fill:#acdcc2; stroke:none;" d="M780 318L781 319L780 318z"/>
<path style="fill:#8acfa3; stroke:none;" d="M849 318L850 319L849 318z"/>
<path style="fill:#6dba90; stroke:none;" d="M1242 318L1243 319L1242 318z"/>
<path style="fill:#5d9884; stroke:none;" d="M269 319L270 320L269 319z"/>
<path style="fill:#75ddac; stroke:none;" d="M777 319L778 320L777 319z"/>
<path style="fill:#a8ceb5; stroke:none;" d="M1243 319L1244 320L1243 319z"/>
<path style="fill:#119154; stroke:none;" d="M761 322C761 324.14 759.79 324.061 759 326C764.131 324.784 768.987 322.634 774 321C769.946 319.465 765.322 321.875 761 322z"/>
<path style="fill:#88b29e; stroke:none;" d="M774 320L775 321L774 320z"/>
<path style="fill:#0d9253; stroke:none;" d="M1243 320C1243.5 322.686 1243.88 325.27 1244 328L1248 328C1246.82 324.793 1245.45 322.383 1243 320z"/>
<path style="fill:#89d9b6; stroke:none;" d="M772 321L773 322L772 321z"/>
<path style="fill:#87b39c; stroke:none;" d="M847 321L848 322L847 321z"/>
<path style="fill:#5dbc90; stroke:none;" d="M266 322L267 323L266 322z"/>
<path style="fill:#7dc0a1; stroke:none;" d="M769 322L770 323L769 322z"/>
<path style="fill:#d7ffeb; stroke:none;" d="M767 323C769.052 323.874 770.747 323.953 773 324C770.838 322.746 769.517 322.776 767 323z"/>
<path style="fill:#178e56; stroke:none;" d="M847 322L845 326C847.649 325.432 848.355 324.412 847 322z"/>
<path style="fill:#96d9b6; stroke:none;" d="M1245 322L1246 323L1245 322z"/>
<path style="fill:#7cbd9f; stroke:none;" d="M265 323L266 324L265 323z"/>
<path style="fill:#5ebe8e; stroke:none;" d="M766 323L767 324L766 323z"/>
<path style="fill:#7bd3ad; stroke:none;" d="M264 324L265 325L264 324z"/>
<path style="fill:#8dc5aa; stroke:none;" d="M764 324L765 325L764 324z"/>
<path style="fill:#78caa4; stroke:none;" d="M845 324L846 325L845 324z"/>
<path style="fill:#5ab78a; stroke:none;" d="M1246 324L1247 325L1246 324z"/>
<path style="fill:#b0e4cd; stroke:none;" d="M263 325L264 326L263 325z"/>
<path style="fill:#4bb98a; stroke:none;" d="M761 325L762 326L761 325z"/>
<path style="fill:#b7ffde; stroke:none;" d="M762.667 325.333C762.222 325.778 763.278 325.722 763.333 325.667C763.778 325.222 762.722 325.278 762.667 325.333z"/>
<path style="fill:#8fceaf; stroke:none;" d="M1247 325L1248 326L1247 325z"/>
<path style="fill:#90d9bb; stroke:none;" d="M262 326L263 327L262 326z"/>
<path style="fill:#4aad81; stroke:none;" d="M263 326L264 327L263 326z"/>
<path style="fill:#559d78; stroke:none;" d="M757 328L760 327C758.222 326.045 757.979 326.234 757 328z"/>
<path style="fill:#e1fff7; stroke:none;" d="M837 337C840.009 333.797 842.143 329.973 844 326C839.521 328.308 837.346 332.013 837 337z"/>
<path style="fill:#188e5a; stroke:none;" d="M844 326L839 336C842.854 334.878 845.375 329.776 844 326z"/>
<path style="fill:#63ac8e; stroke:none;" d="M262 327L263 328L262 327z"/>
<path style="fill:#71b493; stroke:none;" d="M843 327L844 328L843 327z"/>
<path style="fill:#87b599; stroke:none;" d="M1248 327L1249 328L1248 327z"/>
<path style="fill:#189350; stroke:none;" d="M747 331C749.469 330.574 751.645 329.868 754 329C751.121 327.341 747.772 327.398 747 331z"/>
<path style="fill:#5ea885; stroke:none;" d="M754 328L755 329L754 328z"/>
<path style="fill:#99d6b7; stroke:none;" d="M755 328L756 329L755 328z"/>
<path style="fill:#d3fff1; stroke:none;" d="M754 329C755.506 329.683 756.315 329.826 758 330C756.276 328.944 756.079 328.964 754 329z"/>
<path style="fill:#288a57; stroke:none;" d="M1248 328C1248.78 330.132 1249.13 330.771 1251 332C1250.04 330.233 1249.43 329.393 1248 328z"/>
<path style="fill:#a7debe; stroke:none;" d="M1249 328L1250 329L1249 328z"/>
<path style="fill:#5eb290; stroke:none;" d="M260 329L261 330L260 329z"/>
<path style="fill:#98e4c0; stroke:none;" d="M752.667 329.333C752.222 329.778 753.278 329.722 753.333 329.667C753.778 329.222 752.722 329.278 752.667 329.333z"/>
<path style="fill:#d8ffee; stroke:none;" d="M1250 329C1251.33 331.719 1252.42 333.43 1255 335C1253.72 332.148 1252.72 330.535 1250 329z"/>
<path style="fill:#69c69a; stroke:none;" d="M259 330L260 331L259 330z"/>
<path style="fill:#169351; stroke:none;" d="M736 330L736 334C739.43 334.481 741.78 334.293 745 333C742.948 332.126 741.253 332.047 739 332L740 330L736 330z"/>
<path style="fill:#99be9f; stroke:none;" d="M750 330L751 331L750 330z"/>
<path style="fill:#92e2bd; stroke:none;" d="M841 330L842 331L841 330z"/>
<path style="fill:#6ac395; stroke:none;" d="M1250 330L1251 331L1250 330z"/>
<path style="fill:#87c2a6; stroke:none;" d="M258 331L259 332L258 331z"/>
<path style="fill:#8edbaf; stroke:none;" d="M748 331L749 332L748 331z"/>
<path style="fill:#86e3b6; stroke:none;" d="M257 332L258 333L257 332z"/>
<path style="fill:#5caa83; stroke:none;" d="M745 332L746 333L745 332z"/>
<path style="fill:#a1debd; stroke:none;" d="M746 332L747 333L746 332z"/>
<path style="fill:#6fcba4; stroke:none;" d="M840 332L841 333L840 332z"/>
<path style="fill:#a1e1be; stroke:none;" d="M256 333L257 334L256 333z"/>
<path style="fill:#b3ffda; stroke:none;" d="M744.667 333.333C744.222 333.778 745.278 333.722 745.333 333.667C745.777 333.223 744.722 333.278 744.667 333.333z"/>
<path style="fill:#277e54; stroke:none;" d="M1251 333C1251.41 335.092 1251.22 335.02 1253 336C1252.32 334.42 1252.2 334.223 1251 333z"/>
<path style="fill:#92d9b7; stroke:none;" d="M1252 333L1253 334L1252 333z"/>
<path style="fill:#75af89; stroke:none;" d="M741 334L742 335L741 334z"/>
<path style="fill:#dbffef; stroke:none;" d="M742 334L740 335C741.482 335.791 742.796 335.87 742 334z"/>
<path style="fill:#60b794; stroke:none;" d="M839 334L840 335L839 334z"/>
<path style="fill:#718880; stroke:none;" d="M255 335L256 336L255 335z"/>
<path style="fill:#55cb8e; stroke:none;" d="M739 335L740 336L739 335z"/>
<path style="fill:#9eddc2; stroke:none;" d="M838 335L839 336L838 335z"/>
<path style="fill:#77ae97; stroke:none;" d="M1253 335L1254 336L1253 335z"/>
<path style="fill:#129851; stroke:none;" d="M254 336L247 346C249.035 347.226 249.326 347.703 250 350L254 348C253.224 345.868 252.87 345.229 251 344C251.986 342.521 252 342.797 252 341C253.186 342.186 253.584 342.528 255 343C255.608 340.131 255.502 338.535 254 336z"/>
<path style="fill:#587c70; stroke:none;" d="M736 336L737 337L736 336z"/>
<path style="fill:#93aea5; stroke:none;" d="M737 336L738 337L737 336z"/>
<path style="fill:#5cb78b; stroke:none;" d="M1254.33 336.667C1254.28 336.722 1254.22 337.778 1254.67 337.333C1254.72 337.278 1254.78 336.222 1254.33 336.667z"/>
<path style="fill:#74b692; stroke:none;" d="M253 337L254 338L253 337z"/>
<path style="fill:#57d596; stroke:none;" d="M735 337L736 338L735 337z"/>
<path style="fill:#88c09b; stroke:none;" d="M837 337L838 338L837 337z"/>
<path style="fill:#93e1b9; stroke:none;" d="M252 338L253 339L252 338z"/>
<path style="fill:#8dc0a1; stroke:none;" d="M733 338L734 339L733 338z"/>
<path style="fill:#85cfac; stroke:none;" d="M1255 338L1256 339L1255 338z"/>
<path style="fill:#78d1a5; stroke:none;" d="M731 339L732 340L731 339z"/>
<path style="fill:#81b795; stroke:none;" d="M836 339L837 340L836 339z"/>
<path style="fill:#30895d; stroke:none;" d="M727.667 340.333C727.222 340.778 728.278 340.722 728.333 340.667C728.777 340.223 727.722 340.278 727.667 340.333z"/>
<path style="fill:#8dd0af; stroke:none;" d="M729 340L730 341L729 340z"/>
<path style="fill:#c5ffe7; stroke:none;" d="M730 340L728 341C729.482 341.791 730.796 341.87 730 340z"/>
<path style="fill:#a9f1cc; stroke:none;" d="M835 340L836 341L835 340z"/>
<path style="fill:#70c59c; stroke:none;" d="M1256 340L1257 341L1256 340z"/>
<path style="fill:#61cb9a; stroke:none;" d="M726.667 341.333C726.222 341.778 727.278 341.722 727.333 341.667C727.777 341.223 726.722 341.278 726.667 341.333z"/>
<path style="fill:#6cb48f; stroke:none;" d="M835 341L836 342L835 341z"/>
<path style="fill:#59b687; stroke:none;" d="M249 342L250 343L249 342z"/>
<path style="fill:#80bd9b; stroke:none;" d="M725 342L726 343L725 342z"/>
<path style="fill:#e7feec; stroke:none;" d="M720 345C723.474 346.355 726.82 344.921 729 342C725.792 342.571 723.011 343.772 720 345z"/>
<path style="fill:#9de9c3; stroke:none;" d="M834 342L835 343L834 342z"/>
<path style="fill:#69c69a; stroke:none;" d="M1257 342L1258 343L1257 342z"/>
<path style="fill:#c6fbdf; stroke:none;" d="M1258.33 342.667C1258.28 342.722 1258.22 343.778 1258.67 343.333C1258.72 343.278 1258.78 342.222 1258.33 342.667z"/>
<path style="fill:#85c5a3; stroke:none;" d="M248 343L249 344L248 343z"/>
<path style="fill:#66d19b; stroke:none;" d="M723 343L724 344L723 343z"/>
<path style="fill:#8fe4ba; stroke:none;" d="M247 344L248 345L247 344z"/>
<path style="fill:#85c0a0; stroke:none;" d="M721 344L722 345L721 344z"/>
<path style="fill:#aae3c0; stroke:none;" d="M833 344L834 345L833 344z"/>
<path style="fill:#b0e2c7; stroke:none;" d="M246 345L247 346L246 345z"/>
<path style="fill:#4eba7e; stroke:none;" d="M719 345L720 346L719 345z"/>
<path style="fill:#6ea784; stroke:none;" d="M833 345L834 346L833 345z"/>
<path style="fill:#b6e2c9; stroke:none;" d="M1259 345L1260 346L1259 345z"/>
<path style="fill:#1d945c; stroke:none;" d="M246 346C244.14 348.782 243.058 350.654 243 354C244.848 351.504 246.465 349.115 246 346z"/>
<path style="fill:#73a687; stroke:none;" d="M717 346L718 347L717 346z"/>
<path style="fill:#e0f8ea; stroke:none;" d="M718 346C716.401 347.481 715.892 348.061 715 350C717.025 348.803 718.101 348.348 718 346z"/>
<path style="fill:#9febbd; stroke:none;" d="M832 346L833 347L832 346z"/>
<path style="fill:#60ac7e; stroke:none;" d="M832 347L833 348L832 347z"/>
<path style="fill:#5bb788; stroke:none;" d="M244 348L245 349L244 348z"/>
<path style="fill:#b9ffe4; stroke:none;" d="M714 348L712 349C713.482 349.791 714.797 349.87 714 348z"/>
<path style="fill:#a7d1c3; stroke:none;" d="M831 348L832 349L831 348z"/>
<path style="fill:#93d0af; stroke:none;" d="M243 349L244 350L243 349z"/>
<path style="fill:#4b7e6d; stroke:none;" d="M831 349L831 352C831.696 350.447 831.696 350.554 831 349z"/>
<path style="fill:#a6e1c5; stroke:none;" d="M1261 349L1262 350L1261 349z"/>
<path style="fill:#95be9e; stroke:none;" d="M710 350L711 351L710 350z"/>
<path style="fill:#a8d2c4; stroke:none;" d="M830 350L831 351L830 350z"/>
<path style="fill:#3c8864; stroke:none;" d="M1261.33 350.667C1261.28 350.722 1261.22 351.778 1261.67 351.333C1261.72 351.277 1261.78 350.223 1261.33 350.667z"/>
<path style="fill:#67cc92; stroke:none;" d="M708 351L709 352L708 351z"/>
<path style="fill:#7aa496; stroke:none;" d="M830 351L831 352L830 351z"/>
<path style="fill:#ced3cc; stroke:none;" d="M1262 351L1263 352L1262 351z"/>
<path style="fill:#a6ffd9; stroke:none;" d="M240 352L241 353L240 352z"/>
<path style="fill:#456f61; stroke:none;" d="M704 354L707 353C705.222 352.044 704.979 352.234 704 354z"/>
<path style="fill:#b6d7cc; stroke:none;" d="M707 352L708 353L707 352z"/>
<path style="fill:#83f1b4; stroke:none;" d="M1263.33 352.667C1263.28 352.722 1263.22 353.778 1263.67 353.333C1263.72 353.278 1263.78 352.222 1263.33 352.667z"/>
<path style="fill:#60d193; stroke:none;" d="M240 353L241 354L240 353z"/>
<path style="fill:#a2ccbe; stroke:none;" d="M705 353L706 354L705 353z"/>
<path style="fill:#6ebd90; stroke:none;" d="M829 353L830 354L829 353z"/>
<path style="fill:#89d6b6; stroke:none;" d="M239 354L240 355L239 354z"/>
<path style="fill:#228455; stroke:none;" d="M700 356L703 355C701.222 354.045 700.979 354.234 700 356z"/>
<path style="fill:#9ed4b4; stroke:none;" d="M703 354L704 355L703 354z"/>
<path style="fill:#b4e4ce; stroke:none;" d="M238 355L239 356L238 355z"/>
<path style="fill:#6ab68f; stroke:none;" d="M701 355L702 356L701 355z"/>
<path style="fill:#83c09e; stroke:none;" d="M828 355L829 356L828 355z"/>
<path style="fill:#37c088; stroke:none;" d="M238 356L239 357L238 356z"/>
<path style="fill:#62b392; stroke:none;" d="M1264 356L1265 357L1264 356z"/>
<path style="fill:#71b093; stroke:none;" d="M237 357L238 358L237 357z"/>
<path style="fill:#75d5a7; stroke:none;" d="M698 357L699 358L698 357z"/>
<path style="fill:#97d7b5; stroke:none;" d="M827 357L828 358L827 357z"/>
<path style="fill:#84caa5; stroke:none;" d="M236 358L237 359L236 358z"/>
<path style="fill:#159451; stroke:none;" d="M689 362C691.741 361.069 693.104 360.182 695 358C692.056 358.71 690.608 359.427 689 362z"/>
<path style="fill:#71a485; stroke:none;" d="M695.667 358.333C695.222 358.778 696.278 358.722 696.333 358.667C696.778 358.222 695.722 358.278 695.667 358.333z"/>
<path style="fill:#79cea7; stroke:none;" d="M1265 358L1266 359L1265 358z"/>
<path style="fill:#badac5; stroke:none;" d="M235 359L236 360L235 359z"/>
<path style="fill:#4ea676; stroke:none;" d="M694 359L695 360L694 359z"/>
<path style="fill:#9ddfbb; stroke:none;" d="M695 359L696 360L695 359z"/>
<path style="fill:#9df5cd; stroke:none;" d="M234 360L235 361L234 360z"/>
<path style="fill:#a3d0b3; stroke:none;" d="M693 360L694 361L693 360z"/>
<path style="fill:#389568; stroke:none;" d="M826.333 360.667C826.278 360.722 826.222 361.778 826.667 361.333C826.723 361.277 826.778 360.222 826.333 360.667z"/>
<path style="fill:#90dbb2; stroke:none;" d="M1266 360L1267 361L1266 360z"/>
<path style="fill:#5fb78f; stroke:none;" d="M234 361L235 362L234 361z"/>
<path style="fill:#5dc18d; stroke:none;" d="M691 361L692 362L691 361z"/>
<path style="fill:#55a077; stroke:none;" d="M1266 361L1267 362L1266 361z"/>
<path style="fill:#6bc69b; stroke:none;" d="M233 362L234 363L233 362z"/>
<path style="fill:#64a084; stroke:none;" d="M689 362L690 363L689 362z"/>
<path style="fill:#a0dcc0; stroke:none;" d="M690 362L691 363L690 362z"/>
<path style="fill:#d8fff8; stroke:none;" d="M689 363C690.506 363.683 691.315 363.826 693 364C691.276 362.944 691.079 362.964 689 363z"/>
<path style="fill:#74ca9d; stroke:none;" d="M825 362L826 363L825 362z"/>
<path style="fill:#9bc7b0; stroke:none;" d="M232 363L233 364L232 363z"/>
<path style="fill:#72c099; stroke:none;" d="M688 363L689 364L688 363z"/>
<path style="fill:#6eb68e; stroke:none;" d="M1267 363L1268 364L1267 363z"/>
<path style="fill:#a7eccf; stroke:none;" d="M231 364L232 365L231 364z"/>
<path style="fill:#299460; stroke:none;" d="M681 368L687 365C683.714 363.669 680.931 364.135 681 368z"/>
<path style="fill:#cff6e1; stroke:none;" d="M823 364L822 370C823.455 368.079 824.203 366.289 825 364L823 364z"/>
<path style="fill:#037e45; stroke:none;" d="M681.667 365.333C681.222 365.778 682.278 365.722 682.333 365.667C682.778 365.222 681.722 365.278 681.667 365.333z"/>
<path style="fill:#89d5ae; stroke:none;" d="M685 365L686 366L685 365z"/>
<path style="fill:#5fba8e; stroke:none;" d="M824 365L825 366L824 365z"/>
<path style="fill:#83c4a2; stroke:none;" d="M1268 365L1269 366L1268 365z"/>
<path style="fill:#60c69f; stroke:none;" d="M230 366L231 367L230 366z"/>
<path style="fill:#98d3bf; stroke:none;" d="M229 367L230 368L229 367z"/>
<path style="fill:#7dddaf; stroke:none;" d="M682 367L683 368L682 367z"/>
<path style="fill:#f1ffff; stroke:none;" d="M226 368C224.483 368.957 223.661 369.387 222 370C222.053 375.654 215.093 378.513 218 384C220.956 379.655 227.463 373.512 226 368z"/>
<path style="fill:#c4ffe4; stroke:none;" d="M227 368L227 370C228.635 369.455 228.455 369.635 229 368L227 368z"/>
<path style="fill:#37895b; stroke:none;" d="M677 368L678 370C679.635 369.455 679.455 369.635 680 368L677 368z"/>
<path style="fill:#8ab89c; stroke:none;" d="M680 368L681 369L680 368z"/>
<path style="fill:#deffed; stroke:none;" d="M821 368C820.062 371.336 819.283 374.542 819 378C821.259 374.964 822.409 371.619 821 368z"/>
<path style="fill:#37a371; stroke:none;" d="M1269.33 368.667C1269.28 368.722 1269.22 369.778 1269.67 369.333C1269.72 369.278 1269.78 368.222 1269.33 368.667z"/>
<path style="fill:#169455; stroke:none;" d="M673 372C675.434 371.349 676.283 370.84 678 369C675.457 369.526 674.465 369.878 673 372z"/>
<path style="fill:#c7f5d9; stroke:none;" d="M679.667 369.333C679.222 369.778 680.278 369.722 680.333 369.667C680.778 369.223 679.722 369.278 679.667 369.333z"/>
<path style="fill:#e9fff6; stroke:none;" d="M684 369C679.172 370.57 675.262 372.14 671 375C674.389 376.558 684.687 373.198 684 369z"/>
<path style="fill:#6dc298; stroke:none;" d="M227 370L228 371L227 370z"/>
<path style="fill:#149753; stroke:none;" d="M665 370L664 374C666.557 374.754 668.369 375.24 671 375C669.752 374.315 669.452 374.251 668 374L668 370L665 370z"/>
<path style="fill:#66ae89; stroke:none;" d="M677 370L678 371L677 370z"/>
<path style="fill:#6ac292; stroke:none;" d="M822 370L823 371L822 370z"/>
<path style="fill:#8ccdad; stroke:none;" d="M1270 370L1271 371L1270 370z"/>
<path style="fill:#9ed5b8; stroke:none;" d="M226 371L227 372L226 371z"/>
<path style="fill:#238054; stroke:none;" d="M225 375C227.001 373.785 227.785 373.001 229 371C226.751 372.154 226.154 372.751 225 375z"/>
<path style="fill:#91eabc; stroke:none;" d="M676 371L677 372L676 371z"/>
<path style="fill:#228d59; stroke:none;" d="M1270 371C1270.11 373.442 1270.43 375.623 1271 378C1271.97 375.294 1271.47 373.45 1270 371z"/>
<path style="fill:#348864; stroke:none;" d="M672 374L675 373C673.222 372.044 672.979 372.234 672 374z"/>
<path style="fill:#9cdfc0; stroke:none;" d="M673 373L674 374L673 373z"/>
<path style="fill:#80c6a2; stroke:none;" d="M821 373L822 374L821 373z"/>
<path style="fill:#97c3aa; stroke:none;" d="M1271 373L1272 374L1271 373z"/>
<path style="fill:#6cd8a6; stroke:none;" d="M224 374L225 375L224 374z"/>
<path style="fill:#5fa37c; stroke:none;" d="M671 374L672 375L671 374z"/>
<path style="fill:#6bce98; stroke:none;" d="M670 375L671 376L670 375z"/>
<path style="fill:#569c77; stroke:none;" d="M668 376L669 377L668 376z"/>
<path style="fill:#429d71; stroke:none;" d="M820.333 376.667C820.278 376.722 820.222 377.778 820.667 377.333C820.723 377.277 820.778 376.222 820.333 376.667z"/>
<path style="fill:#54a079; stroke:none;" d="M1272.33 376.667C1272.28 376.722 1272.22 377.778 1272.67 377.333C1272.72 377.278 1272.78 376.222 1272.33 376.667z"/>
<path style="fill:#7cb290; stroke:none;" d="M222 377L223 378L222 377z"/>
<path style="fill:#63c997; stroke:none;" d="M667 377L668 378L667 377z"/>
<path style="fill:#83d9ac; stroke:none;" d="M221 378L222 379L221 378z"/>
<path style="fill:#139555; stroke:none;" d="M656 378C656.389 380.739 656.987 382.1 659 384L663 381C660.751 379.579 658.586 378.618 656 378z"/>
<path style="fill:#99ceb0; stroke:none;" d="M666 378L667 379L666 378z"/>
<path style="fill:#e6fff8; stroke:none;" d="M667 378C665.39 379.327 664.299 380.383 663 382C665.253 381.168 667.441 380.54 667 378z"/>
<path style="fill:#d6fff0; stroke:none;" d="M818.333 378.667C818.278 378.722 818.222 379.778 818.667 379.333C818.723 379.277 818.777 378.223 818.333 378.667z"/>
<path style="fill:#97edc6; stroke:none;" d="M819 378L820 379L819 378z"/>
<path style="fill:#40b17d; stroke:none;" d="M664 379L665 380L664 379z"/>
<path style="fill:#67bd96; stroke:none;" d="M819 379L820 380L819 379z"/>
<path style="fill:#8dd3ae; stroke:none;" d="M1273 379L1274 380L1273 379z"/>
<path style="fill:#51b181; stroke:none;" d="M220 380L221 381L220 380z"/>
<path style="fill:#8bccae; stroke:none;" d="M663 380L664 381L663 380z"/>
<path style="fill:#b1f4d5; stroke:none;" d="M818 380L818 383C818.696 381.446 818.696 381.554 818 380z"/>
<path style="fill:#1c8f54; stroke:none;" d="M819 380L817 390C821.175 389.105 820.802 383.127 819 380z"/>
<path style="fill:#287f52; stroke:none;" d="M1273 380L1273 384C1273.71 382.24 1273.71 381.76 1273 380z"/>
<path style="fill:#79bf9d; stroke:none;" d="M219 381L220 382L219 381z"/>
<path style="fill:#41b17f; stroke:none;" d="M661 381L662 382L661 381z"/>
<path style="fill:#8feabf; stroke:none;" d="M662 381L663 382L662 381z"/>
<path style="fill:#78b190; stroke:none;" d="M660 382L661 383L660 382z"/>
<path style="fill:#9bc7ac; stroke:none;" d="M1274 382L1275 383L1274 382z"/>
<path style="fill:#7bd6aa; stroke:none;" d="M659 383L660 384L659 383z"/>
<path style="fill:#6b977c; stroke:none;" d="M1274 383L1275 384L1274 383z"/>
<path style="fill:#339a63; stroke:none;" d="M217.333 384.667C217.278 384.722 217.222 385.778 217.667 385.333C217.722 385.278 217.778 384.222 217.333 384.667z"/>
<path style="fill:#325b4d; stroke:none;" d="M656 384L657 385L656 384z"/>
<path style="fill:#7c9c91; stroke:none;" d="M657 384L658 385L657 384z"/>
<path style="fill:#d3ffe5; stroke:none;" d="M816 384L816 389C816.924 387.352 817.449 385.825 818 384L816 384z"/>
<path style="fill:#b1ecce; stroke:none;" d="M1275.33 384.667C1275.28 384.722 1275.22 385.778 1275.67 385.333C1275.72 385.278 1275.78 384.222 1275.33 384.667z"/>
<path style="fill:#91c9a6; stroke:none;" d="M216 385L217 386L216 385z"/>
<path style="fill:#bcdcd1; stroke:none;" d="M656.667 385.333C656.222 385.778 657.278 385.722 657.333 385.667C657.778 385.222 656.722 385.278 656.667 385.333z"/>
<path style="fill:#a2ebc0; stroke:none;" d="M817 385L818 386L817 385z"/>
<path style="fill:#278759; stroke:none;" d="M652 386L653 388C654.635 387.455 654.455 387.635 655 386L652 386z"/>
<path style="fill:#a1cfb5; stroke:none;" d="M655 386L656 387L655 386z"/>
<path style="fill:#eefffa; stroke:none;" d="M772 392C772 394.085 772.138 393.724 770 394C770.062 395.739 769.522 396.375 769 398C773.675 395.916 777.32 392.521 781 389C782.972 391.91 783.842 388.089 784 386C779.439 387.141 776.516 390.841 772 392z"/>
<path style="fill:#68c090; stroke:none;" d="M817 386L818 387L817 386z"/>
<path style="fill:#7dc09f; stroke:none;" d="M1275 386L1276 387L1275 386z"/>
<path style="fill:#65a781; stroke:none;" d="M215 387L216 388L215 387z"/>
<path style="fill:#6dd3a3; stroke:none;" d="M214 388L215 389L214 388z"/>
<path style="fill:#5ba37e; stroke:none;" d="M652 388L653 389L652 388z"/>
<path style="fill:#b1e8c9; stroke:none;" d="M213 389L214 390L213 389z"/>
<path style="fill:#63cf9d; stroke:none;" d="M651 389L652 390L651 389z"/>
<path style="fill:#bad4c9; stroke:none;" d="M780 389C780.031 390.832 779.92 390.559 781 392C780.749 390.549 780.685 390.248 780 389z"/>
<path style="fill:#9ae9bc; stroke:none;" d="M816 389L817 390L816 389z"/>
<path style="fill:#bed4c7; stroke:none;" d="M1276 389L1277 390L1276 389z"/>
<path style="fill:#49b885; stroke:none;" d="M213 390L214 391L213 390z"/>
<path style="fill:#349a68; stroke:none;" d="M648 390L648 392C649.635 391.455 649.455 391.635 650 390L648 390z"/>
<path style="fill:#9bd2b5; stroke:none;" d="M650 390L651 391L650 390z"/>
<path style="fill:#52bd87; stroke:none;" d="M816 390L817 391L816 390z"/>
<path style="fill:#71bd99; stroke:none;" d="M212 391L213 392L212 391z"/>
<path style="fill:#7ea092; stroke:none;" d="M778.667 391.333C778.222 391.778 779.278 391.722 779.333 391.667C779.777 391.223 778.722 391.278 778.667 391.333z"/>
<path style="fill:#93efc6; stroke:none;" d="M211 392L212 393L211 392z"/>
<path style="fill:#0f9057; stroke:none;" d="M212 392C209.738 395.863 208.767 399.088 214 400C213.546 397.254 213.061 394.564 212 392z"/>
<path style="fill:#238b56; stroke:none;" d="M643 396C644.753 394.67 645.67 393.753 647 392C644.751 393.154 644.154 393.751 643 396z"/>
<path style="fill:#629d7f; stroke:none;" d="M647 392L648 393L647 392z"/>
<path style="fill:#38906a; stroke:none;" d="M770 402C773.634 399.221 776.421 395.772 779 392C774.338 393.974 770.23 396.641 770 402z"/>
<path style="fill:#e4fff5; stroke:none;" d="M779 392C777.427 394.115 776.648 395.469 776 398L780 396C779.807 394.474 779.631 393.372 779 392z"/>
<path style="fill:#71c299; stroke:none;" d="M646 393L647 394L646 393z"/>
<path style="fill:#abd5c7; stroke:none;" d="M815 393L816 394L815 393z"/>
<path style="fill:#a2e5c6; stroke:none;" d="M1277 393L1278 394L1277 393z"/>
<path style="fill:#5bb68a; stroke:none;" d="M210 394L211 395L210 394z"/>
<path style="fill:#edfff7; stroke:none;" d="M645 394C643.029 395.941 641.355 397.61 640 400L647 400C646.232 398.686 646.314 398.768 645 398C645.309 396.242 645.406 395.659 645 394z"/>
<path style="fill:#96dcc0; stroke:none;" d="M774 394L775 395L774 394z"/>
<path style="fill:#278465; stroke:none;" d="M815 394L815 400C815.951 397.715 815.951 396.285 815 394z"/>
<path style="fill:#44a175; stroke:none;" d="M1277.33 394.667C1277.28 394.722 1277.22 395.778 1277.67 395.333C1277.72 395.278 1277.78 394.223 1277.33 394.667z"/>
<path style="fill:#b7dac2; stroke:none;" d="M209 395L210 396L209 395z"/>
<path style="fill:#139451; stroke:none;" d="M635 402C637.909 400.312 639.95 398.663 642 396C637.496 395.834 635.696 397.6 635 402z"/>
<path style="fill:#5fa581; stroke:none;" d="M642 396L643 397L642 396z"/>
<path style="fill:#c3ffee; stroke:none;" d="M775 396L773 400C774.86 398.734 775.369 398.223 775 396z"/>
<path style="fill:#63bb91; stroke:none;" d="M208 397L209 398L208 397z"/>
<path style="fill:#78d9ac; stroke:none;" d="M641 397L642 398L641 397z"/>
<path style="fill:#7cc2aa; stroke:none;" d="M814 397L814 400C814.696 398.446 814.696 398.554 814 397z"/>
<path style="fill:#b4d2c8; stroke:none;" d="M207 398L208 399L207 398z"/>
<path style="fill:#9ee8c5; stroke:none;" d="M769 398L770 399L769 398z"/>
<path style="fill:#dbfffd; stroke:none;" d="M782 398L782 400L784 400L784 398L782 398z"/>
<path style="fill:#7daa8d; stroke:none;" d="M1278.33 398.667C1278.28 398.722 1278.22 399.778 1278.67 399.333C1278.72 399.278 1278.78 398.222 1278.33 398.667z"/>
<path style="fill:#6b897f; stroke:none;" d="M207 399L208 400L207 399z"/>
<path style="fill:#7ff0b2; stroke:none;" d="M639 399L640 400L639 399z"/>
<path style="fill:#94c0a7; stroke:none;" d="M768 399L769 400L768 399z"/>
<path style="fill:#169254; stroke:none;" d="M206 400L203 406C205.035 407.226 205.326 407.703 206 410C208.465 406.746 208.465 403.254 206 400z"/>
<path style="fill:#709d88; stroke:none;" d="M637 400L638 401L637 400z"/>
<path style="fill:#cbd7cd; stroke:none;" d="M638 400L639 401L638 400z"/>
<path style="fill:#dfffee; stroke:none;" d="M763 400L763 404C764.753 402.67 765.67 401.752 767 400L763 400z"/>
<path style="fill:#1a9964; stroke:none;" d="M766 401C767.458 402.081 768.248 402.436 770 403C769.386 400.149 768.697 400.146 766 401z"/>
<path style="fill:#b1fed2; stroke:none;" d="M813 400L813 403C813.696 401.446 813.696 401.554 813 400z"/>
<path style="fill:#218551; stroke:none;" d="M814 400C813.571 405.392 812.112 410.568 812 416C814.461 411.893 815.832 404.491 814 400z"/>
<path style="fill:#129551; stroke:none;" d="M1276 400C1276.17 405.634 1278.95 410.252 1279 416C1281.08 411.035 1279.66 405.187 1279 400L1276 400z"/>
<path style="fill:#a7ffd1; stroke:none;" d="M1279 400L1279 403C1279.7 401.447 1279.7 401.554 1279 400z"/>
<path style="fill:#a1e1be; stroke:none;" d="M205 401L206 402L205 401z"/>
<path style="fill:#6bbb96; stroke:none;" d="M636 401L637 402L636 401z"/>
<path style="fill:#a5e0c2; stroke:none;" d="M771 401L772 402L771 401z"/>
<path style="fill:#87c2a4; stroke:none;" d="M635 402L636 403L635 402z"/>
<path style="fill:#208654; stroke:none;" d="M765 402L762 406L766 408C766.227 405.549 766.116 404.186 765 402z"/>
<path style="fill:#098f50; stroke:none;" d="M766 402L766 406C767.902 404.666 767.966 404.337 768 402L766 402z"/>
<path style="fill:#8bcead; stroke:none;" d="M770 402L771 403L770 402z"/>
<path style="fill:#6ba283; stroke:none;" d="M204 403L205 404L204 403z"/>
<path style="fill:#7bcca3; stroke:none;" d="M634 403L635 404L634 403z"/>
<path style="fill:#79d1a9; stroke:none;" d="M769 403L770 404L769 403z"/>
<path style="fill:#d4ffea; stroke:none;" d="M812 403C811.313 406.7 811.01 410.237 811 414C812.51 410.636 813.358 406.48 812 403z"/>
<path style="fill:#50a878; stroke:none;" d="M813.333 403.667C813.278 403.722 813.222 404.778 813.667 404.333C813.723 404.277 813.778 403.222 813.333 403.667z"/>
<path style="fill:#6cd59a; stroke:none;" d="M1279 403L1280 404L1279 403z"/>
<path style="fill:#a6f8d2; stroke:none;" d="M203 404L204 405L203 404z"/>
<path style="fill:#4f936c; stroke:none;" d="M632 404L633 405L632 404z"/>
<path style="fill:#adcfb7; stroke:none;" d="M633 404L634 405L633 404z"/>
<path style="fill:#afecca; stroke:none;" d="M761 404L761 406C762.635 405.455 762.455 405.635 763 404L761 404z"/>
<path style="fill:#62a886; stroke:none;" d="M768 404L769 405L768 404z"/>
<path style="fill:#53c393; stroke:none;" d="M202 406L203 407L202 406z"/>
<path style="fill:#45b07a; stroke:none;" d="M629 406L629 408C630.635 407.455 630.455 407.635 631 406L629 406z"/>
<path style="fill:#a6dcbc; stroke:none;" d="M631 406L632 407L631 406z"/>
<path style="fill:#68d7a4; stroke:none;" d="M760 406L761 407L760 406z"/>
<path style="fill:#049b4c; stroke:none;" d="M719 461C713.015 458.97 710.268 465.207 708 470L712 470C712.106 473.586 713.232 476.553 714 480L721 480L721 474C728.667 474 728.13 460.838 732 456L733 457L733 451L735 452L735 448C739.458 446.68 747.706 437.18 743 433C744.801 432.438 746.122 432.176 748 432C748.675 427.749 752.251 425.932 753 422C758.659 419.241 763.038 412.914 765 407C758.63 406.1 753.941 412.857 749.83 417.089C739.943 427.269 730.297 437.117 723.985 449.896C721.855 454.206 718.45 455.777 719 461z"/>
<path style="fill:#dcfff8; stroke:none;" d="M1280 406C1281 411.329 1281.95 416.565 1282 422C1283.87 417.537 1284 409.235 1280 406z"/>
<path style="fill:#9decbf; stroke:none;" d="M630 407L631 408L630 407z"/>
<path style="fill:#94d7b6; stroke:none;" d="M759 407L760 408L759 407z"/>
<path style="fill:#94eabd; stroke:none;" d="M766 407L767 408L766 407z"/>
<path style="fill:#70ac88; stroke:none;" d="M628 408L629 409L628 408z"/>
<path style="fill:#76c79e; stroke:none;" d="M758 408L759 409L758 408z"/>
<path style="fill:#85c2a0; stroke:none;" d="M765 408L766 409L765 408z"/>
<path style="fill:#77c39c; stroke:none;" d="M812.333 408.667C812.278 408.722 812.222 409.778 812.667 409.333C812.723 409.277 812.778 408.222 812.333 408.667z"/>
<path style="fill:#86bba9; stroke:none;" d="M1280.33 408.667C1280.28 408.722 1280.22 409.778 1280.67 409.333C1280.72 409.277 1280.78 408.223 1280.33 408.667z"/>
<path style="fill:#fdfffe; stroke:none;" d="M1290 408C1289.95 412.549 1288.19 416.542 1288.02 421.015C1287.61 432.027 1289.72 443.018 1289.99 454C1290.17 461.798 1288 469.157 1288 477C1285.74 472.774 1286 468.706 1286 464L1284 464C1284 478.86 1280.12 493.201 1280 508C1283.12 507.544 1284.53 506.815 1286 504C1286.81 506.729 1286.98 509.15 1287 512L1296 512C1296 502.747 1296.6 493.267 1295.95 484.039C1295.65 479.887 1294 476.268 1294 472C1294 467.436 1295.81 463.467 1295.98 458.985C1296.19 453.267 1294.44 448.497 1294.05 442.985C1293.24 431.288 1294.48 419.676 1293 408L1290 408z"/>
<path style="fill:#e2fef0; stroke:none;" d="M198 409C196.943 411.243 196.819 411.577 197 414C198.274 412.659 199.041 411.597 200 410L198 409z"/>
<path style="fill:#92c2aa; stroke:none;" d="M200 409L201 410L200 409z"/>
<path style="fill:#6cc293; stroke:none;" d="M627 409L628 410L627 409z"/>
<path style="fill:#7aa48c; stroke:none;" d="M757 409L758 410L757 409z"/>
<path style="fill:#57ba8b; stroke:none;" d="M764 409L765 410L764 409z"/>
<path style="fill:#86bd9d; stroke:none;" d="M626 410L627 411L626 410z"/>
<path style="fill:#6bc399; stroke:none;" d="M756 410L757 411L756 410z"/>
<path style="fill:#287e61; stroke:none;" d="M1280 410L1280 416C1280.95 413.715 1280.95 412.285 1280 410z"/>
<path style="fill:#5d9c7d; stroke:none;" d="M199 411L200 412L199 411z"/>
<path style="fill:#80cda3; stroke:none;" d="M625 411L626 412L625 411z"/>
<path style="fill:#6fa78a; stroke:none;" d="M755 411L756 412L755 411z"/>
<path style="fill:#89e1b9; stroke:none;" d="M198 412L199 413L198 412z"/>
<path style="fill:#248a58; stroke:none;" d="M619 412L619 417C621.136 415.451 622.451 414.136 624 412L619 412z"/>
<path style="fill:#64c194; stroke:none;" d="M754 412L755 413L754 412z"/>
<path style="fill:#a4d6b9; stroke:none;" d="M762 412L763 413L762 412z"/>
<path style="fill:#229963; stroke:none;" d="M197 416C199.124 415.101 199.101 415.124 200 413C198.077 413.901 198.001 414.076 197 416z"/>
<path style="fill:#90e5bc; stroke:none;" d="M623 413L624 414L623 413z"/>
<path style="fill:#71b491; stroke:none;" d="M753 413L754 414L753 413z"/>
<path style="fill:#69b68c; stroke:none;" d="M761 413L762 414L761 413z"/>
<path style="fill:#78c5a3; stroke:none;" d="M197 414L198 415L197 414z"/>
<path style="fill:#11925c; stroke:none;" d="M617 414C615.896 415.457 615.457 415.896 614 417L615 420C617.814 418.528 618.544 417.122 619 414L617 414z"/>
<path style="fill:#8cdfb5; stroke:none;" d="M622 414L623 415L622 414z"/>
<path style="fill:#cafff3; stroke:none;" d="M623 414L621 415C622.482 415.791 623.796 415.87 623 414z"/>
<path style="fill:#caffe1; stroke:none;" d="M760 416C761.58 415.316 761.777 415.195 763 414C761.42 414.684 761.223 414.805 760 416z"/>
<path style="fill:#93cbb0; stroke:none;" d="M811.333 414.667C811.278 414.722 811.222 415.778 811.667 415.333C811.723 415.277 811.778 414.222 811.333 414.667z"/>
<path style="fill:#9fdec9; stroke:none;" d="M1281.33 414.667C1281.28 414.722 1281.22 415.778 1281.67 415.333C1281.72 415.278 1281.78 414.222 1281.33 414.667z"/>
<path style="fill:#badccc; stroke:none;" d="M196 415L197 416L196 415z"/>
<path style="fill:#889f97; stroke:none;" d="M751 415L752 416L751 415z"/>
<path style="fill:#dffeec; stroke:none;" d="M194 416L193 420C194.434 418.607 195.045 417.767 196 416L194 416z"/>
<path style="fill:#21945d; stroke:none;" d="M196 416L192 424C195.293 422.228 197.099 419.773 196 416z"/>
<path style="fill:#dbfffa; stroke:none;" d="M620 416L617 420L621 420C620.807 418.474 620.631 417.372 620 416z"/>
<path style="fill:#adfbd1; stroke:none;" d="M749 416L750 417L749 416z"/>
<path style="fill:#37c482; stroke:none;" d="M750 416L751 417L750 416z"/>
<path style="fill:#c5ffe5; stroke:none;" d="M759.333 416.667C759.278 416.722 759.222 417.778 759.667 417.333C759.723 417.277 759.778 416.222 759.333 416.667z"/>
<path style="fill:#d5ffea; stroke:none;" d="M810 416L810 422C810.951 419.715 810.951 418.285 810 416z"/>
<path style="fill:#54a67e; stroke:none;" d="M811.333 416.667C811.278 416.722 811.222 417.778 811.667 417.333C811.723 417.277 811.778 416.222 811.333 416.667z"/>
<path style="fill:#51ba82; stroke:none;" d="M1281.33 416.667C1281.28 416.722 1281.22 417.778 1281.67 417.333C1281.72 417.278 1281.78 416.222 1281.33 416.667z"/>
<path style="fill:#78be9a; stroke:none;" d="M195 417L196 418L195 417z"/>
<path style="fill:#5dab81; stroke:none;" d="M749 417L750 418L749 417z"/>
<path style="fill:#6ec59b; stroke:none;" d="M758 417L759 418L758 417z"/>
<path style="fill:#53a07c; stroke:none;" d="M617 418L618 419L617 418z"/>
<path style="fill:#e4fffb; stroke:none;" d="M742 424L748 419C744.212 417.721 743 420.74 742 424z"/>
<path style="fill:#76c198; stroke:none;" d="M748 418L749 419L748 418z"/>
<path style="fill:#6aa285; stroke:none;" d="M194 419L195 420L194 419z"/>
<path style="fill:#70a587; stroke:none;" d="M747 419L748 420L747 419z"/>
<path style="fill:#8ae2b8; stroke:none;" d="M193 420L194 421L193 420z"/>
<path style="fill:#65b18a; stroke:none;" d="M615 420L616 421L615 420z"/>
<path style="fill:#e8fff2; stroke:none;" d="M616 420L613 424C615.303 423.383 616.372 422.743 618 421L616 420z"/>
<path style="fill:#a2ffd7; stroke:none;" d="M745 420L746 421L745 420z"/>
<path style="fill:#5dc796; stroke:none;" d="M746 420L747 421L746 420z"/>
<path style="fill:#94cdac; stroke:none;" d="M756 420L757 421L756 420z"/>
<path style="fill:#5fca94; stroke:none;" d="M614 421L615 422L614 421z"/>
<path style="fill:#5ab98f; stroke:none;" d="M745 421L746 422L745 421z"/>
<path style="fill:#5bae82; stroke:none;" d="M755 421L756 422L755 421z"/>
<path style="fill:#dcffee; stroke:none;" d="M754 426C755.627 424.412 756.774 422.922 758 421C755.453 422.372 754.812 423.218 754 426z"/>
<path style="fill:#5bc998; stroke:none;" d="M192 422L193 423L192 422z"/>
<path style="fill:#71bd96; stroke:none;" d="M613 422L614 423L613 422z"/>
<path style="fill:#57b789; stroke:none;" d="M744 422L745 423L744 422z"/>
<path style="fill:#3c8d62; stroke:none;" d="M753.667 422.333C753.222 422.778 754.278 422.722 754.333 422.667C754.777 422.223 753.722 422.278 753.667 422.333z"/>
<path style="fill:#9fdebf; stroke:none;" d="M810.333 422.667C810.278 422.722 810.222 423.778 810.667 423.333C810.723 423.277 810.778 422.222 810.333 422.667z"/>
<path style="fill:#a6d7b9; stroke:none;" d="M1282.33 422.667C1282.28 422.722 1282.22 423.778 1282.67 423.333C1282.72 423.278 1282.78 422.222 1282.33 422.667z"/>
<path style="fill:#53ba85; stroke:none;" d="M612 423L613 424L612 423z"/>
<path style="fill:#95d8b5; stroke:none;" d="M743 423L744 424L743 423z"/>
<path style="fill:#418963; stroke:none;" d="M191.333 424.667C191.278 424.722 191.222 425.778 191.667 425.333C191.722 425.278 191.778 424.222 191.333 424.667z"/>
<path style="fill:#1a9159; stroke:none;" d="M605 424C605.098 426.74 603.083 428.838 603 432C605.391 430.645 607.059 428.971 609 427C607.55 425.393 606.948 424.936 605 424z"/>
<path style="fill:#77ba97; stroke:none;" d="M611 424L612 425L611 424z"/>
<path style="fill:#e3fffb; stroke:none;" d="M739 424L738 429C739.752 427.41 740.794 426.046 742 424L739 424z"/>
<path style="fill:#83e5b4; stroke:none;" d="M742 424L743 425L742 424z"/>
<path style="fill:#a8ebca; stroke:none;" d="M753.333 424.667C753.278 424.722 753.222 425.778 753.667 425.333C753.723 425.277 753.777 424.222 753.333 424.667z"/>
<path style="fill:#75c19b; stroke:none;" d="M810 424L810 427C810.696 425.446 810.696 425.554 810 424z"/>
<path style="fill:#81bd9b; stroke:none;" d="M1282 424L1282 427C1282.7 425.446 1282.7 425.554 1282 424z"/>
<path style="fill:#95c3a9; stroke:none;" d="M190 425L191 426L190 425z"/>
<path style="fill:#62bf93; stroke:none;" d="M610 425L611 426L610 425z"/>
<path style="fill:#8be2b8; stroke:none;" d="M741 425L742 426L741 425z"/>
<path style="fill:#4fb988; stroke:none;" d="M752 425L753 426L752 425z"/>
<path style="fill:#def9ea; stroke:none;" d="M1283 425L1283 432C1284.06 429.466 1284.06 427.534 1283 425z"/>
<path style="fill:#76bca0; stroke:none;" d="M609 426L610 427L609 426z"/>
<path style="fill:#80ffc5; stroke:none;" d="M740 426L741 427L740 426z"/>
<path style="fill:#1c9659; stroke:none;" d="M748 432C749.643 430.114 750.809 428.207 752 426C748.679 427.099 748.263 428.59 748 432z"/>
<path style="fill:#e6fff1; stroke:none;" d="M809 426C807.411 438.575 808 451.328 808 464C811.014 456.817 808.56 446.732 809.089 439C809.394 434.557 810.78 430.244 809 426z"/>
<path style="fill:#6fbb94; stroke:none;" d="M189 427L190 428L189 427z"/>
<path style="fill:#63be9b; stroke:none;" d="M608 427L609 428L608 427z"/>
<path style="fill:#71e4ad; stroke:none;" d="M751 427L752 428L751 427z"/>
<path style="fill:#1c8d57; stroke:none;" d="M810 427L810 466C813.206 458.361 811 446.271 811 438C811 434.297 811.441 430.435 810 427z"/>
<path style="fill:#1f8f55; stroke:none;" d="M1282 427L1282 475C1283.66 471.042 1283 466.26 1283 462L1283 439C1283 435.017 1283.55 430.699 1282 427z"/>
<path style="fill:#a1efc7; stroke:none;" d="M188 428L189 429L188 428z"/>
<path style="fill:#6eaf91; stroke:none;" d="M607 428L608 429L607 428z"/>
<path style="fill:#2c9763; stroke:none;" d="M739 428L736 432C738.144 430.869 739.099 430.432 739 428z"/>
<path style="fill:#63a381; stroke:none;" d="M750 428L751 429L750 428z"/>
<path style="fill:#63b189; stroke:none;" d="M188 429L189 430L188 429z"/>
<path style="fill:#5db88d; stroke:none;" d="M606 429L607 430L606 429z"/>
<path style="fill:#95d1b5; stroke:none;" d="M187 430L188 431L187 430z"/>
<path style="fill:#64b08c; stroke:none;" d="M605 430L606 431L605 430z"/>
<path style="fill:#7bba9b; stroke:none;" d="M749 430L750 431L749 430z"/>
<path style="fill:#35a46f; stroke:none;" d="M186.333 432.667C186.278 432.722 186.222 433.778 186.667 433.333C186.722 433.278 186.778 432.222 186.333 432.667z"/>
<path style="fill:#5b9e7d; stroke:none;" d="M603 432L604 433L603 432z"/>
<path style="fill:#dffff6; stroke:none;" d="M728 440C730.557 437.54 732.902 434.86 735 432C730.607 433.248 728.697 435.496 728 440z"/>
<path style="fill:#278e59; stroke:none;" d="M743 432L746 436L748 432L743 432z"/>
<path style="fill:#c2ffe4; stroke:none;" d="M748.333 432.667C748.278 432.722 748.222 433.778 748.667 433.333C748.723 433.277 748.777 432.223 748.333 432.667z"/>
<path style="fill:#adf0cf; stroke:none;" d="M1283 432L1283 436C1283.71 434.24 1283.71 433.759 1283 432z"/>
<path style="fill:#defff0; stroke:none;" d="M1284 432L1284 464C1286.6 457.806 1285 448.686 1285 442C1285 438.581 1285.33 435.166 1284 432z"/>
<path style="fill:#a6e2c0; stroke:none;" d="M185 433L186 434L185 433z"/>
<path style="fill:#4eab7e; stroke:none;" d="M602 433L603 434L602 433z"/>
<path style="fill:#81c4a5; stroke:none;" d="M734 433L735 434L734 433z"/>
<path style="fill:#7bddae; stroke:none;" d="M747 433L748 434L747 433z"/>
<path style="fill:#088744; stroke:none;" d="M599 434L599 436L601 436L601 434L599 434z"/>
<path style="fill:#579f77; stroke:none;" d="M601 434L602 435L601 434z"/>
<path style="fill:#8edeb7; stroke:none;" d="M733 434L734 435L733 434z"/>
<path style="fill:#9dcdb3; stroke:none;" d="M184 435L185 436L184 435z"/>
<path style="fill:#a1dcc0; stroke:none;" d="M732 435L733 436L732 435z"/>
<path style="fill:#9ae0bc; stroke:none;" d="M809 435L809 443C810.161 440.23 810.161 437.77 809 435z"/>
<path style="fill:#489e6d; stroke:none;" d="M599 436L600 437L599 436z"/>
<path style="fill:#abdebd; stroke:none;" d="M600 436L601 437L600 436z"/>
<path style="fill:#b7fddb; stroke:none;" d="M730 436L730 438C731.635 437.455 731.455 437.635 732 436L730 436z"/>
<path style="fill:#78c49d; stroke:none;" d="M745 436L746 437L745 436z"/>
<path style="fill:#98dfbd; stroke:none;" d="M1283 436L1283 440C1283.71 438.24 1283.71 437.759 1283 436z"/>
<path style="fill:#8bd1ad; stroke:none;" d="M183 437L184 438L183 437z"/>
<path style="fill:#93e9b8; stroke:none;" d="M599 437L600 438L599 437z"/>
<path style="fill:#86d8b0; stroke:none;" d="M182.333 438.667C182.278 438.722 182.222 439.778 182.667 439.333C182.722 439.278 182.778 438.222 182.333 438.667z"/>
<path style="fill:#90d3aa; stroke:none;" d="M598 438L599 439L598 438z"/>
<path style="fill:#9ae4bf; stroke:none;" d="M744 438L745 439L744 438z"/>
<path style="fill:#75d19e; stroke:none;" d="M597 439L598 440L597 439z"/>
<path style="fill:#3da870; stroke:none;" d="M729.333 439.667C729.278 439.722 729.222 440.778 729.667 440.333C729.723 440.277 729.778 439.222 729.333 439.667z"/>
<path style="fill:#43986e; stroke:none;" d="M742 439L742 441C743.635 440.455 743.455 440.635 744 439L742 439z"/>
<path style="fill:#89c19e; stroke:none;" d="M596 440L597 441L596 440z"/>
<path style="fill:#7fcca2; stroke:none;" d="M728 440L729 441L728 440z"/>
<path style="fill:#6ab78d; stroke:none;" d="M1283 440L1283 460C1285.09 455.027 1285.09 444.973 1283 440z"/>
<path style="fill:#66ae88; stroke:none;" d="M181 441L182 442L181 441z"/>
<path style="fill:#66bc8f; stroke:none;" d="M595 441L596 442L595 441z"/>
<path style="fill:#99eec4; stroke:none;" d="M742 441L743 442L742 441z"/>
<path style="fill:#94e2ba; stroke:none;" d="M180 442L181 443L180 442z"/>
<path style="fill:#129551; stroke:none;" d="M587 442L586 448C590.186 447.83 592.227 445.707 594 442L587 442z"/>
<path style="fill:#67b18c; stroke:none;" d="M594 442L595 443L594 442z"/>
<path style="fill:#6bb992; stroke:none;" d="M741 442L742 443L741 442z"/>
<path style="fill:#57a57d; stroke:none;" d="M180 443L181 444L180 443z"/>
<path style="fill:#a9f3ce; stroke:none;" d="M594 443L595 444L594 443z"/>
<path style="fill:#69a684; stroke:none;" d="M726 443L727 444L726 443z"/>
<path style="fill:#64ac87; stroke:none;" d="M809 443L809 451C810.161 448.23 810.161 445.77 809 443z"/>
<path style="fill:#81dcb1; stroke:none;" d="M179 444L180 445L179 444z"/>
<path style="fill:#159854; stroke:none;" d="M180 444C177.472 447.833 175.278 451.573 174 456C179.111 453.806 181.323 449.511 180 444z"/>
<path style="fill:#57977c; stroke:none;" d="M592 444L593 445L592 444z"/>
<path style="fill:#d0f7e4; stroke:none;" d="M723 444L722 448C723.434 446.608 724.045 445.767 725 444L723 444z"/>
<path style="fill:#69cd9b; stroke:none;" d="M725 444L726 445L725 444z"/>
<path style="fill:#188e54; stroke:none;" d="M726 444C721.916 448.792 717.662 454.281 715 460C716.641 460.131 717.456 460.369 719 461C719.34 454.516 726.445 450.64 726 444z"/>
<path style="fill:#97ceaf; stroke:none;" d="M740 444L741 445L740 444z"/>
<path style="fill:#d3f5e4; stroke:none;" d="M177 445L177 448C178.016 446.861 178.309 446.389 179 445L177 445z"/>
<path style="fill:#a9e9ce; stroke:none;" d="M592 445L593 446L592 445z"/>
<path style="fill:#92dcb7; stroke:none;" d="M724 445L725 446L724 445z"/>
<path style="fill:#76c8a2; stroke:none;" d="M178 446L179 447L178 446z"/>
<path style="fill:#74e2a5; stroke:none;" d="M591 446L592 447L591 446z"/>
<path style="fill:#aad6bb; stroke:none;" d="M739 446L740 447L739 446z"/>
<path style="fill:#68df9f; stroke:none;" d="M590 447L591 448L590 447z"/>
<path style="fill:#62c694; stroke:none;" d="M738 447L739 448L738 447z"/>
<path style="fill:#f0fffb; stroke:none;" d="M168 448C168.44 450.201 170.242 453.792 171 450C172.168 454.823 169.965 456.49 167.799 460.514C165.108 465.513 161.799 472.421 161 478C167.491 470.455 171.901 459.062 176 450C173.255 449.019 170.928 448.195 168 448z"/>
<path style="fill:#7eecaf; stroke:none;" d="M176.333 448.667C176.278 448.722 176.222 449.778 176.667 449.333C176.722 449.278 176.778 448.222 176.333 448.667z"/>
<path style="fill:#89bfa7; stroke:none;" d="M589 448L590 449L589 448z"/>
<path style="fill:#57c48d; stroke:none;" d="M722 448L723 449L722 448z"/>
<path style="fill:#367058; stroke:none;" d="M735 448L735 452C736.434 450.607 737.045 449.767 738 448L735 448z"/>
<path style="fill:#4fb98b; stroke:none;" d="M588 449L589 450L588 449z"/>
<path style="fill:#d6fff3; stroke:none;" d="M589 449C587.651 450.768 586.878 451.983 586 454C588.193 452.519 589.637 451.682 589 449z"/>
<path style="fill:#83cba3; stroke:none;" d="M721 449L722 450L721 449z"/>
<path style="fill:#91beaa; stroke:none;" d="M737 449L738 450L737 449z"/>
<path style="fill:#2e9f69; stroke:none;" d="M586 450L586 452C587.635 451.455 587.455 451.635 588 450L586 450z"/>
<path style="fill:#a8ecc5; stroke:none;" d="M720 450L721 451L720 450z"/>
<path style="fill:#aec8bb; stroke:none;" d="M175 451L176 452L175 451z"/>
<path style="fill:#5ea27b; stroke:none;" d="M720 451L721 452L720 451z"/>
<path style="fill:#26824f; stroke:none;" d="M733 451L733 456C734.346 453.824 734.381 453.141 733 451z"/>
<path style="fill:#a0ccbb; stroke:none;" d="M736 451L737 452L736 451z"/>
<path style="fill:#a2e5c2; stroke:none;" d="M809 451L809 458C810.059 455.466 810.059 453.534 809 451z"/>
<path style="fill:#138e4f; stroke:none;" d="M579 452C579.421 454.843 579.939 456.012 582 458C583.218 456.014 584.32 454.61 586 453C583.668 452.015 581.543 452.019 579 452z"/>
<path style="fill:#6abf98; stroke:none;" d="M719 452L720 453L719 452z"/>
<path style="fill:#67a580; stroke:none;" d="M735 452L736 453L735 452z"/>
<path style="fill:#98d0b5; stroke:none;" d="M174 453L175 454L174 453z"/>
<path style="fill:#9ff2c8; stroke:none;" d="M585 453L586 454L585 453z"/>
<path style="fill:#95d0b2; stroke:none;" d="M718 453L719 454L718 453z"/>
<path style="fill:#b3f1cc; stroke:none;" d="M735 453L736 454L735 453z"/>
<path style="fill:#8ad4af; stroke:none;" d="M173.333 454.667C173.278 454.722 173.222 455.778 173.667 455.333C173.722 455.278 173.778 454.222 173.333 454.667z"/>
<path style="fill:#53ac7e; stroke:none;" d="M583 454L583 456C584.635 455.455 584.455 455.635 585 454L583 454z"/>
<path style="fill:#95e8be; stroke:none;" d="M717 454L718 455L717 454z"/>
<path style="fill:#3ea975; stroke:none;" d="M718 454L719 455L718 454z"/>
<path style="fill:#7cb997; stroke:none;" d="M734 454L735 455L734 454z"/>
<path style="fill:#55a87e; stroke:none;" d="M717 455L718 456L717 455z"/>
<path style="fill:#199151; stroke:none;" d="M173 456L170 464C172.85 462.048 173.991 459.357 173 456z"/>
<path style="fill:#e2fff1; stroke:none;" d="M583 456L579 462C582.04 460.96 583.543 459.278 583 456z"/>
<path style="fill:#459b6c; stroke:none;" d="M716.333 456.667C716.278 456.722 716.222 457.778 716.667 457.333C716.723 457.277 716.778 456.222 716.333 456.667z"/>
<path style="fill:#279a63; stroke:none;" d="M731 456C730.009 458.706 729.287 461.132 729 464C731.131 461.404 732.573 459.113 731 456z"/>
<path style="fill:#90c8ad; stroke:none;" d="M733 456L734 457L733 456z"/>
<path style="fill:#7cbc99; stroke:none;" d="M172 457L173 458L172 457z"/>
<path style="fill:#92c9a9; stroke:none;" d="M715 457L716 458L715 457z"/>
<path style="fill:#98cdb3; stroke:none;" d="M732 458L733 459L732 458z"/>
<path style="fill:#c6ffe4; stroke:none;" d="M809 458L809 464C809.951 461.715 809.951 460.285 809 458z"/>
<path style="fill:#7cb896; stroke:none;" d="M171 459L172 460L171 459z"/>
<path style="fill:#1d8b5a; stroke:none;" d="M576 459L576 464C577.959 461.838 578.304 460.908 576 459z"/>
<path style="fill:#6fe6b0; stroke:none;" d="M580 459L581 460L580 459z"/>
<path style="fill:#64af86; stroke:none;" d="M714 459L715 460L714 459z"/>
<path style="fill:#79be9f; stroke:none;" d="M579 460L580 461L579 460z"/>
<path style="fill:#80e0b2; stroke:none;" d="M713 460L714 461L713 460z"/>
<path style="fill:#add3be; stroke:none;" d="M731 460L732 461L731 460z"/>
<path style="fill:#9be8be; stroke:none;" d="M1283 460L1283 467C1284.06 464.466 1284.06 462.534 1283 460z"/>
<path style="fill:#6ab48f; stroke:none;" d="M170 461L171 462L170 461z"/>
<path style="fill:#51b084; stroke:none;" d="M578 461L579 462L578 461z"/>
<path style="fill:#9ae1c3; stroke:none;" d="M169 462L170 463L169 462z"/>
<path style="fill:#eaeaea; stroke:none;" d="M578 462C577.043 463.516 576.613 464.339 576 466C577.63 464.485 578.163 464.145 578 462z"/>
<path style="fill:#5db48a; stroke:none;" d="M712 462L713 463L712 462z"/>
<path style="fill:#b0e1c4; stroke:none;" d="M730 462L731 463L730 462z"/>
<path style="fill:#66ad8f; stroke:none;" d="M169 463L170 464L169 463z"/>
<path style="fill:#b3dfc8; stroke:none;" d="M711 463L712 464L711 463z"/>
<path style="fill:#97ecc3; stroke:none;" d="M168 464L169 465L168 464z"/>
<path style="fill:#219159; stroke:none;" d="M169 464L161 480C165.7 478.631 170.749 468.623 169 464z"/>
<path style="fill:#159759; stroke:none;" d="M571 464L570 468L571 469C572.661 468.387 573.483 467.957 575 467C573.55 465.393 572.948 464.936 571 464z"/>
<path style="fill:#eafff2; stroke:none;" d="M729 464C727.916 466.92 726.303 469.191 730 470C729.839 467.839 729.827 465.986 729 464z"/>
<path style="fill:#5fb48b; stroke:none;" d="M168 465L169 466L168 465z"/>
<path style="fill:#50cb9c; stroke:none;" d="M575 465L576 466L575 465z"/>
<path style="fill:#5aae7c; stroke:none;" d="M710 465L711 466L710 465z"/>
<path style="fill:#51b181; stroke:none;" d="M728 465L729 466L728 465z"/>
<path style="fill:#89d0a8; stroke:none;" d="M709 466L710 467L709 466z"/>
<path style="fill:#53ae85; stroke:none;" d="M810.333 466.667C810.278 466.722 810.222 467.778 810.667 467.333C810.723 467.277 810.778 466.222 810.333 466.667z"/>
<path style="fill:#68a788; stroke:none;" d="M167 467L168 468L167 467z"/>
<path style="fill:#4cb481; stroke:none;" d="M727 467L728 468L727 467z"/>
<path style="fill:#d6ffee; stroke:none;" d="M1283 467L1283 481C1284.67 477.028 1284.67 470.972 1283 467z"/>
<path style="fill:#a4e1c2; stroke:none;" d="M166 468L167 469L166 468z"/>
<path style="fill:#90cbaf; stroke:none;" d="M573 468L574 469L573 468z"/>
<path style="fill:#46b582; stroke:none;" d="M708 468L709 469L708 468z"/>
<path style="fill:#83d8b1; stroke:none;" d="M810.333 468.667C810.278 468.722 810.222 469.778 810.667 469.333C810.722 469.278 810.778 468.222 810.333 468.667z"/>
<path style="fill:#629f80; stroke:none;" d="M166 469L167 470L166 469z"/>
<path style="fill:#66c397; stroke:none;" d="M572 469L573 470L572 469z"/>
<path style="fill:#8ad0ae; stroke:none;" d="M707 469L708 470L707 469z"/>
<path style="fill:#52ae7d; stroke:none;" d="M726 469L727 470L726 469z"/>
<path style="fill:#9fe7c2; stroke:none;" d="M165 470L166 471L165 470z"/>
<path style="fill:#039b4e; stroke:none;" d="M692 504L688 504L688 512C694.97 506.236 697.909 500.589 696 492C697.723 492.934 699.106 493.535 701 494L702 506L706 506L706 512L709 512C709.159 506.632 711.775 503.702 713.612 498.911C715.447 494.123 715.826 489.006 715 484L719 486L720 480L714 480L712 470C706.34 470.446 704.281 474.416 701.579 479.015C696.75 487.231 692.763 494.352 692 504z"/>
<path style="fill:#429f72; stroke:none;" d="M725.333 470.667C725.278 470.722 725.222 471.778 725.667 471.333C725.723 471.277 725.778 470.222 725.333 470.667z"/>
<path style="fill:#a9dec0; stroke:none;" d="M726 470L727 471L726 470z"/>
<path style="fill:#a3edca; stroke:none;" d="M810.333 470.667C810.278 470.722 810.222 471.778 810.667 471.333C810.723 471.277 810.778 470.222 810.333 470.667z"/>
<path style="fill:#60ad89; stroke:none;" d="M706 471L707 472L706 471z"/>
<path style="fill:#98ceb4; stroke:none;" d="M570 472L571 473L570 472z"/>
<path style="fill:#649d7a; stroke:none;" d="M705.333 472.667C705.278 472.722 705.222 473.778 705.667 473.333C705.723 473.277 705.777 472.223 705.333 472.667z"/>
<path style="fill:#c9ffe7; stroke:none;" d="M725.333 472.667C725.278 472.722 725.222 473.778 725.667 473.333C725.723 473.277 725.777 472.222 725.333 472.667z"/>
<path style="fill:#2b885b; stroke:none;" d="M811 472L811 477C811.83 474.97 811.83 474.03 811 472z"/>
<path style="fill:#6dc19d; stroke:none;" d="M569 473L570 474L569 473z"/>
<path style="fill:#4ab281; stroke:none;" d="M724 473L725 474L724 473z"/>
<path style="fill:#9febc5; stroke:none;" d="M163 474L164 475L163 474z"/>
<path style="fill:#529778; stroke:none;" d="M568 474L569 475L568 474z"/>
<path style="fill:#1a9157; stroke:none;" d="M721 474L721 480C722.573 478.051 723.291 476.412 724 474L721 474z"/>
<path style="fill:#ebfffd; stroke:none;" d="M724 474L720 486C721.195 485.402 720.977 485.534 722 484C722.98 485.784 722.907 485.594 725 486C725.423 483.4 725.936 481.822 724 480L726 474L724 474z"/>
<path style="fill:#a4e9ca; stroke:none;" d="M568 475L569 476L568 475z"/>
<path style="fill:#a6e1cd; stroke:none;" d="M703 475L704 476L703 475z"/>
<path style="fill:#6ebc92; stroke:none;" d="M1282 475L1282 479C1282.71 477.24 1282.71 476.76 1282 475z"/>
<path style="fill:#a3e9c7; stroke:none;" d="M162 476L163 477L162 476z"/>
<path style="fill:#148750; stroke:none;" d="M563 480C564.981 478.84 565.74 477.951 567 476C564.751 477.154 564.154 477.751 563 480z"/>
<path style="fill:#86c5aa; stroke:none;" d="M567 476L568 477L567 476z"/>
<path style="fill:#d3fff6; stroke:none;" d="M701 476L701 479C702.016 477.86 702.309 477.389 703 476L701 476z"/>
<path style="fill:#a4d6b9; stroke:none;" d="M723 476L724 477L723 476z"/>
<path style="fill:#62be95; stroke:none;" d="M566 477L567 478L566 477z"/>
<path style="fill:#83c7b0; stroke:none;" d="M702 477L703 478L702 477z"/>
<path style="fill:#74c59c; stroke:none;" d="M811 477L811 480C811.696 478.446 811.696 478.554 811 477z"/>
<path style="fill:#abe2c5; stroke:none;" d="M161 478L162 479L161 478z"/>
<path style="fill:#5ca77e; stroke:none;" d="M565 478L566 479L565 478z"/>
<path style="fill:#97dbb6; stroke:none;" d="M722 478L723 479L722 478z"/>
<path style="fill:#91dcb3; stroke:none;" d="M565 479L566 480L565 479z"/>
<path style="fill:#6eb69e; stroke:none;" d="M701 479L702 480L701 479z"/>
<path style="fill:#afe4ca; stroke:none;" d="M1282 479L1282 482C1282.7 480.446 1282.7 480.554 1282 479z"/>
<path style="fill:#eefff9; stroke:none;" d="M152 480L152 482L156 482C155.637 487.649 152.564 492.383 152 498C155.421 493.48 156.776 487.717 160 483C157.583 480.458 155.45 480.104 152 480z"/>
<path style="fill:#86f7b7; stroke:none;" d="M160 480L161 481L160 480z"/>
<path style="fill:#119951; stroke:none;" d="M161 480L158 488C161.531 486.219 163.085 483.809 164 480L161 480z"/>
<path style="fill:#41916a; stroke:none;" d="M563 480L563 482C564.635 481.455 564.455 481.635 565 480L563 480z"/>
<path style="fill:#598375; stroke:none;" d="M720.333 480.667C720.278 480.722 720.222 481.778 720.667 481.333C720.722 481.278 720.778 480.222 720.333 480.667z"/>
<path style="fill:#bedfd4; stroke:none;" d="M721.333 480.667C721.278 480.722 721.222 481.778 721.667 481.333C721.722 481.278 721.778 480.222 721.333 480.667z"/>
<path style="fill:#398561; stroke:none;" d="M812 480L812 485C812.83 482.97 812.83 482.03 812 480z"/>
<path style="fill:#21824f; stroke:none;" d="M813 480L813 490C814.346 486.793 814.346 483.207 813 480z"/>
<path style="fill:#11925a; stroke:none;" d="M1280 480L1280 496C1282.01 491.252 1281.99 485.138 1282 480L1280 480z"/>
<path style="fill:#4ebf7f; stroke:none;" d="M160 481L161 482L160 481z"/>
<path style="fill:#e6fff7; stroke:none;" d="M564 481C562.49 483.291 561.163 485.518 560 488C563.97 486.843 565.166 484.99 566 481L564 481z"/>
<path style="fill:#e8fff6; stroke:none;" d="M697 482L694 489C696.892 487.228 697.966 485.209 699 482L697 482z"/>
<path style="fill:#83d1aa; stroke:none;" d="M699 482L700 483L699 482z"/>
<path style="fill:#95b6ab; stroke:none;" d="M720 482L721 483L720 482z"/>
<path style="fill:#92a99f; stroke:none;" d="M159 483L160 484L159 483z"/>
<path style="fill:#7ddaab; stroke:none;" d="M562 483L563 484L562 483z"/>
<path style="fill:#82bca3; stroke:none;" d="M158.333 484.667C158.278 484.722 158.222 485.778 158.667 485.333C158.722 485.278 158.778 484.222 158.333 484.667z"/>
<path style="fill:#559674; stroke:none;" d="M561 484L562 485L561 484z"/>
<path style="fill:#62bf93; stroke:none;" d="M698 484L699 485L698 484z"/>
<path style="fill:#1a8d52; stroke:none;" d="M715 484C715.282 487.382 715.197 490.617 715 494C717.468 490.679 718.761 486.691 715 484z"/>
<path style="fill:#50b480; stroke:none;" d="M719 484L720 485L719 484z"/>
<path style="fill:#83e7b3; stroke:none;" d="M719 485L720 486L719 485z"/>
<path style="fill:#ade2c6; stroke:none;" d="M812.333 485.667C812.278 485.722 812.222 486.778 812.667 486.333C812.722 486.278 812.778 485.222 812.333 485.667z"/>
<path style="fill:#77c4a0; stroke:none;" d="M157.333 486.667C157.278 486.722 157.222 487.778 157.667 487.333C157.722 487.278 157.778 486.222 157.333 486.667z"/>
<path style="fill:#2a8156; stroke:none;" d="M559 486C557.427 488.115 556.648 489.47 556 492C557.894 490.069 559.157 488.707 559 486z"/>
<path style="fill:#a1c9b1; stroke:none;" d="M560 486L561 487L560 486z"/>
<path style="fill:#4fb988; stroke:none;" d="M697 486L698 487L697 486z"/>
<path style="fill:#69c594; stroke:none;" d="M718.333 486.667C718.278 486.722 718.222 487.778 718.667 487.333C718.722 487.278 718.778 486.222 718.333 486.667z"/>
<path style="fill:#ebfff8; stroke:none;" d="M719 486C717.76 489.4 717.085 492.377 717 496L720 492C719.839 489.84 719.827 487.986 719 486z"/>
<path style="fill:#9ce3c1; stroke:none;" d="M696 487L697 488L696 487z"/>
<path style="fill:#128854; stroke:none;" d="M157 488L154 496C156.85 494.047 157.991 491.357 157 488z"/>
<path style="fill:#e8fff5; stroke:none;" d="M559 488C557.545 490.656 556.069 493.168 555 496C557.989 494.159 560.232 491.557 559 488z"/>
<path style="fill:#78cea9; stroke:none;" d="M1281.33 488.667C1281.28 488.723 1281.22 489.778 1281.67 489.333C1281.72 489.278 1281.78 488.222 1281.33 488.667z"/>
<path style="fill:#7fc29f; stroke:none;" d="M156 489L157 490L156 489z"/>
<path style="fill:#7de0b1; stroke:none;" d="M558 489L559 490L558 489z"/>
<path style="fill:#8cc9a7; stroke:none;" d="M695 489L696 490L695 489z"/>
<path style="fill:#79c19c; stroke:none;" d="M717 489L718 490L717 489z"/>
<path style="fill:#6aab8b; stroke:none;" d="M557 490L558 491L557 490z"/>
<path style="fill:#e3fff4; stroke:none;" d="M688 502C691.226 498.554 693.195 494.335 695 490C690.527 492.345 688.65 497.115 688 502z"/>
<path style="fill:#78c398; stroke:none;" d="M813.333 490.667C813.278 490.722 813.222 491.778 813.667 491.333C813.722 491.278 813.778 490.222 813.333 490.667z"/>
<path style="fill:#a0eac9; stroke:none;" d="M1281.33 490.667C1281.28 490.723 1281.22 491.778 1281.67 491.333C1281.72 491.278 1281.78 490.222 1281.33 490.667z"/>
<path style="fill:#94d1b2; stroke:none;" d="M155 491L156 492L155 491z"/>
<path style="fill:#77bb96; stroke:none;" d="M694 491L695 492L694 491z"/>
<path style="fill:#9ad3b3; stroke:none;" d="M556 492L557 493L556 492z"/>
<path style="fill:#cfffef; stroke:none;" d="M716 492L715 496C716.656 494.596 716.751 494.046 716 492z"/>
<path style="fill:#defff6; stroke:none;" d="M813 492C813.012 496.193 812.508 501.175 816 504C815.184 500.043 814.646 495.664 813 492z"/>
<path style="fill:#c7ffea; stroke:none;" d="M1281 492L1281 496C1281.71 494.24 1281.71 493.76 1281 492z"/>
<path style="fill:#91d8b8; stroke:none;" d="M154 493L155 494L154 493z"/>
<path style="fill:#6ec193; stroke:none;" d="M555 493L556 494L555 493z"/>
<path style="fill:#6bbc93; stroke:none;" d="M693 493L694 494L693 493z"/>
<path style="fill:#65cd9c; stroke:none;" d="M715 494L716 495L715 494z"/>
<path style="fill:#82e3ae; stroke:none;" d="M554 495L555 496L554 495z"/>
<path style="fill:#6dba96; stroke:none;" d="M692 495L693 496L692 495z"/>
<path style="fill:#349162; stroke:none;" d="M153.333 496.667C153.278 496.722 153.222 497.778 153.667 497.333C153.722 497.278 153.778 496.222 153.333 496.667z"/>
<path style="fill:#99dbc0; stroke:none;" d="M553.333 496.667C553.278 496.722 553.222 497.778 553.667 497.333C553.722 497.278 553.778 496.222 553.333 496.667z"/>
<path style="fill:#22955e; stroke:none;" d="M692 496L688 504L692 504C692.353 501.161 692.969 498.698 692 496z"/>
<path style="fill:#6c987f; stroke:none;" d="M714 496L715 497L714 496z"/>
<path style="fill:#93d9c1; stroke:none;" d="M814.333 496.667C814.278 496.722 814.222 497.778 814.667 497.333C814.722 497.278 814.778 496.222 814.333 496.667z"/>
<path style="fill:#268a68; stroke:none;" d="M815 496L815 501C815.83 498.97 815.83 498.03 815 496z"/>
<path style="fill:#87a79c; stroke:none;" d="M1280 496L1280 499C1280.7 497.446 1280.7 497.554 1280 496z"/>
<path style="fill:#5ca882; stroke:none;" d="M691 497L692 498L691 497z"/>
<path style="fill:#a1cdb4; stroke:none;" d="M714 497L715 498L714 497z"/>
<path style="fill:#68b48d; stroke:none;" d="M152 498L153 499L152 498z"/>
<path style="fill:#8adab7; stroke:none;" d="M552 498L553 499L552 498z"/>
<path style="fill:#d4ffea; stroke:none;" d="M714 498C713.149 500.673 712.41 503.226 712 506C714.037 503.439 715.098 501.167 714 498z"/>
<path style="fill:#619e7f; stroke:none;" d="M690 499L691 500L690 499z"/>
<path style="fill:#66bd92; stroke:none;" d="M713 499L714 500L713 499z"/>
<path style="fill:#cee8df; stroke:none;" d="M1280.33 499.667C1280.28 499.723 1280.22 500.777 1280.67 500.333C1280.72 500.278 1280.78 499.222 1280.33 499.667z"/>
<path style="fill:#d0ffea; stroke:none;" d="M150.333 500.667C150.278 500.722 150.222 501.778 150.667 501.333C150.722 501.278 150.778 500.222 150.333 500.667z"/>
<path style="fill:#69be94; stroke:none;" d="M151 500L152 501L151 500z"/>
<path style="fill:#92eac0; stroke:none;" d="M689 500L690 501L689 500z"/>
<path style="fill:#6ec49d; stroke:none;" d="M550 501L551 502L550 501z"/>
<path style="fill:#6cad97; stroke:none;" d="M815 501L816 502L815 501z"/>
<path style="fill:#e0fff8; stroke:none;" d="M149 502C147.323 505.485 146.118 508.079 146 512C148.012 509.004 149.984 505.587 149 502z"/>
<path style="fill:#7fdcaf; stroke:none;" d="M150 502L151 503L150 502z"/>
<path style="fill:#5bb38b; stroke:none;" d="M549 502L550 503L549 502z"/>
<path style="fill:#86f4c3; stroke:none;" d="M688 502L689 503L688 502z"/>
<path style="fill:#20925e; stroke:none;" d="M711 502C709.695 505.077 709.097 507.66 709 511C710.773 508.179 711.961 505.251 711 502z"/>
<path style="fill:#74c09a; stroke:none;" d="M712 502L713 503L712 502z"/>
<path style="fill:#8de5bd; stroke:none;" d="M549 503L550 504L549 503z"/>
<path style="fill:#168d55; stroke:none;" d="M544 504L544 511C546.032 508.813 547.094 506.845 548 504L544 504z"/>
<path style="fill:#4fb085; stroke:none;" d="M548 504L549 505L548 504z"/>
<path style="fill:#e0fced; stroke:none;" d="M549 504C547.286 506.624 545.944 509.013 545 512C547.603 509.762 549.576 507.534 549 504z"/>
<path style="fill:#b7e1cb; stroke:none;" d="M687 504L688 505L687 504z"/>
<path style="fill:#69d4a0; stroke:none;" d="M1279.33 504.667C1279.28 504.723 1279.22 505.777 1279.67 505.333C1279.72 505.278 1279.78 504.222 1279.33 504.667z"/>
<path style="fill:#58ab81; stroke:none;" d="M149 505L150 506L149 505z"/>
<path style="fill:#ecfff2; stroke:none;" d="M685 505C683.109 509.228 681.27 513.548 680 518C683.14 514.893 687.179 509.318 685 505z"/>
<path style="fill:#76a08a; stroke:none;" d="M687 505L688 506L687 505z"/>
<path style="fill:#8cd8b4; stroke:none;" d="M711 505L712 506L711 505z"/>
<path style="fill:#86d1b0; stroke:none;" d="M547 506L548 507L547 506z"/>
<path style="fill:#7fd1a3; stroke:none;" d="M816 506L817 507L816 506z"/>
<path style="fill:#159354; stroke:none;" d="M817 506C817.307 509.752 818.241 513.32 819 517C820.44 513.568 820 509.7 820 506L817 506z"/>
<path style="fill:#228f58; stroke:none;" d="M1277 506L1277 512C1278.49 509.606 1278.49 508.394 1277 506z"/>
<path style="fill:#cbffe7; stroke:none;" d="M1279 506L1279 512C1279.95 509.715 1279.95 508.285 1279 506z"/>
<path style="fill:#6bbe94; stroke:none;" d="M148 507L149 508L148 507z"/>
<path style="fill:#71ae8d; stroke:none;" d="M686 507L687 508L686 507z"/>
<path style="fill:#99dabc; stroke:none;" d="M710 508L711 509L710 508z"/>
<path style="fill:#7fd4ad; stroke:none;" d="M147 509L148 510L147 509z"/>
<path style="fill:#75c2a0; stroke:none;" d="M545 509L546 510L545 509z"/>
<path style="fill:#5db487; stroke:none;" d="M685 509L686 510L685 509z"/>
<path style="fill:#1c8b56; stroke:none;" d="M147 510C146.149 512.673 145.41 515.225 145 518C147.574 515.918 148.105 513.087 147 510z"/>
<path style="fill:#97e4b8; stroke:none;" d="M817.333 510.667C817.278 510.722 817.222 511.778 817.667 511.333C817.722 511.278 817.778 510.222 817.333 510.667z"/>
<path style="fill:#5eba87; stroke:none;" d="M1278.33 510.667C1278.28 510.723 1278.22 511.777 1278.67 511.333C1278.72 511.278 1278.78 510.222 1278.33 510.667z"/>
<path style="fill:#7addb1; stroke:none;" d="M544 511L545 512L544 511z"/>
<path style="fill:#87cfaa; stroke:none;" d="M684 511L685 512L684 511z"/>
<path style="fill:#71ddac; stroke:none;" d="M709 511L710 512L709 511z"/>
<path style="fill:#e0fff3; stroke:none;" d="M144 512L144 516C145.081 514.542 145.436 513.752 146 512L144 512z"/>
<path style="fill:#208656; stroke:none;" d="M542 512C538.53 517.038 535.188 520.661 535 527C538.648 522.643 541.594 517.148 544 512L542 512z"/>
<path style="fill:#1f864f; stroke:none;" d="M707 512L707 516C707.911 514.623 708.404 513.56 709 512L707 512z"/>
<path style="fill:#c6edd2; stroke:none;" d="M1278.33 512.667C1278.28 512.723 1278.22 513.777 1278.67 513.333C1278.72 513.278 1278.78 512.222 1278.33 512.667z"/>
<path style="fill:#ebfff6; stroke:none;" d="M1279 512C1277.74 515.963 1276.79 519.919 1276 524C1279.38 521.305 1280.62 515.985 1279 512z"/>
<path style="fill:#97e2c3; stroke:none;" d="M543 513L544 514L543 513z"/>
<path style="fill:#8bd3ab; stroke:none;" d="M683 513L684 514L683 513z"/>
<path style="fill:#5ca079; stroke:none;" d="M708 513L709 514L708 513z"/>
<path style="fill:#73c69c; stroke:none;" d="M145 514L146 515L145 514z"/>
<path style="fill:#e9fffa; stroke:none;" d="M543 514C538.087 522.86 533.694 532.671 528 541C530.163 543.277 531.885 543.784 535 544C534.173 541.499 532.338 538.823 533.207 536.13C535.422 529.264 545.631 521.605 543 514z"/>
<path style="fill:#159b5e; stroke:none;" d="M683 514C682.149 516.673 681.41 519.225 681 522C683.204 519.445 684.227 517.245 683 514z"/>
<path style="fill:#91d1ac; stroke:none;" d="M708 514L709 515L708 514z"/>
<path style="fill:#acf6d5; stroke:none;" d="M818.333 514.667C818.278 514.722 818.222 515.778 818.667 515.333C818.722 515.278 818.778 514.222 818.333 514.667z"/>
<path style="fill:#9df7d1; stroke:none;" d="M542 515L543 516L542 515z"/>
<path style="fill:#9fd8b7; stroke:none;" d="M682 515L683 516L682 515z"/>
<path style="fill:#8ce2b5; stroke:none;" d="M144 516L145 517L144 516z"/>
<path style="fill:#79bc9d; stroke:none;" d="M541 516L542 517L541 516z"/>
<path style="fill:#dffff7; stroke:none;" d="M818 516L818 520C818.71 518.24 818.71 517.759 818 516z"/>
<path style="fill:#278257; stroke:none;" d="M1276 516L1276 520C1276.71 518.24 1276.71 517.759 1276 516z"/>
<path style="fill:#65bd93; stroke:none;" d="M1277 516L1278 517L1277 516z"/>
<path style="fill:#58ae81; stroke:none;" d="M144 517L145 518L144 517z"/>
<path style="fill:#b1e8c9; stroke:none;" d="M681 517L682 518L681 517z"/>
<path style="fill:#73c99c; stroke:none;" d="M707 517L708 518L707 517z"/>
<path style="fill:#53b68a; stroke:none;" d="M819 517L820 518L819 517z"/>
<path style="fill:#edfff8; stroke:none;" d="M139 518L138 531C140.829 527.258 142.308 522.619 143 518L139 518z"/>
<path style="fill:#8cb6a2; stroke:none;" d="M143.333 518.667C143.278 518.722 143.222 519.778 143.667 519.333C143.722 519.278 143.778 518.222 143.333 518.667z"/>
<path style="fill:#85c4a5; stroke:none;" d="M540 518L541 519L540 518z"/>
<path style="fill:#e2ffee; stroke:none;" d="M679 518L677 526C679.185 523.488 680.139 521.215 679 518z"/>
<path style="fill:#c4ffe4; stroke:none;" d="M680.333 518.667C680.278 518.722 680.222 519.778 680.667 519.333C680.722 519.278 680.778 518.222 680.333 518.667z"/>
<path style="fill:#59bb8c; stroke:none;" d="M681 518L682 519L681 518z"/>
<path style="fill:#278b59; stroke:none;" d="M705 518C704.276 521.039 704.026 523.878 704 527C705.531 524.184 706.283 521.126 707 518L705 518z"/>
<path style="fill:#b5ffde; stroke:none;" d="M819.333 518.667C819.278 518.722 819.222 519.778 819.667 519.333C819.722 519.278 819.778 518.222 819.333 518.667z"/>
<path style="fill:#1f8e5b; stroke:none;" d="M820 518L821 525C822.078 522.227 821.663 520.435 820 518z"/>
<path style="fill:#188850; stroke:none;" d="M143 520L140 528C142.957 526.038 144.186 523.426 143 520z"/>
<path style="fill:#44a476; stroke:none;" d="M680.333 520.667C680.278 520.722 680.222 521.778 680.667 521.333C680.722 521.278 680.778 520.222 680.333 520.667z"/>
<path style="fill:#a4f0c9; stroke:none;" d="M706.333 520.667C706.278 520.722 706.222 521.778 706.667 521.333C706.722 521.278 706.778 520.222 706.333 520.667z"/>
<path style="fill:#16924c; stroke:none;" d="M1268 520L1268 528L1271 528C1270.92 524.565 1270.91 522.055 1268 520z"/>
<path style="fill:#9eddbe; stroke:none;" d="M1276.33 520.667C1276.28 520.723 1276.22 521.777 1276.67 521.333C1276.72 521.278 1276.78 520.222 1276.33 520.667z"/>
<path style="fill:#99ceb2; stroke:none;" d="M142 521L143 522L142 521z"/>
<path style="fill:#70c29a; stroke:none;" d="M538 521L539 522L538 521z"/>
<path style="fill:#6aba95; stroke:none;" d="M820 521L821 522L820 521z"/>
<path style="fill:#86ddb0; stroke:none;" d="M679 522L680 523L679 522z"/>
<path style="fill:#cfffe8; stroke:none;" d="M706 522L705 528C706.654 525.823 706.994 524.575 706 522z"/>
<path style="fill:#75c19d; stroke:none;" d="M537 523L538 524L537 523z"/>
<path style="fill:#118d4f; stroke:none;" d="M679 523C677.053 530.131 673.671 536.783 672 544C677.283 542.149 680.951 528.011 679 523z"/>
<path style="fill:#65a684; stroke:none;" d="M705 523L706 524L705 523z"/>
<path style="fill:#5dbe8b; stroke:none;" d="M141 524L142 525L141 524z"/>
<path style="fill:#6aa98a; stroke:none;" d="M1275 524L1276 525L1275 524z"/>
<path style="fill:#87d3af; stroke:none;" d="M536 525L537 526L536 525z"/>
<path style="fill:#7fd2a8; stroke:none;" d="M678 525L679 526L678 525z"/>
<path style="fill:#baf9da; stroke:none;" d="M821 525L821 528C821.696 526.446 821.696 526.554 821 525z"/>
<path style="fill:#caffe4; stroke:none;" d="M1275 525L1275 528C1275.7 526.446 1275.7 526.554 1275 525z"/>
<path style="fill:#87d3ac; stroke:none;" d="M140 526L141 527L140 526z"/>
<path style="fill:#b6eed1; stroke:none;" d="M677.333 526.667C677.278 526.722 677.222 527.778 677.667 527.333C677.722 527.278 677.778 526.222 677.333 526.667z"/>
<path style="fill:#2fa56b; stroke:none;" d="M822.333 526.667C822.278 526.722 822.222 527.778 822.667 527.333C822.722 527.278 822.778 526.222 822.333 526.667z"/>
<path style="fill:#61aa8c; stroke:none;" d="M534.333 527.667C534.278 527.722 534.222 528.778 534.667 528.333C534.722 528.278 534.778 527.222 534.333 527.667z"/>
<path style="fill:#86deb4; stroke:none;" d="M535 527L536 528L535 527z"/>
<path style="fill:#72be98; stroke:none;" d="M704 527L705 528L704 527z"/>
<path style="fill:#1c9659; stroke:none;" d="M532 528C531.149 530.673 530.41 533.225 530 536C532.036 533.534 533.113 531.071 534 528L532 528z"/>
<path style="fill:#e1ffee; stroke:none;" d="M675 528C674.205 532.478 672.239 536.394 672 541C674.583 537.109 675.951 532.53 677 528L675 528z"/>
<path style="fill:#1a8f5b; stroke:none;" d="M703 528C700.883 533.196 700.004 537.384 700 543C702.061 539.131 704.252 532.232 703 528z"/>
<path style="fill:#bae8ce; stroke:none;" d="M822.333 528.667C822.278 528.722 822.222 529.778 822.667 529.333C822.722 529.278 822.778 528.222 822.333 528.667z"/>
<path style="fill:#1e8f57; stroke:none;" d="M823 528C824.238 536.383 828.304 544.032 831 552C832.149 548.68 830.59 546.234 829.558 543C827.799 537.491 828.354 531.507 823 528z"/>
<path style="fill:#b8e4c9; stroke:none;" d="M1274.33 528.667C1274.28 528.723 1274.22 529.777 1274.67 529.333C1274.72 529.278 1274.78 528.222 1274.33 528.667z"/>
<path style="fill:#63c594; stroke:none;" d="M139 529L140 530L139 529z"/>
<path style="fill:#97e0c2; stroke:none;" d="M534 529L535 530L534 529z"/>
<path style="fill:#20935a; stroke:none;" d="M139 530L137 537C138.737 536.938 138.64 536.98 140 538C140.325 535.034 140.272 532.711 139 530z"/>
<path style="fill:#6ab491; stroke:none;" d="M533 530L534 531L533 530z"/>
<path style="fill:#7ed5ab; stroke:none;" d="M676 530L677 531L676 530z"/>
<path style="fill:#d6ffea; stroke:none;" d="M1274 530C1273.15 532.673 1272.41 535.225 1272 538C1274.2 535.445 1275.23 533.245 1274 530z"/>
<path style="fill:#97d7b5; stroke:none;" d="M138 531L139 532L138 531z"/>
<path style="fill:#7fe1a8; stroke:none;" d="M703 531L704 532L703 531z"/>
<path style="fill:#85cfaa; stroke:none;" d="M823 531L824 532L823 531z"/>
<path style="fill:#63ba8d; stroke:none;" d="M1273 531L1274 532L1273 531z"/>
<path style="fill:#79b997; stroke:none;" d="M532 532L533 533L532 532z"/>
<path style="fill:#d8ffec; stroke:none;" d="M703 532L703 536C703.71 534.241 703.71 533.759 703 532z"/>
<path style="fill:#77b697; stroke:none;" d="M675 533L676 534L675 533z"/>
<path style="fill:#81c8a6; stroke:none;" d="M137 534L138 535L137 534z"/>
<path style="fill:#78b997; stroke:none;" d="M531 534L532 535L531 534z"/>
<path style="fill:#7ed4a5; stroke:none;" d="M702.333 534.667C702.278 534.722 702.222 535.778 702.667 535.333C702.722 535.278 702.778 534.222 702.333 534.667z"/>
<path style="fill:#8ad1af; stroke:none;" d="M824 534L825 535L824 534z"/>
<path style="fill:#17824e; stroke:none;" d="M1271 534L1270 540C1271.31 537.819 1271.74 536.433 1271 534z"/>
<path style="fill:#63af88; stroke:none;" d="M1272 534L1273 535L1272 534z"/>
<path style="fill:#d4ffed; stroke:none;" d="M135 536C134.356 538.106 134.094 539.795 134 542C135.293 540.077 136.142 538.16 137 536L135 536z"/>
<path style="fill:#0e9550; stroke:none;" d="M528 536C523.079 540.742 522.512 541.302 524 548C526.883 544.52 528.752 540.333 530 536L528 536z"/>
<path style="fill:#aefad6; stroke:none;" d="M530.333 536.667C530.278 536.722 530.222 537.778 530.667 537.333C530.722 537.278 530.778 536.222 530.333 536.667z"/>
<path style="fill:#69b994; stroke:none;" d="M136 537L137 538L136 537z"/>
<path style="fill:#82cca9; stroke:none;" d="M825 537L826 538L825 537z"/>
<path style="fill:#61ad87; stroke:none;" d="M1271 537L1272 538L1271 537z"/>
<path style="fill:#b2ecd4; stroke:none;" d="M529.333 538.667C529.278 538.722 529.222 539.778 529.667 539.333C529.722 539.278 529.778 538.222 529.333 538.667z"/>
<path style="fill:#6dcd9d; stroke:none;" d="M673 538L674 539L673 538z"/>
<path style="fill:#dbffef; stroke:none;" d="M825 538C825.547 543.485 827.183 550.842 832 554C830.245 548.769 828.3 542.411 825 538z"/>
<path style="fill:#8ee3ba; stroke:none;" d="M135 539L136 540L135 539z"/>
<path style="fill:#d0fff0; stroke:none;" d="M701 539L701 544C701.83 541.97 701.83 541.03 701 539z"/>
<path style="fill:#a0bbb2; stroke:none;" d="M528 540L529 541L528 540z"/>
<path style="fill:#80c1a1; stroke:none;" d="M1270 540L1271 541L1270 540z"/>
<path style="fill:#78d1a1; stroke:none;" d="M672 541L673 542L672 541z"/>
<path style="fill:#82cca7; stroke:none;" d="M134 542L135 543L134 542z"/>
<path style="fill:#63d496; stroke:none;" d="M527 542L528 543L527 542z"/>
<path style="fill:#77bf9a; stroke:none;" d="M827 542L828 543L827 542z"/>
<path style="fill:#64d09e; stroke:none;" d="M1269.33 542.667C1269.28 542.723 1269.22 543.777 1269.67 543.333C1269.72 543.278 1269.78 542.222 1269.33 542.667z"/>
<path style="fill:#a1ffd4; stroke:none;" d="M527 543L528 544L527 543z"/>
<path style="fill:#62c99e; stroke:none;" d="M700 543L701 544L700 543z"/>
<path style="fill:#e0fff6; stroke:none;" d="M132 544L132 548C132.71 546.241 132.71 545.759 132 544z"/>
<path style="fill:#98f8ca; stroke:none;" d="M133 544L134 545L133 544z"/>
<path style="fill:#16924c; stroke:none;" d="M137 544C137.318 546.877 137.62 548.334 140 550L140 544L137 544z"/>
<path style="fill:#cde6d3; stroke:none;" d="M526.333 544.667C526.278 544.722 526.222 545.778 526.667 545.333C526.722 545.278 526.778 544.222 526.333 544.667z"/>
<path style="fill:#dbfff4; stroke:none;" d="M670 544C667.676 549.352 666.02 554.136 666 560C668.429 556.023 671.342 548.538 670 544z"/>
<path style="fill:#80cbac; stroke:none;" d="M671 544L672 545L671 544z"/>
<path style="fill:#2e8e5e; stroke:none;" d="M698 544L698 552C699.378 549.412 699.761 546.92 700 544L698 544z"/>
<path style="fill:#c1edd6; stroke:none;" d="M700.333 544.667C700.278 544.722 700.222 545.778 700.667 545.333C700.722 545.278 700.778 544.222 700.333 544.667z"/>
<path style="fill:#188e52; stroke:none;" d="M1266 544C1265.22 548.597 1264.2 553.556 1260 556L1260 563C1264.24 557.54 1266.8 550.515 1269 544L1266 544z"/>
<path style="fill:#e7fff5; stroke:none;" d="M1269 544C1267.27 549.313 1264.57 554.42 1264 560C1268.09 555.575 1271.54 550.062 1269 544z"/>
<path style="fill:#5fbf91; stroke:none;" d="M133 545L134 546L133 545z"/>
<path style="fill:#1e8651; stroke:none;" d="M671 545L667 560C670.667 557.039 672.828 549.39 671 545z"/>
<path style="fill:#89d4ad; stroke:none;" d="M828 545L829 546L828 545z"/>
<path style="fill:#72af8d; stroke:none;" d="M1268 545L1269 546L1268 545z"/>
<path style="fill:#188f57; stroke:none;" d="M133 546L130 556C133.234 553.743 134.324 549.702 133 546z"/>
<path style="fill:#7dc09f; stroke:none;" d="M525 546L526 547L525 546z"/>
<path style="fill:#e0fff4; stroke:none;" d="M700 546L699 554C700.825 551.328 701.232 549.023 700 546z"/>
<path style="fill:#98ceb6; stroke:none;" d="M670 547L671 548L670 547z"/>
<path style="fill:#60be8c; stroke:none;" d="M829 547L830 548L829 547z"/>
<path style="fill:#63af89; stroke:none;" d="M132 548L133 549L132 548z"/>
<path style="fill:#298b58; stroke:none;" d="M523.333 548.667C523.278 548.722 523.222 549.778 523.667 549.333C523.722 549.278 523.778 548.222 523.333 548.667z"/>
<path style="fill:#82c3a1; stroke:none;" d="M524 548L525 549L524 548z"/>
<path style="fill:#a7e4c3; stroke:none;" d="M699.333 548.667C699.278 548.722 699.222 549.778 699.667 549.333C699.722 549.278 699.778 548.222 699.333 548.667z"/>
<path style="fill:#8bd3ab; stroke:none;" d="M1267 548L1268 549L1267 548z"/>
<path style="fill:#a3e0c1; stroke:none;" d="M131 550L132 551L131 550z"/>
<path style="fill:#238353; stroke:none;" d="M522.333 550.667C522.278 550.722 522.222 551.778 522.667 551.333C522.722 551.278 522.778 550.222 522.333 550.667z"/>
<path style="fill:#7db294; stroke:none;" d="M523 550L524 551L523 550z"/>
<path style="fill:#8adab5; stroke:none;" d="M669 550L670 551L669 550z"/>
<path style="fill:#98caaf; stroke:none;" d="M830 550L831 551L830 550z"/>
<path style="fill:#96e4ba; stroke:none;" d="M1266.33 550.667C1266.28 550.723 1266.22 551.777 1266.67 551.333C1266.72 551.278 1266.78 550.222 1266.33 550.667z"/>
<path style="fill:#c7ffea; stroke:none;" d="M130.333 551.667C130.278 551.722 130.222 552.778 130.667 552.333C130.722 552.278 130.778 551.222 130.333 551.667z"/>
<path style="fill:#6ca98a; stroke:none;" d="M131 551L132 552L131 551z"/>
<path style="fill:#238959; stroke:none;" d="M521 552L519 558C520.915 555.968 521.744 554.735 521 552z"/>
<path style="fill:#69a685; stroke:none;" d="M522 552L523 553L522 552z"/>
<path style="fill:#20814e; stroke:none;" d="M697 552L697 558C697.951 555.715 697.951 554.285 697 552z"/>
<path style="fill:#7abe97; stroke:none;" d="M698.333 552.667C698.278 552.722 698.222 553.778 698.667 553.333C698.722 553.278 698.778 552.222 698.333 552.667z"/>
<path style="fill:#9adbbd; stroke:none;" d="M130 553L131 554L130 553z"/>
<path style="fill:#9fe7c2; stroke:none;" d="M668 553L669 554L668 553z"/>
<path style="fill:#9cd7b9; stroke:none;" d="M1265 553L1266 554L1265 553z"/>
<path style="fill:#2f8c5d; stroke:none;" d="M832 554L835 561C836.147 557.666 834.94 555.716 832 554z"/>
<path style="fill:#9addbc; stroke:none;" d="M521 555L522 556L521 555z"/>
<path style="fill:#94d0b6; stroke:none;" d="M1264 555L1265 556L1264 555z"/>
<path style="fill:#8edab4; stroke:none;" d="M129 556L130 557L129 556z"/>
<path style="fill:#308b5f; stroke:none;" d="M129 557L129 560C129.696 558.446 129.696 558.554 129 557z"/>
<path style="fill:#9ed9bb; stroke:none;" d="M520 557L521 558L520 557z"/>
<path style="fill:#8cceaa; stroke:none;" d="M667 557L668 558L667 557z"/>
<path style="fill:#98e6be; stroke:none;" d="M833 557L834 558L833 557z"/>
<path style="fill:#5ecb92; stroke:none;" d="M1263 557L1264 558L1263 557z"/>
<path style="fill:#b3dbc3; stroke:none;" d="M128.333 558.667C128.278 558.722 128.222 559.778 128.667 559.333C128.722 559.278 128.778 558.222 128.333 558.667z"/>
<path style="fill:#5b9e7b; stroke:none;" d="M519 558L520 559L519 558z"/>
<path style="fill:#77c699; stroke:none;" d="M697.333 558.667C697.278 558.722 697.222 559.778 697.667 559.333C697.722 559.278 697.778 558.222 697.333 558.667z"/>
<path style="fill:#55c289; stroke:none;" d="M1262.33 558.667C1262.28 558.722 1262.22 559.778 1262.67 559.333C1262.72 559.278 1262.78 558.222 1262.33 558.667z"/>
<path style="fill:#bfffed; stroke:none;" d="M1263.33 558.667C1263.28 558.723 1263.22 559.777 1263.67 559.333C1263.72 559.278 1263.78 558.222 1263.33 558.667z"/>
<path style="fill:#548f73; stroke:none;" d="M518.333 559.667C518.278 559.722 518.222 560.778 518.667 560.333C518.722 560.278 518.778 559.222 518.333 559.667z"/>
<path style="fill:#88cba8; stroke:none;" d="M519 559L520 560L519 559z"/>
<path style="fill:#98e4bd; stroke:none;" d="M834 559L835 560L834 559z"/>
<path style="fill:#8bdfbb; stroke:none;" d="M666 560L667 561L666 560z"/>
<path style="fill:#377e5c; stroke:none;" d="M696 560L696 565C696.83 562.97 696.83 562.03 696 560z"/>
<path style="fill:#eefff0; stroke:none;" d="M697 560L696 576C698.193 571.154 699.146 564.985 697 560z"/>
<path style="fill:#7fba9e; stroke:none;" d="M518 561L519 562L518 561z"/>
<path style="fill:#5fb38f; stroke:none;" d="M666 561L667 562L666 561z"/>
<path style="fill:#73d8ae; stroke:none;" d="M835 561L836 562L835 561z"/>
<path style="fill:#81d1ac; stroke:none;" d="M1261 561L1262 562L1261 561z"/>
<path style="fill:#8dc6b3; stroke:none;" d="M127 562L128 563L127 562z"/>
<path style="fill:#268b5f; stroke:none;" d="M127 563L126 570L128 570C127.981 567.456 127.985 565.332 127 563z"/>
<path style="fill:#69c197; stroke:none;" d="M517 563L518 564L517 563z"/>
<path style="fill:#6ed4a5; stroke:none;" d="M836 563L837 564L836 563z"/>
<path style="fill:#77cfa5; stroke:none;" d="M1260 563L1261 564L1260 563z"/>
<path style="fill:#d6ffea; stroke:none;" d="M517 564L513 576C515.847 572.846 518.465 568.234 517 564z"/>
<path style="fill:#91d2b4; stroke:none;" d="M665 564L666 565L665 564z"/>
<path style="fill:#228f58; stroke:none;" d="M1258 564L1257 568C1258.43 566.607 1259.04 565.767 1260 564L1258 564z"/>
<path style="fill:#a2d1bd; stroke:none;" d="M126 565L127 566L126 565z"/>
<path style="fill:#abdbc1; stroke:none;" d="M696 565L696 568C696.696 566.446 696.696 566.554 696 565z"/>
<path style="fill:#80cca5; stroke:none;" d="M837 565L838 566L837 565z"/>
<path style="fill:#8ad8b0; stroke:none;" d="M1259 565L1260 566L1259 565z"/>
<path style="fill:#3ea474; stroke:none;" d="M515.333 566.667C515.278 566.722 515.222 567.778 515.667 567.333C515.722 567.278 515.778 566.222 515.333 566.667z"/>
<path style="fill:#90d8b3; stroke:none;" d="M516 566L517 567L516 566z"/>
<path style="fill:#c4ffe6; stroke:none;" d="M664.333 566.667C664.278 566.722 664.222 567.778 664.667 567.333C664.722 567.278 664.778 566.222 664.333 566.667z"/>
<path style="fill:#228a55; stroke:none;" d="M695 566L695 571C695.83 568.97 695.83 568.03 695 566z"/>
<path style="fill:#73c59d; stroke:none;" d="M838 567L839 568L838 567z"/>
<path style="fill:#8bd7b0; stroke:none;" d="M1258 567L1259 568L1258 567z"/>
<path style="fill:#2b9161; stroke:none;" d="M514.333 568.667C514.278 568.722 514.222 569.778 514.667 569.333C514.722 569.278 514.778 568.222 514.333 568.667z"/>
<path style="fill:#88cfad; stroke:none;" d="M515 568L516 569L515 568z"/>
<path style="fill:#dfffe8; stroke:none;" d="M662 568L661 581C663.079 577.096 663.651 572.386 664 568L662 568z"/>
<path style="fill:#55ab84; stroke:none;" d="M664.333 568.667C664.278 568.722 664.222 569.778 664.667 569.333C664.722 569.278 664.778 568.222 664.333 568.667z"/>
<path style="fill:#fffdff; stroke:none;" d="M698 568C696.34 574.739 696.667 581.178 695.871 587.961C694.163 602.51 693.365 617.144 694.039 632C694.285 637.422 695.955 642.524 696 648L700 648L700 644L702 644L702 656L704 656C704 647.121 702.695 638.786 702.089 630C701.335 619.05 702.391 607.981 701.982 597.015C701.862 593.819 700.496 591.085 700.129 587.961C699.365 581.456 700 574.548 700 568L698 568z"/>
<path style="fill:#57ac83; stroke:none;" d="M1257 568L1258 569L1257 568z"/>
<path style="fill:#6abd93; stroke:none;" d="M125 569L126 570L125 569z"/>
<path style="fill:#71d4a8; stroke:none;" d="M839 569L840 570L839 569z"/>
<path style="fill:#92e7be; stroke:none;" d="M1257 569L1258 570L1257 569z"/>
<path style="fill:#a4eec9; stroke:none;" d="M124.333 570.667C124.278 570.722 124.222 571.778 124.667 571.333C124.722 571.278 124.778 570.222 124.333 570.667z"/>
<path style="fill:#12884c; stroke:none;" d="M125 570L124 576C125.654 573.823 125.994 572.575 125 570z"/>
<path style="fill:#1e8a59; stroke:none;" d="M513 570C512.438 571.8 512.176 573.122 512 575C513.318 573.074 513.651 572.234 513 570z"/>
<path style="fill:#6ab08c; stroke:none;" d="M514 570L515 571L514 570z"/>
<path style="fill:#1b945f; stroke:none;" d="M840 570L843 575C843.401 572.259 842.343 571.347 840 570z"/>
<path style="fill:#61ab86; stroke:none;" d="M1256 570L1257 571L1256 570z"/>
<path style="fill:#68c090; stroke:none;" d="M695.333 571.667C695.278 571.722 695.222 572.778 695.667 572.333C695.722 572.278 695.778 571.222 695.333 571.667z"/>
<path style="fill:#84deb8; stroke:none;" d="M840 571L841 572L840 571z"/>
<path style="fill:#70c79a; stroke:none;" d="M124 572L125 573L124 572z"/>
<path style="fill:#5dba8d; stroke:none;" d="M663.333 572.667C663.278 572.722 663.222 573.778 663.667 573.333C663.722 573.278 663.778 572.222 663.333 572.667z"/>
<path style="fill:#168952; stroke:none;" d="M694 572C693.151 578.716 693 585.229 693 592C694.953 587.347 695.941 576.669 694 572z"/>
<path style="fill:#5fa283; stroke:none;" d="M841 572L842 573L841 572z"/>
<path style="fill:#89bea2; stroke:none;" d="M1255 572L1256 573L1255 572z"/>
<path style="fill:#90dab9; stroke:none;" d="M513 573L514 574L513 573z"/>
<path style="fill:#9bedbf; stroke:none;" d="M695 573L695 576C695.696 574.446 695.696 574.554 695 573z"/>
<path style="fill:#a0e3c4; stroke:none;" d="M841 573L842 574L841 573z"/>
<path style="fill:#9bd3b6; stroke:none;" d="M123.333 574.667C123.278 574.722 123.222 575.778 123.667 575.333C123.722 575.278 123.778 574.222 123.333 574.667z"/>
<path style="fill:#288a57; stroke:none;" d="M663 574L660 592L662 592C662.053 586.089 665.342 579.594 663 574z"/>
<path style="fill:#5cac87; stroke:none;" d="M842 574L843 575L842 574z"/>
<path style="fill:#8fcfac; stroke:none;" d="M1254 574L1255 575L1254 574z"/>
<path style="fill:#6ecda1; stroke:none;" d="M512 575L513 576L512 575z"/>
<path style="fill:#129050; stroke:none;" d="M507 576L508 585C509.881 582.201 511.073 579.24 512 576L507 576z"/>
<path style="fill:#eafff5; stroke:none;" d="M695 576L695 592C696.812 587.681 696.812 580.319 695 576z"/>
<path style="fill:#77c697; stroke:none;" d="M662 577L663 578L662 577z"/>
<path style="fill:#8bc7ab; stroke:none;" d="M1252 577L1253 578L1252 577z"/>
<path style="fill:#dbffeb; stroke:none;" d="M511 578L510 582C511.656 580.596 511.751 580.047 511 578z"/>
<path style="fill:#eefff2; stroke:none;" d="M660 578C659.125 582.642 658.203 587.275 658 592C660.569 587.912 661.913 582.568 660 578z"/>
<path style="fill:#7ec49f; stroke:none;" d="M844 578L845 579L844 578z"/>
<path style="fill:#57a57d; stroke:none;" d="M1251 578L1252 579L1251 578z"/>
<path style="fill:#68b08b; stroke:none;" d="M694.333 579.667C694.278 579.722 694.222 580.778 694.667 580.333C694.722 580.278 694.778 579.222 694.333 579.667z"/>
<path style="fill:#dbffea; stroke:none;" d="M843 579C844.037 581.513 844.683 582.617 847 584C846.061 581.362 845.42 580.382 843 579z"/>
<path style="fill:#95e3bb; stroke:none;" d="M1251 579L1252 580L1251 579z"/>
<path style="fill:#14944a; stroke:none;" d="M499 580L499 588L503 588C502.942 585.216 502.648 582.709 502 580L499 580z"/>
<path style="fill:#89cda6; stroke:none;" d="M510 580L511 581L510 580z"/>
<path style="fill:#6eba93; stroke:none;" d="M1250 580L1251 581L1250 580z"/>
<path style="fill:#a5dcbc; stroke:none;" d="M661 581L662 582L661 581z"/>
<path style="fill:#94d7b4; stroke:none;" d="M694 581L694 584C694.696 582.446 694.696 582.554 694 581z"/>
<path style="fill:#69b78d; stroke:none;" d="M846 581L847 582L846 581z"/>
<path style="fill:#e7fff8; stroke:none;" d="M120 582C119.808 582.319 117.869 584.306 118 584C117.152 585.978 118.684 588.949 118 591C117.38 592.861 116.022 595.297 115 597L114 596L114 606C117.324 601.164 121.688 587.705 120 582z"/>
<path style="fill:#54a17d; stroke:none;" d="M121.333 582.667C121.278 582.722 121.222 583.778 121.667 583.333C121.722 583.278 121.778 582.222 121.333 582.667z"/>
<path style="fill:#99dab8; stroke:none;" d="M509.333 582.667C509.278 582.722 509.222 583.778 509.667 583.333C509.722 583.278 509.778 582.222 509.333 582.667z"/>
<path style="fill:#58a37c; stroke:none;" d="M1248.33 582.667C1248.28 582.723 1248.22 583.777 1248.67 583.333C1248.72 583.278 1248.78 582.222 1248.33 582.667z"/>
<path style="fill:#9ecab1; stroke:none;" d="M1249 582L1250 583L1249 582z"/>
<path style="fill:#8dc9a7; stroke:none;" d="M847 583L848 584L847 583z"/>
<path style="fill:#ceffea; stroke:none;" d="M694 584L694 592C695.161 589.23 695.161 586.77 694 584z"/>
<path style="fill:#228d59; stroke:none;" d="M848 584C849.655 586.939 851.105 589.245 854 591C852.528 587.687 851.235 585.679 848 584z"/>
<path style="fill:#8dddb8; stroke:none;" d="M120 585L121 586L120 585z"/>
<path style="fill:#6abd93; stroke:none;" d="M508 585L509 586L508 585z"/>
<path style="fill:#8af1c2; stroke:none;" d="M848 585L849 586L848 585z"/>
<path style="fill:#7be7b6; stroke:none;" d="M1247 585L1248 586L1247 585z"/>
<path style="fill:#d8ffec; stroke:none;" d="M508 586L507 592C508.654 589.823 508.994 588.575 508 586z"/>
<path style="fill:#6eae8b; stroke:none;" d="M660.333 586.667C660.278 586.722 660.222 587.778 660.667 587.333C660.722 587.278 660.778 586.222 660.333 586.667z"/>
<path style="fill:#6dbd96; stroke:none;" d="M849 586L850 587L849 586z"/>
<path style="fill:#54a67e; stroke:none;" d="M1246 586L1247 587L1246 586z"/>
<path style="fill:#6dc89c; stroke:none;" d="M119.333 588.667C119.278 588.722 119.222 589.778 119.667 589.333C119.722 589.278 119.778 588.222 119.333 588.667z"/>
<path style="fill:#168c50; stroke:none;" d="M506 588C504.51 590.78 504.089 592.848 504 596C505.61 593.438 506.628 590.986 506 588z"/>
<path style="fill:#8cd2ae; stroke:none;" d="M507 588L508 589L507 588z"/>
<path style="fill:#a4d6bb; stroke:none;" d="M850 588L851 589L850 588z"/>
<path style="fill:#9cd3b6; stroke:none;" d="M1245 588L1246 589L1245 588z"/>
<path style="fill:#c6f0d8; stroke:none;" d="M659 589L659 592C659.696 590.446 659.696 590.554 659 589z"/>
<path style="fill:#65b68d; stroke:none;" d="M851 589L852 590L851 589z"/>
<path style="fill:#6abf95; stroke:none;" d="M1244 589L1245 590L1244 589z"/>
<path style="fill:#6ab58e; stroke:none;" d="M506 590L507 591L506 590z"/>
<path style="fill:#9ae5be; stroke:none;" d="M506 591L507 592L506 591z"/>
<path style="fill:#8ceebb; stroke:none;" d="M852 591L853 592L852 591z"/>
<path style="fill:#96fec9; stroke:none;" d="M1243 591L1244 592L1243 591z"/>
<path style="fill:#c9ffe8; stroke:none;" d="M658 592L658 598C658.951 595.715 658.951 594.285 658 592z"/>
<path style="fill:#58ba89; stroke:none;" d="M659.333 592.667C659.278 592.722 659.222 593.778 659.667 593.333C659.722 593.278 659.778 592.222 659.333 592.667z"/>
<path style="fill:#159153; stroke:none;" d="M692 592L693 662L696 662C695.895 658.429 694.537 655.439 694.129 651.961C692.831 640.905 693 629.143 693 618C693 610.256 695.004 599.159 692 592z"/>
<path style="fill:#70b694; stroke:none;" d="M693 592L693 595C693.696 593.446 693.696 593.554 693 592z"/>
<path style="fill:#71b793; stroke:none;" d="M853 592L854 593L853 592z"/>
<path style="fill:#049a50; stroke:none;" d="M1216 592L1216 608L1200 608L1200 624C1197.12 624 1192.14 623.089 1189.74 625.028C1185.41 628.531 1188.49 638.777 1190 643C1187.61 643.648 1186.46 644.032 1185 646C1191.29 645.655 1195.52 642.693 1195 636C1198.02 635.055 1199.95 635.378 1203 636L1203 632C1211.02 630.767 1215.57 622.407 1222 618L1219 611L1224 610C1222.49 609.317 1221.69 609.174 1220 609C1220.18 601.352 1228.12 592.368 1216 592z"/>
<path style="fill:#83caac; stroke:none;" d="M1242 592L1243 593L1242 592z"/>
<path style="fill:#60bd90; stroke:none;" d="M118 593L119 594L118 593z"/>
<path style="fill:#76bd95; stroke:none;" d="M505 593L506 594L505 593z"/>
<path style="fill:#a8eeca; stroke:none;" d="M853 593L854 594L853 593z"/>
<path style="fill:#41b67f; stroke:none;" d="M854 593L855 594L854 593z"/>
<path style="fill:#64be9a; stroke:none;" d="M1241 593L1242 594L1241 593z"/>
<path style="fill:#d2ffe6; stroke:none;" d="M505 594L504 600C505.495 597.815 505.889 596.508 505 594z"/>
<path style="fill:#1f8f55; stroke:none;" d="M659 594C658.659 601.939 657.452 610.186 656 618C661.764 614.077 661.515 599.994 659 594z"/>
<path style="fill:#c1ffdf; stroke:none;" d="M854.333 594.667C854.278 594.722 854.222 595.778 854.667 595.333C854.722 595.278 854.778 594.222 854.333 594.667z"/>
<path style="fill:#a3e6c5; stroke:none;" d="M693 595L693 600C693.83 597.97 693.83 597.03 693 595z"/>
<path style="fill:#6ecf9c; stroke:none;" d="M855 595L856 596L855 595z"/>
<path style="fill:#98e8c3; stroke:none;" d="M1240 595L1241 596L1240 595z"/>
<path style="fill:#86cca8; stroke:none;" d="M117 596L118 597L117 596z"/>
<path style="fill:#79d0a3; stroke:none;" d="M504 596L505 597L504 596z"/>
<path style="fill:#58a176; stroke:none;" d="M856 596L857 597L856 596z"/>
<path style="fill:#7bc5a2; stroke:none;" d="M1239 596L1240 597L1239 596z"/>
<path style="fill:#a7f0c5; stroke:none;" d="M856 597L857 598L856 597z"/>
<path style="fill:#51c28e; stroke:none;" d="M1238 597L1239 598L1238 597z"/>
<path style="fill:#adeccd; stroke:none;" d="M116.333 598.667C116.278 598.722 116.222 599.778 116.667 599.333C116.722 599.278 116.778 598.222 116.333 598.667z"/>
<path style="fill:#6bc498; stroke:none;" d="M503.333 598.667C503.278 598.722 503.222 599.778 503.667 599.333C503.722 599.278 503.778 598.222 503.333 598.667z"/>
<path style="fill:#64b08c; stroke:none;" d="M658.333 598.667C658.278 598.722 658.222 599.778 658.667 599.333C658.722 599.278 658.778 598.222 658.333 598.667z"/>
<path style="fill:#90c9a8; stroke:none;" d="M857 598L858 599L857 598z"/>
<path style="fill:#58af85; stroke:none;" d="M1237 598L1238 599L1237 598z"/>
<path style="fill:#56c28e; stroke:none;" d="M858 599L859 600L858 599z"/>
<path style="fill:#90e7bd; stroke:none;" d="M1237 599L1238 600L1237 599z"/>
<path style="fill:#50a57c; stroke:none;" d="M116.333 600.667C116.278 600.722 116.222 601.778 116.667 601.333C116.722 601.278 116.778 600.222 116.333 600.667z"/>
<path style="fill:#c3ffe5; stroke:none;" d="M693 600L693 626C695.477 620.097 695.477 605.903 693 600z"/>
<path style="fill:#e2fdec; stroke:none;" d="M858 600C858.777 602.132 859.13 602.771 861 604C860.045 602.233 859.434 601.393 858 600z"/>
<path style="fill:#4fa179; stroke:none;" d="M859 600L860 601L859 600z"/>
<path style="fill:#93caad; stroke:none;" d="M1236 600L1237 601L1236 600z"/>
<path style="fill:#63b992; stroke:none;" d="M1235 601L1236 602L1235 601z"/>
<path style="fill:#bffada; stroke:none;" d="M502.333 602.667C502.278 602.722 502.222 603.778 502.667 603.333C502.722 603.278 502.778 602.222 502.333 602.667z"/>
<path style="fill:#baf0d8; stroke:none;" d="M657 602L657 605C657.696 603.446 657.696 603.554 657 602z"/>
<path style="fill:#88c8a6; stroke:none;" d="M860 602L861 603L860 602z"/>
<path style="fill:#65b290; stroke:none;" d="M1234 602L1235 603L1234 602z"/>
<path style="fill:#e9fffd; stroke:none;" d="M1235 602C1234.04 603.516 1233.61 604.339 1233 606C1235.65 605.432 1236.35 604.412 1235 602z"/>
<path style="fill:#54b588; stroke:none;" d="M861 603L862 604L861 603z"/>
<path style="fill:#70d0a2; stroke:none;" d="M115 604L116 605L115 604z"/>
<path style="fill:#1e8f57; stroke:none;" d="M115 605L113 618C115.955 614.284 116.86 609.433 115 605z"/>
<path style="fill:#7dcba3; stroke:none;" d="M501 605L502 606L501 605z"/>
<path style="fill:#73b398; stroke:none;" d="M657 605L657 608C657.696 606.446 657.696 606.554 657 605z"/>
<path style="fill:#a0ddbc; stroke:none;" d="M862 605L863 606L862 605z"/>
<path style="fill:#8ed5b7; stroke:none;" d="M1232 605L1233 606L1232 605z"/>
<path style="fill:#bdf6d5; stroke:none;" d="M114.333 606.667C114.278 606.722 114.222 607.778 114.667 607.333C114.722 607.278 114.778 606.222 114.333 606.667z"/>
<path style="fill:#96c9aa; stroke:none;" d="M863 606L864 607L863 606z"/>
<path style="fill:#63d496; stroke:none;" d="M1231 606L1232 607L1231 606z"/>
<path style="fill:#48c080; stroke:none;" d="M1230 607L1231 608L1230 607z"/>
<path style="fill:#defaeb; stroke:none;" d="M1231 607L1227 612C1229.52 610.752 1231.08 609.895 1231 607z"/>
<path style="fill:#65ab87; stroke:none;" d="M114.333 608.667C114.278 608.722 114.222 609.778 114.667 609.333C114.722 609.278 114.778 608.222 114.333 608.667z"/>
<path style="fill:#27885b; stroke:none;" d="M498 608C497.248 612.443 496.041 616.438 496 621C498.132 617.078 499.348 612.412 500 608L498 608z"/>
<path style="fill:#b8edcf; stroke:none;" d="M500.333 608.667C500.278 608.722 500.222 609.778 500.667 609.333C500.722 609.278 500.778 608.222 500.333 608.667z"/>
<path style="fill:#f4ffff; stroke:none;" d="M654 608L654 614L650 614C649.953 618.251 648.35 621.894 648.054 626.039C646.972 641.198 648 656.8 648 672L650 672L650 624C657.538 622.703 656 613.965 656 608L654 608z"/>
<path style="fill:#bbffe2; stroke:none;" d="M656 608L656 612C656.71 610.24 656.71 609.759 656 608z"/>
<path style="fill:#049a50; stroke:none;" d="M670 608L670 624C665.899 623.997 662.036 623.749 658 623C658.957 621.484 659.387 620.661 660 619L656 618C656 639.809 654.501 662.212 656.054 683.961C656.626 691.969 659.645 699.864 660 708C664.498 703.544 662 694.086 662 688L668 688C668 695.65 669.517 702.565 670 710L672 710C672 704.177 670.108 698.592 670.108 693C670.108 689.556 671.852 686.457 671.982 682.985C672.911 658.067 672 632.938 672 608L670 608z"/>
<path style="fill:#2a8d5f; stroke:none;" d="M865 608C865.545 609.635 865.365 609.455 867 610L867 608L865 608z"/>
<path style="fill:#61a687; stroke:none;" d="M1229 608L1230 609L1229 608z"/>
<path style="fill:#e9fcf6; stroke:none;" d="M500 610L496 624C500.181 621.442 501.623 614.526 500 610z"/>
<path style="fill:#87d2ab; stroke:none;" d="M113 611L113 614C113.696 612.446 113.696 612.554 113 611z"/>
<path style="fill:#82d3a8; stroke:none;" d="M499 611L500 612L499 611z"/>
<path style="fill:#95dfba; stroke:none;" d="M867 611L868 612L867 611z"/>
<path style="fill:#bdffda; stroke:none;" d="M112 612L112 616C112.71 614.24 112.71 613.76 112 612z"/>
<path style="fill:#7ee0ad; stroke:none;" d="M656.333 612.667C656.278 612.722 656.222 613.778 656.667 613.333C656.722 613.278 656.778 612.222 656.333 612.667z"/>
<path style="fill:#91b19c; stroke:none;" d="M868 612L869 613L868 612z"/>
<path style="fill:#5eac84; stroke:none;" d="M869 613L870 614L869 613z"/>
<path style="fill:#80d7ac; stroke:none;" d="M1225 613L1226 614L1225 613z"/>
<path style="fill:#9debc3; stroke:none;" d="M498.333 614.667C498.278 614.722 498.222 615.778 498.667 615.333C498.722 615.278 498.778 614.222 498.333 614.667z"/>
<path style="fill:#57bf8a; stroke:none;" d="M656.333 614.667C656.278 614.722 656.222 615.778 656.667 615.333C656.722 615.278 656.778 614.222 656.333 614.667z"/>
<path style="fill:#7dbd9a; stroke:none;" d="M1224 614L1225 615L1224 614z"/>
<path style="fill:#65c595; stroke:none;" d="M1223 615L1224 616L1223 615z"/>
<path style="fill:#eeffff; stroke:none;" d="M111 616C108.161 620.606 108.591 623.78 109 629C110.836 625.21 112.569 620.03 111 616z"/>
<path style="fill:#5cbf89; stroke:none;" d="M112.333 616.667C112.278 616.722 112.222 617.778 112.667 617.333C112.722 617.278 112.778 616.222 112.333 616.667z"/>
<path style="fill:#3f9568; stroke:none;" d="M872 616C872.545 617.635 872.365 617.455 874 618L874 616L872 616z"/>
<path style="fill:#61ac85; stroke:none;" d="M1222 616L1223 617L1222 616z"/>
<path style="fill:#71b093; stroke:none;" d="M497 617L498 618L497 617z"/>
<path style="fill:#dffeee; stroke:none;" d="M1216 624C1219.6 622.382 1221.98 620.396 1224 617C1220.44 618.663 1218.27 620.805 1216 624z"/>
<path style="fill:#53aa80; stroke:none;" d="M1220 618L1221 619L1220 618z"/>
<path style="fill:#9fedc5; stroke:none;" d="M874 619L875 620L874 619z"/>
<path style="fill:#49b584; stroke:none;" d="M1219 619L1220 620L1219 619z"/>
<path style="fill:#bfd4cd; stroke:none;" d="M655 620L655 624C655.71 622.241 655.71 621.76 655 620z"/>
<path style="fill:#ceffe5; stroke:none;" d="M875.333 620.667C875.278 620.722 875.222 621.778 875.667 621.333C875.722 621.278 875.778 620.222 875.333 620.667z"/>
<path style="fill:#61a785; stroke:none;" d="M1218 620L1219 621L1218 620z"/>
<path style="fill:#96b1a8; stroke:none;" d="M111 621L112 622L111 621z"/>
<path style="fill:#9fd2c1; stroke:none;" d="M496 621L497 622L496 621z"/>
<path style="fill:#93e6bc; stroke:none;" d="M876 621L877 622L876 621z"/>
<path style="fill:#5aa584; stroke:none;" d="M1217 621L1218 622L1217 621z"/>
<path style="fill:#4c7064; stroke:none;" d="M111.333 622.667C111.278 622.722 111.222 623.778 111.667 623.333C111.722 623.278 111.778 622.222 111.333 622.667z"/>
<path style="fill:#c7ffe8; stroke:none;" d="M654 622L654 640C655.952 635.349 655.952 626.651 654 622z"/>
<path style="fill:#c6ffde; stroke:none;" d="M877.333 622.667C877.278 622.722 877.222 623.778 877.667 623.333C877.722 623.278 877.778 622.222 877.333 622.667z"/>
<path style="fill:#71a189; stroke:none;" d="M1216 622L1217 623L1216 622z"/>
<path style="fill:#7be3b2; stroke:none;" d="M878 623L879 624L878 623z"/>
<path style="fill:#1db369; stroke:none;" d="M1214.67 623.333C1214.22 623.777 1215.28 623.722 1215.33 623.667C1215.78 623.222 1214.72 623.278 1214.67 623.333z"/>
<path style="fill:#85f1c0; stroke:none;" d="M110.333 624.667C110.278 624.722 110.222 625.778 110.667 625.333C110.722 625.278 110.778 624.222 110.333 624.667z"/>
<path style="fill:#288654; stroke:none;" d="M493 624L493 628L495 628L495 624L493 624z"/>
<path style="fill:#81b898; stroke:none;" d="M495 624L496 625L495 624z"/>
<path style="fill:#62ba92; stroke:none;" d="M655 624L655 629C655.83 626.97 655.83 626.03 655 624z"/>
<path style="fill:#228849; stroke:none;" d="M1204 624L1204 626L1207 626L1207 624L1204 624z"/>
<path style="fill:#799a87; stroke:none;" d="M1214 624L1215 625L1214 624z"/>
<path style="fill:#e0faed; stroke:none;" d="M1215 624C1212.46 626.563 1210.08 629.048 1208 632C1211.44 630.393 1215.09 628.074 1215 624z"/>
<path style="fill:#d9ffee; stroke:none;" d="M495 625L495 629C495.71 627.24 495.71 626.759 495 625z"/>
<path style="fill:#48c38c; stroke:none;" d="M880 625L881 626L880 625z"/>
<path style="fill:#5ba886; stroke:none;" d="M1213 625L1214 626L1213 625z"/>
<path style="fill:#35a774; stroke:none;" d="M110.333 626.667C110.278 626.722 110.222 627.778 110.667 627.333C110.722 627.278 110.778 626.222 110.333 626.667z"/>
<path style="fill:#9edfbf; stroke:none;" d="M693 626L693 632C693.951 629.715 693.951 628.285 693 626z"/>
<path style="fill:#79b291; stroke:none;" d="M881 626L882 627L881 626z"/>
<path style="fill:#6ac196; stroke:none;" d="M882 627L883 628L882 627z"/>
<path style="fill:#4fb284; stroke:none;" d="M1211 627L1212 628L1211 627z"/>
<path style="fill:#15915b; stroke:none;" d="M493 628C492.031 629.6 491.28 630.663 490 632L491 635L489 634C489.562 637.025 489.936 639.922 490 643C491.971 638.931 494.335 632.396 493 628z"/>
<path style="fill:#96dab5; stroke:none;" d="M494 628L495 629L494 628z"/>
<path style="fill:#93caab; stroke:none;" d="M883 628L884 629L883 628z"/>
<path style="fill:#639e7e; stroke:none;" d="M1210 628L1211 629L1210 628z"/>
<path style="fill:#90d7af; stroke:none;" d="M109 629L109 632C109.696 630.446 109.696 630.554 109 629z"/>
<path style="fill:#1b8d5a; stroke:none;" d="M655 629L655 672C658.507 663.643 656 650.06 656 641C656 637.017 656.552 632.699 655 629z"/>
<path style="fill:#76d1a5; stroke:none;" d="M884 629L885 630L884 629z"/>
<path style="fill:#58b185; stroke:none;" d="M1209 629L1210 630L1209 629z"/>
<path style="fill:#a2c2ad; stroke:none;" d="M885 630L886 631L885 630z"/>
<path style="fill:#79bd98; stroke:none;" d="M1208 630L1209 631L1208 630z"/>
<path style="fill:#6ec09a; stroke:none;" d="M493 631L494 632L493 631z"/>
<path style="fill:#8fd2af; stroke:none;" d="M886 631L887 632L886 631z"/>
<path style="fill:#61c795; stroke:none;" d="M1207 631L1208 632L1207 631z"/>
<path style="fill:#159057; stroke:none;" d="M109 632L106 652L108 652C108.341 645.338 111.745 638.542 109 632z"/>
<path style="fill:#bbfcde; stroke:none;" d="M493.333 632.667C493.278 632.722 493.222 633.778 493.667 633.333C493.722 633.278 493.778 632.222 493.333 632.667z"/>
<path style="fill:#6fb996; stroke:none;" d="M693 632L693 636C693.71 634.24 693.71 633.76 693 632z"/>
<path style="fill:#c4f9df; stroke:none;" d="M887 632L887 634L889 634C888.455 632.365 888.635 632.545 887 632z"/>
<path style="fill:#288f5a; stroke:none;" d="M1203 635C1204.46 633.897 1204.9 633.457 1206 632C1203.64 632.482 1203.48 632.636 1203 635z"/>
<path style="fill:#94caaa; stroke:none;" d="M1206 632L1207 633L1206 632z"/>
<path style="fill:#9be7c0; stroke:none;" d="M1205 633L1206 634L1205 633z"/>
<path style="fill:#cffff7; stroke:none;" d="M493 634L492 640L494 640C493.953 637.747 493.874 636.052 493 634z"/>
<path style="fill:#f1fef4; stroke:none;" d="M653 634L653 668C655.802 661.323 654 651.215 654 644C654 640.581 654.328 637.166 653 634z"/>
<path style="fill:#258659; stroke:none;" d="M890 634C891.45 635.608 892.051 636.064 894 637C892.703 635.08 892.209 634.684 890 634z"/>
<path style="fill:#108f4c; stroke:none;" d="M1195 636L1195 641C1198.33 639.201 1199.84 637.844 1200 634C1198.11 634.465 1196.72 635.066 1195 636z"/>
<path style="fill:#dbfff4; stroke:none;" d="M1204 634C1202.65 635.768 1201.88 636.982 1201 639C1203.24 637.567 1204.41 636.693 1204 634z"/>
<path style="fill:#62b484; stroke:none;" d="M108.333 635.667C108.278 635.722 108.222 636.778 108.667 636.333C108.722 636.278 108.778 635.222 108.333 635.667z"/>
<path style="fill:#73cba5; stroke:none;" d="M492 635L493 636L492 635z"/>
<path style="fill:#e5fff0; stroke:none;" d="M694 636L695 652C696.982 647.278 696.39 640.496 694 636z"/>
<path style="fill:#61a888; stroke:none;" d="M1201 636L1202 637L1201 636z"/>
<path style="fill:#58b589; stroke:none;" d="M1200 637L1201 638L1200 637z"/>
<path style="fill:#61cfa2; stroke:none;" d="M491.333 638.667C491.278 638.722 491.222 639.778 491.667 639.333C491.722 639.278 491.778 638.222 491.333 638.667z"/>
<path style="fill:#3c946a; stroke:none;" d="M894.667 638.333C894.222 638.778 895.278 638.722 895.333 638.667C895.778 638.222 894.722 638.278 894.667 638.333z"/>
<path style="fill:#61cf92; stroke:none;" d="M1199 638L1200 639L1199 638z"/>
<path style="fill:#72caa0; stroke:none;" d="M895 639L896 640L895 639z"/>
<path style="fill:#6ce3a3; stroke:none;" d="M1198 639L1199 640L1198 639z"/>
<path style="fill:#cdffea; stroke:none;" d="M106 640L106 646C106.951 643.715 106.951 642.285 106 640z"/>
<path style="fill:#65c094; stroke:none;" d="M107.333 640.667C107.278 640.722 107.222 641.778 107.667 641.333C107.722 641.278 107.778 640.222 107.333 640.667z"/>
<path style="fill:#daffea; stroke:none;" d="M491 640L490 648C491.825 645.328 492.232 643.023 491 640z"/>
<path style="fill:#9ae5c4; stroke:none;" d="M654 640L654 664C656.349 658.403 656.349 645.597 654 640z"/>
<path style="fill:#a7ddc6; stroke:none;" d="M896 640L897 641L896 640z"/>
<path style="fill:#1f8256; stroke:none;" d="M897 640C898.223 641.195 898.42 641.316 900 642C899.02 640.217 899.092 640.406 897 640z"/>
<path style="fill:#5b9d81; stroke:none;" d="M1196 640L1197 641L1196 640z"/>
<path style="fill:#e9fff9; stroke:none;" d="M896 641L897 645C898.388 644.309 898.861 644.015 900 643L896 641z"/>
<path style="fill:#9df5cf; stroke:none;" d="M897 641L898 642L897 641z"/>
<path style="fill:#58ba93; stroke:none;" d="M1195 641L1196 642L1195 641z"/>
<path style="fill:#68a586; stroke:none;" d="M899 642L900 643L899 642z"/>
<path style="fill:#199b5b; stroke:none;" d="M900 642C901.33 643.753 902.247 644.67 904 646C903.03 643.508 902.525 642.854 900 642z"/>
<path style="fill:#7cbd9f; stroke:none;" d="M1194 642L1195 643L1194 642z"/>
<path style="fill:#81d0a3; stroke:none;" d="M490 643L491 644L490 643z"/>
<path style="fill:#86d0ab; stroke:none;" d="M694.333 643.667C694.278 643.722 694.222 644.778 694.667 644.333C694.722 644.278 694.778 643.222 694.333 643.667z"/>
<path style="fill:#72c79d; stroke:none;" d="M900 643L901 644L900 643z"/>
<path style="fill:#7fd4ad; stroke:none;" d="M1193 643L1194 644L1193 643z"/>
<path style="fill:#b6d9c1; stroke:none;" d="M901 644L902 645L901 644z"/>
<path style="fill:#e0fff6; stroke:none;" d="M1189 648C1191.61 647.162 1192.46 646.29 1194 644C1191.64 645.104 1190.62 645.956 1189 648z"/>
<path style="fill:#5caa83; stroke:none;" d="M694 645L694 648C694.696 646.446 694.696 646.554 694 645z"/>
<path style="fill:#b3f7d2; stroke:none;" d="M902 645L903 646L902 645z"/>
<path style="fill:#53c68f; stroke:none;" d="M1190 645L1191 646L1190 645z"/>
<path style="fill:#e8fff6; stroke:none;" d="M105 646C104.198 649.368 104.01 652.545 104 656C105.398 652.886 106.163 649.242 105 646z"/>
<path style="fill:#5bad85; stroke:none;" d="M106.333 646.667C106.278 646.722 106.222 647.778 106.667 647.333C106.722 647.278 106.778 646.222 106.333 646.667z"/>
<path style="fill:#f4fafa; stroke:none;" d="M901 647C902.377 647.911 903.44 648.404 905 649C903.2 649.562 901.878 649.824 900 650L900 654C904.056 654.494 909.998 655.019 913.675 656.723C919.112 659.243 922.672 663.528 929 664C924.626 659.265 918.216 656.632 913 652.848C909.152 650.056 905.909 646.713 901 647z"/>
<path style="fill:#449165; stroke:none;" d="M904.667 646.333C904.222 646.778 905.278 646.722 905.333 646.667C905.778 646.222 904.722 646.277 904.667 646.333z"/>
<path style="fill:#74c09c; stroke:none;" d="M1189 646L1190 647L1189 646z"/>
<path style="fill:#6ec497; stroke:none;" d="M489 647L490 648L489 647z"/>
<path style="fill:#7bc89c; stroke:none;" d="M905 647L906 648L905 647z"/>
<path style="fill:#7ae0b0; stroke:none;" d="M1188 647L1189 648L1188 647z"/>
<path style="fill:#338a60; stroke:none;" d="M694 648L694 656C695.161 653.23 695.161 650.77 694 648z"/>
<path style="fill:#5f9875; stroke:none;" d="M907 648L908 649L907 648z"/>
<path style="fill:#1a8c51; stroke:none;" d="M908 648L909 650C910.635 649.455 910.455 649.635 911 648L908 648z"/>
<path style="fill:#5ea988; stroke:none;" d="M1186 648L1187 649L1186 648z"/>
<path style="fill:#e2fffb; stroke:none;" d="M1187 648L1184 652C1186.48 651.317 1187.67 650.607 1187 648z"/>
<path style="fill:#6cc492; stroke:none;" d="M908 649L909 650L908 649z"/>
<path style="fill:#6ec6a0; stroke:none;" d="M1185 649L1186 650L1185 649z"/>
<path style="fill:#9de5c0; stroke:none;" d="M105.333 650.667C105.278 650.722 105.222 651.778 105.667 651.333C105.722 651.278 105.778 650.222 105.333 650.667z"/>
<path style="fill:#a0c8b0; stroke:none;" d="M909 650L910 651L909 650z"/>
<path style="fill:#338157; stroke:none;" d="M910 650C910.545 651.635 910.365 651.455 912 652L912 650L910 650z"/>
<path style="fill:#92d9bb; stroke:none;" d="M1184 650L1185 651L1184 650z"/>
<path style="fill:#63b189; stroke:none;" d="M488 651L489 652L488 651z"/>
<path style="fill:#95ffcd; stroke:none;" d="M1183 651L1184 652L1183 651z"/>
<path style="fill:#5dad86; stroke:none;" d="M105.333 652.667C105.278 652.722 105.222 653.778 105.667 653.333C105.722 653.278 105.778 652.222 105.333 652.667z"/>
<path style="fill:#cdffe8; stroke:none;" d="M488 652L488 656C488.71 654.241 488.71 653.759 488 652z"/>
<path style="fill:#99ceb4; stroke:none;" d="M695.333 652.667C695.278 652.722 695.222 653.778 695.667 653.333C695.722 653.278 695.778 652.222 695.333 652.667z"/>
<path style="fill:#83c09e; stroke:none;" d="M912 652L913 653L912 652z"/>
<path style="fill:#228254; stroke:none;" d="M913 652C914.896 654.02 916.415 654.995 919 656C917.881 652.807 916.284 652.375 913 652z"/>
<path style="fill:#2e855b; stroke:none;" d="M1178 652L1179 654C1180.63 653.455 1180.45 653.635 1181 652L1178 652z"/>
<path style="fill:#8fc4a6; stroke:none;" d="M1181 652L1182 653L1181 652z"/>
<path style="fill:#94e2ba; stroke:none;" d="M913 653L914 654L913 653z"/>
<path style="fill:#79af95; stroke:none;" d="M695.333 654.667C695.278 654.722 695.222 655.778 695.667 655.333C695.722 655.278 695.778 654.222 695.333 654.667z"/>
<path style="fill:#dffffa; stroke:none;" d="M896 654L896 656L898 656L898 654L896 654z"/>
<path style="fill:#89bea4; stroke:none;" d="M915 654L916 655L915 654z"/>
<path style="fill:#7ec8a3; stroke:none;" d="M1178 654L1179 655L1178 654z"/>
<path style="fill:#defff6; stroke:none;" d="M1178 656L1181 656L1180 654C1178.36 654.545 1178.55 654.365 1178 656z"/>
<path style="fill:#a0eec7; stroke:none;" d="M916 655L917 656L916 655z"/>
<path style="fill:#8df0c2; stroke:none;" d="M1177 655L1178 656L1177 655z"/>
<path style="fill:#99f2c2; stroke:none;" d="M104 656L104 659C104.696 657.446 104.696 657.554 104 656z"/>
<path style="fill:#1d8756; stroke:none;" d="M486 656C485.363 660.729 484.1 665.196 484 670C486.079 666.097 487.641 660.204 486 656z"/>
<path style="fill:#a6dec1; stroke:none;" d="M487.333 656.667C487.278 656.722 487.222 657.778 487.667 657.333C487.723 657.277 487.777 656.223 487.333 656.667z"/>
<path style="fill:#c2ffe6; stroke:none;" d="M696 656L696 660C696.71 658.241 696.71 657.759 696 656z"/>
<path style="fill:#92bca8; stroke:none;" d="M918 656L919 657L918 656z"/>
<path style="fill:#2a8b58; stroke:none;" d="M919 656C920.223 657.195 920.42 657.316 922 658C921.02 656.217 921.092 656.406 919 656z"/>
<path style="fill:#1b814f; stroke:none;" d="M1169 660L1175 657C1171.95 655.938 1170.43 657.268 1169 660z"/>
<path style="fill:#87b79d; stroke:none;" d="M1175 656L1176 657L1175 656z"/>
<path style="fill:#e0fff1; stroke:none;" d="M1168 661C1169.38 661.911 1170.44 662.404 1172 663C1174.07 660.653 1176.41 658.68 1178 656C1174.36 657.152 1171.25 659.012 1168 661z"/>
<path style="fill:#aee9cb; stroke:none;" d="M919 657L920 658L919 657z"/>
<path style="fill:#89c9a6; stroke:none;" d="M921 658L922 659L921 658z"/>
<path style="fill:#12944c; stroke:none;" d="M1157 658L1157 662C1159.3 662.674 1159.77 662.965 1161 665L1165 663L1165 660C1162.26 658.572 1160.09 658.092 1157 658z"/>
<path style="fill:#88d0aa; stroke:none;" d="M1172 658L1173 659L1172 658z"/>
<path style="fill:#3aa16c; stroke:none;" d="M104.333 659.667C104.278 659.722 104.222 660.778 104.667 660.333C104.722 660.278 104.778 659.222 104.333 659.667z"/>
<path style="fill:#50b37d; stroke:none;" d="M923 659L924 660L923 659z"/>
<path style="fill:#4fb583; stroke:none;" d="M1170 659L1171 660L1170 659z"/>
<path style="fill:#76cba1; stroke:none;" d="M696.333 660.667C696.278 660.722 696.222 661.778 696.667 661.333C696.722 661.278 696.778 660.222 696.333 660.667z"/>
<path style="fill:#358761; stroke:none;" d="M925 660C926.139 661.016 926.612 661.309 928 662C927.02 660.217 927.092 660.406 925 660z"/>
<path style="fill:#568771; stroke:none;" d="M1168 660L1169 661L1168 660z"/>
<path style="fill:#78c8a1; stroke:none;" d="M486 661L487 662L486 661z"/>
<path style="fill:#82c8a6; stroke:none;" d="M926 661L927 662L926 661z"/>
<path style="fill:#40c586; stroke:none;" d="M1167 661L1168 662L1167 661z"/>
<path style="fill:#c6f4da; stroke:none;" d="M103.333 662.667C103.278 662.722 103.222 663.778 103.667 663.333C103.722 663.278 103.778 662.222 103.333 662.667z"/>
<path style="fill:#118b50; stroke:none;" d="M696 662L697 678C698.862 673.554 697.854 666.377 696 662z"/>
<path style="fill:#c5fede; stroke:none;" d="M697 662L697 666C697.71 664.241 697.71 663.759 697 662z"/>
<path style="fill:#449d71; stroke:none;" d="M928.667 662.333C928.222 662.778 929.278 662.722 929.333 662.667C929.778 662.222 928.722 662.278 928.667 662.333z"/>
<path style="fill:#159356; stroke:none;" d="M930 662C931.465 664.122 932.457 664.474 935 665C933.372 663.257 932.303 662.617 930 662z"/>
<path style="fill:#68a180; stroke:none;" d="M1165 662L1166 663L1165 662z"/>
<path style="fill:#abdebf; stroke:none;" d="M1166 662L1167 663L1166 662z"/>
<path style="fill:#dffff3; stroke:none;" d="M1167 662L1165 663C1166.48 663.791 1167.8 663.87 1167 662z"/>
<path style="fill:#8ee7bb; stroke:none;" d="M929 663L930 664L929 663z"/>
<path style="fill:#86d4ac; stroke:none;" d="M1164 663L1165 664L1164 663z"/>
<path style="fill:#8cc3a6; stroke:none;" d="M103.333 664.667C103.278 664.722 103.222 665.778 103.667 665.333C103.722 665.278 103.778 664.222 103.333 664.667z"/>
<path style="fill:#beffe5; stroke:none;" d="M654 664L654 672C655.161 669.23 655.161 666.77 654 664z"/>
<path style="fill:#a1cdb6; stroke:none;" d="M931 664L932 665L931 664z"/>
<path style="fill:#89d5af; stroke:none;" d="M1162 664L1163 665L1162 664z"/>
<path style="fill:#8ee5bb; stroke:none;" d="M933 665L934 666L933 665z"/>
<path style="fill:#41b07d; stroke:none;" d="M934 665L935 666L934 665z"/>
<path style="fill:#90efc3; stroke:none;" d="M1160.67 665.333C1160.22 665.778 1161.28 665.722 1161.33 665.667C1161.78 665.222 1160.72 665.278 1160.67 665.333z"/>
<path style="fill:#387f5d; stroke:none;" d="M103 666L103 672C103.951 669.715 103.951 668.285 103 666z"/>
<path style="fill:#a2eac5; stroke:none;" d="M485.333 666.667C485.278 666.722 485.222 667.778 485.667 667.333C485.722 667.278 485.778 666.222 485.333 666.667z"/>
<path style="fill:#7ecaa3; stroke:none;" d="M697.333 666.667C697.278 666.722 697.222 667.778 697.667 667.333C697.722 667.278 697.778 666.222 697.333 666.667z"/>
<path style="fill:#a5d1b8; stroke:none;" d="M934.667 666.333C934.222 666.778 935.278 666.722 935.333 666.667C935.778 666.222 934.722 666.278 934.667 666.333z"/>
<path style="fill:#74b492; stroke:none;" d="M1158 666L1159 667L1158 666z"/>
<path style="fill:#d0fbe0; stroke:none;" d="M698 667L698 672C698.83 669.97 698.83 669.03 698 667z"/>
<path style="fill:#73c499; stroke:none;" d="M937 667L938 668L937 667z"/>
<path style="fill:#1f905a; stroke:none;" d="M1146 669C1146.21 670.661 1146.5 670.54 1145 671C1148.44 671.954 1151.69 670.119 1155 669C1151.85 667.315 1149.34 668.01 1146 669z"/>
<path style="fill:#50b27f; stroke:none;" d="M1156 667L1157 668L1156 667z"/>
<path style="fill:#42956b; stroke:none;" d="M697.333 668.667C697.278 668.722 697.222 669.778 697.667 669.333C697.722 669.278 697.778 668.222 697.333 668.667z"/>
<path style="fill:#80bb9d; stroke:none;" d="M939 668L940 669L939 668z"/>
<path style="fill:#aadfc5; stroke:none;" d="M1155 668L1156 669L1155 668z"/>
<path style="fill:#ddfff8; stroke:none;" d="M1156 668L1154 669C1155.48 669.791 1156.8 669.87 1156 668z"/>
<path style="fill:#6dcea1; stroke:none;" d="M941 669L942 670L941 669z"/>
<path style="fill:#9be8c6; stroke:none;" d="M1153 669L1154 670L1153 669z"/>
<path style="fill:#b5d7c9; stroke:none;" d="M102.333 670.667C102.278 670.722 102.222 671.778 102.667 671.333C102.722 671.278 102.778 670.222 102.333 670.667z"/>
<path style="fill:#59b284; stroke:none;" d="M484.333 670.667C484.278 670.722 484.222 671.778 484.667 671.333C484.722 671.277 484.778 670.222 484.333 670.667z"/>
<path style="fill:#b4c9ba; stroke:none;" d="M943 670L944 671L943 670z"/>
<path style="fill:#67ca9e; stroke:none;" d="M1150 670L1151 671L1150 670z"/>
<path style="fill:#b7ffee; stroke:none;" d="M1151 670L1149 671C1150.48 671.791 1151.8 671.87 1151 670z"/>
<path style="fill:#9fffd2; stroke:none;" d="M944 671L945 672L944 671z"/>
<path style="fill:#5fd797; stroke:none;" d="M945 671L946 672L945 671z"/>
<path style="fill:#76e1b3; stroke:none;" d="M1148 671L1149 672L1148 671z"/>
<path style="fill:#6ec497; stroke:none;" d="M102.333 672.667C102.278 672.722 102.222 673.778 102.667 673.333C102.722 673.278 102.778 672.222 102.333 672.667z"/>
<path style="fill:#2a8559; stroke:none;" d="M483 672L483 677C483.83 674.969 483.83 674.03 483 672z"/>
<path style="fill:#b1e6cc; stroke:none;" d="M484.333 672.667C484.278 672.722 484.222 673.778 484.667 673.333C484.722 673.278 484.778 672.222 484.333 672.667z"/>
<path style="fill:#39725f; stroke:none;" d="M655 672L655 675C655.696 673.446 655.696 673.554 655 672z"/>
<path style="fill:#71bc95; stroke:none;" d="M698.333 672.667C698.278 672.722 698.222 673.778 698.667 673.333C698.722 673.278 698.778 672.222 698.333 672.667z"/>
<path style="fill:#eafff8; stroke:none;" d="M944 672C947.74 677.954 958.377 679.845 965 680C959.383 676.449 950.547 673.134 944 672z"/>
<path style="fill:#a7d1c3; stroke:none;" d="M947 672L948 673L947 672z"/>
<path style="fill:#619b85; stroke:none;" d="M948 672L949 673L948 672z"/>
<path style="fill:#168550; stroke:none;" d="M1140 672L1140 674L1142 674L1142 672L1140 672z"/>
<path style="fill:#327c59; stroke:none;" d="M1142 674L1145 673C1143.22 672.045 1142.98 672.234 1142 674z"/>
<path style="fill:#749f8c; stroke:none;" d="M1145 672L1146 673L1145 672z"/>
<path style="fill:#b6d8c8; stroke:none;" d="M1146 672L1147 673L1146 672z"/>
<path style="fill:#95e0c1; stroke:none;" d="M949 673L950 674L949 673z"/>
<path style="fill:#53b28a; stroke:none;" d="M950 673L951 674L950 673z"/>
<path style="fill:#81cba8; stroke:none;" d="M1143 673L1144 674L1143 673z"/>
<path style="fill:#0e9354; stroke:none;" d="M102 674L101 704C102.689 699.974 101.85 695.316 102.039 691C102.285 685.4 104.249 679.36 102 674z"/>
<path style="fill:#d0ffe6; stroke:none;" d="M484 674L483 688C484.648 684.109 485.614 677.957 484 674z"/>
<path style="fill:#298657; stroke:none;" d="M698 674C698.058 676.784 698.352 679.291 699 682C700.038 679.102 699.433 676.696 698 674z"/>
<path style="fill:#72be98; stroke:none;" d="M952 674L953 675L952 674z"/>
<path style="fill:#1d9150; stroke:none;" d="M1120 674L1120 676L1124 676L1124 674L1120 674z"/>
<path style="fill:#217d4e; stroke:none;" d="M1136 676L1140 675C1137.95 674.249 1137.4 674.344 1136 676z"/>
<path style="fill:#76be98; stroke:none;" d="M1140 674L1141 675L1140 674z"/>
<path style="fill:#7fae9e; stroke:none;" d="M655 675L655 678C655.696 676.446 655.696 676.554 655 675z"/>
<path style="fill:#b4ffe2; stroke:none;" d="M952.667 675.333C952.222 675.778 953.278 675.722 953.333 675.667C953.778 675.222 952.722 675.278 952.667 675.333z"/>
<path style="fill:#81e3b4; stroke:none;" d="M954 675L955 676L954 675z"/>
<path style="fill:#5ab687; stroke:none;" d="M1137 675L1138 676L1137 675z"/>
<path style="fill:#497f65; stroke:none;" d="M957 676C958.249 676.685 958.548 676.749 960 677C958.752 676.315 958.452 676.251 957 676z"/>
<path style="fill:#218c56; stroke:none;" d="M1124 678C1127.46 679.347 1130.56 678.09 1134 677C1130.44 675.543 1127.57 677.204 1124 678z"/>
<path style="fill:#8dd3b1; stroke:none;" d="M1134.67 676.333C1134.22 676.778 1135.28 676.722 1135.33 676.667C1135.78 676.222 1134.72 676.278 1134.67 676.333z"/>
<path style="fill:#94dcb6; stroke:none;" d="M483.333 677.667C483.278 677.722 483.222 678.778 483.667 678.333C483.722 678.278 483.778 677.222 483.333 677.667z"/>
<path style="fill:#9fdabe; stroke:none;" d="M699 677L700 678L699 677z"/>
<path style="fill:#91d0b3; stroke:none;" d="M959 677L960 678L959 677z"/>
<path style="fill:#80d7ac; stroke:none;" d="M1132 677L1133 678L1132 677z"/>
<path style="fill:#c7ffed; stroke:none;" d="M1133 677C1134.25 677.685 1134.55 677.749 1136 678C1134.75 677.315 1134.45 677.251 1133 677z"/>
<path style="fill:#e2fff0; stroke:none;" d="M100 678C98.5221 684.208 99 690.654 99 697C100.863 692.559 101.817 682.456 100 678z"/>
<path style="fill:#9fe0be; stroke:none;" d="M101 678L101 682C101.71 680.24 101.71 679.76 101 678z"/>
<path style="fill:#bfe0d5; stroke:none;" d="M655 678L655 682C655.71 680.24 655.71 679.76 655 678z"/>
<path style="fill:#d8fff4; stroke:none;" d="M700 678L701 686C702.232 682.977 701.825 680.672 700 678z"/>
<path style="fill:#568c72; stroke:none;" d="M962.667 678.333C962.222 678.778 963.278 678.722 963.333 678.667C963.778 678.222 962.722 678.278 962.667 678.333z"/>
<path style="fill:#2a7650; stroke:none;" d="M964 678C965.139 679.016 965.611 679.309 967 680C966.02 678.217 966.092 678.406 964 678z"/>
<path style="fill:#99c4b0; stroke:none;" d="M1129 678L1130 679L1129 678z"/>
<path style="fill:#72be98; stroke:none;" d="M965 679L966 680L965 679z"/>
<path style="fill:#3fb680; stroke:none;" d="M1125 679L1126 680L1125 679z"/>
<path style="fill:#b2ffde; stroke:none;" d="M1126.67 679.333C1126.22 679.777 1127.28 679.722 1127.33 679.667C1127.78 679.222 1126.72 679.278 1126.67 679.333z"/>
<path style="fill:#d6ffec; stroke:none;" d="M966 680C967.314 681.618 967.877 681.693 970 682C968.542 680.919 967.752 680.564 966 680z"/>
<path style="fill:#458f6a; stroke:none;" d="M968.667 680.333C968.222 680.778 969.278 680.722 969.333 680.667C969.778 680.222 968.722 680.278 968.667 680.333z"/>
<path style="fill:#179158; stroke:none;" d="M1107 682C1111.14 684.7 1115.7 682.647 1120 681C1115.6 679.158 1111.4 680.835 1107 682z"/>
<path style="fill:#468260; stroke:none;" d="M1120.67 680.333C1120.22 680.777 1121.28 680.723 1121.33 680.667C1121.78 680.222 1120.72 680.278 1120.67 680.333z"/>
<path style="fill:#71ac8c; stroke:none;" d="M1122 680L1123 681L1122 680z"/>
<path style="fill:#e0fff5; stroke:none;" d="M1123 680L1123 682L1128 682C1126.21 680.578 1125.29 680.303 1123 680z"/>
<path style="fill:#8be2b5; stroke:none;" d="M970 681L971 682L970 681z"/>
<path style="fill:#5cd19a; stroke:none;" d="M1119 681L1120 682L1119 681z"/>
<path style="fill:#c1fddb; stroke:none;" d="M1120 681C1121.25 681.685 1121.55 681.749 1123 682C1121.75 681.315 1121.45 681.251 1120 681z"/>
<path style="fill:#76d3a7; stroke:none;" d="M700 682L701 683L700 682z"/>
<path style="fill:#e2fbe8; stroke:none;" d="M971 682C972.54 683.803 973.598 683.832 976 684C974.25 682.745 973.143 682.386 971 682z"/>
<path style="fill:#8ba894; stroke:none;" d="M974 682L975 683L974 682z"/>
<path style="fill:#6c9e83; stroke:none;" d="M1115 682L1116 683L1115 682z"/>
<path style="fill:#e6fff6; stroke:none;" d="M1106 685C1108.23 686.01 1110.6 686.405 1113 687C1115.13 684.971 1117.27 684.11 1120 683C1115.76 681.528 1110.37 684.151 1106 685z"/>
<path style="fill:#84cfa4; stroke:none;" d="M482.333 683.667C482.278 683.722 482.222 684.778 482.667 684.333C482.723 684.277 482.777 683.223 482.333 683.667z"/>
<path style="fill:#218958; stroke:none;" d="M700 683C700.003 687.137 699.346 691.418 703 694C702.239 690.309 701.632 686.39 700 683z"/>
<path style="fill:#61dca6; stroke:none;" d="M976.667 683.333C976.222 683.778 977.278 683.722 977.333 683.667C977.778 683.222 976.722 683.278 976.667 683.333z"/>
<path style="fill:#a8f2cd; stroke:none;" d="M1112.67 683.333C1112.22 683.778 1113.28 683.722 1113.33 683.667C1113.78 683.222 1112.72 683.278 1112.67 683.333z"/>
<path style="fill:#b7f8da; stroke:none;" d="M100 684L100 688C100.71 686.241 100.71 685.76 100 684z"/>
<path style="fill:#defff6; stroke:none;" d="M976 684L976 686L982 686C979.943 684.418 978.613 684.174 976 684z"/>
<path style="fill:#81b8a1; stroke:none;" d="M981 684L982 685L981 684z"/>
<path style="fill:#208857; stroke:none;" d="M1094 688L1107 685C1102.43 683.081 1096.47 683.281 1094 688z"/>
<path style="fill:#81c7a5; stroke:none;" d="M1107.67 684.333C1107.22 684.777 1108.28 684.723 1108.33 684.667C1108.78 684.222 1107.72 684.278 1107.67 684.333z"/>
<path style="fill:#b5fed3; stroke:none;" d="M482 685L482 688C482.696 686.446 482.696 686.554 482 685z"/>
<path style="fill:#b4ffe1; stroke:none;" d="M982.667 685.333C982.222 685.778 983.278 685.722 983.333 685.667C983.778 685.222 982.722 685.278 982.667 685.333z"/>
<path style="fill:#56c092; stroke:none;" d="M984.667 685.333C984.222 685.778 985.278 685.722 985.333 685.667C985.778 685.222 984.722 685.278 984.667 685.333z"/>
<path style="fill:#98ebc1; stroke:none;" d="M1104.67 685.333C1104.22 685.777 1105.28 685.722 1105.33 685.667C1105.78 685.222 1104.72 685.278 1104.67 685.333z"/>
<path style="fill:#78be9a; stroke:none;" d="M701.333 686.667C701.278 686.722 701.222 687.778 701.667 687.333C701.722 687.278 701.778 686.222 701.333 686.667z"/>
<path style="fill:#e7fff5; stroke:none;" d="M982 686L982 688L992 688C988.829 686.1 985.646 686.031 982 686z"/>
<path style="fill:#7faf95; stroke:none;" d="M989 686L990 687L989 686z"/>
<path style="fill:#3c7153; stroke:none;" d="M990.667 686.333C990.222 686.778 991.278 686.722 991.333 686.667C991.778 686.222 990.722 686.278 990.667 686.333z"/>
<path style="fill:#98cdb1; stroke:none;" d="M1099.67 686.333C1099.22 686.777 1100.28 686.722 1100.33 686.667C1100.78 686.222 1099.72 686.278 1099.67 686.333z"/>
<path style="fill:#e3fff5; stroke:none;" d="M1099 687C1101.3 687.916 1102.38 687.879 1104 686L1099 687z"/>
<path style="fill:#58d794; stroke:none;" d="M992.667 687.333C992.222 687.778 993.278 687.722 993.333 687.667C993.778 687.222 992.722 687.278 992.667 687.333z"/>
<path style="fill:#6dd8a2; stroke:none;" d="M1095 687L1096 688L1095 687z"/>
<path style="fill:#b0ffdb; stroke:none;" d="M1096 687C1097.25 687.685 1097.55 687.749 1099 688C1097.75 687.315 1097.45 687.251 1096 687z"/>
<path style="fill:#90e5bb; stroke:none;" d="M100 688L100 691C100.696 689.446 100.696 689.554 100 688z"/>
<path style="fill:#2eaa78; stroke:none;" d="M481 688L481 691C481.696 689.446 481.696 689.554 481 688z"/>
<path style="fill:#e4fff1; stroke:none;" d="M482 688L482 696C483.161 693.23 483.161 690.77 482 688z"/>
<path style="fill:#85c69e; stroke:none;" d="M656.333 688.667C656.278 688.722 656.222 689.778 656.667 689.333C656.722 689.278 656.778 688.222 656.333 688.667z"/>
<path style="fill:#2d925a; stroke:none;" d="M657 688L657 696C658.161 693.23 658.161 690.77 657 688z"/>
<path style="fill:#dffff3; stroke:none;" d="M702 688L703 694C704.039 691.471 704.093 689.824 702 688z"/>
<path style="fill:#86b39c; stroke:none;" d="M997 688C998.248 688.685 998.548 688.749 1000 689C998.752 688.315 998.452 688.251 997 688z"/>
<path style="fill:#1e8a58; stroke:none;" d="M1000 688C1002.59 689.378 1005.08 689.761 1008 690L1008 688L1000 688z"/>
<path style="fill:#1d9154; stroke:none;" d="M1081 690C1083.38 689.428 1085.56 689.109 1088 689C1085.27 687.86 1083 687.707 1081 690z"/>
<path style="fill:#8fa69e; stroke:none;" d="M1088.67 688.333C1088.22 688.778 1089.28 688.722 1089.33 688.667C1089.78 688.222 1088.72 688.278 1088.67 688.333z"/>
<path style="fill:#caffe6; stroke:none;" d="M998 689C999.506 689.683 1000.31 689.826 1002 690C1000.49 689.317 999.685 689.174 998 689z"/>
<path style="fill:#7ad7ab; stroke:none;" d="M1002.67 689.333C1002.22 689.778 1003.28 689.722 1003.33 689.667C1003.78 689.222 1002.72 689.278 1002.67 689.333z"/>
<path style="fill:#56cc8f; stroke:none;" d="M1082.67 689.333C1082.22 689.778 1083.28 689.722 1083.33 689.667C1083.78 689.222 1082.72 689.278 1082.67 689.333z"/>
<path style="fill:#91ffc6; stroke:none;" d="M1084.67 689.333C1084.22 689.778 1085.28 689.722 1085.33 689.667C1085.78 689.222 1084.72 689.278 1084.67 689.333z"/>
<path style="fill:#b1ffe3; stroke:none;" d="M1086.67 689.333C1086.22 689.778 1087.28 689.722 1087.33 689.667C1087.78 689.222 1086.72 689.278 1086.67 689.333z"/>
<path style="fill:#1a905c; stroke:none;" d="M480 690L480 698C481.161 695.23 481.161 692.77 480 690z"/>
<path style="fill:#cbfdd8; stroke:none;" d="M656 690L656 694C656.71 692.241 656.71 691.759 656 690z"/>
<path style="fill:#6bb38e; stroke:none;" d="M702.333 690.667C702.278 690.722 702.222 691.778 702.667 691.333C702.722 691.278 702.778 690.222 702.333 690.667z"/>
<path style="fill:#e1ffee; stroke:none;" d="M1000 690L1000 692L1016 692C1011.25 689.989 1005.14 690.014 1000 690z"/>
<path style="fill:#a8dbbc; stroke:none;" d="M1008 690C1009.25 690.685 1009.55 690.749 1011 691C1009.75 690.315 1009.45 690.251 1008 690z"/>
<path style="fill:#599271; stroke:none;" d="M1011.67 690.333C1011.22 690.778 1012.28 690.722 1012.33 690.667C1012.78 690.222 1011.72 690.278 1011.67 690.333z"/>
<path style="fill:#2f865b; stroke:none;" d="M1013 690C1016.35 691.417 1020.37 691.542 1024 692C1021.42 689.049 1016.71 690 1013 690z"/>
<path style="fill:#228d57; stroke:none;" d="M1056 690L1056 692L1072 691C1067.28 689.018 1061.09 690 1056 690z"/>
<path style="fill:#48745b; stroke:none;" d="M1072 690C1073.25 690.685 1073.55 690.749 1075 691C1073.75 690.315 1073.45 690.251 1072 690z"/>
<path style="fill:#8ab29a; stroke:none;" d="M1075.67 690.333C1075.22 690.777 1076.28 690.723 1076.33 690.667C1076.78 690.222 1075.72 690.278 1075.67 690.333z"/>
<path style="fill:#c1e2cd; stroke:none;" d="M1077 690C1078.25 690.685 1078.55 690.749 1080 691C1078.75 690.315 1078.45 690.251 1077 690z"/>
<path style="fill:#52ab7f; stroke:none;" d="M100.333 691.667C100.278 691.722 100.222 692.778 100.667 692.333C100.722 692.278 100.778 691.222 100.333 691.667z"/>
<path style="fill:#86e5bd; stroke:none;" d="M481 691L481 694C481.696 692.446 481.696 692.554 481 691z"/>
<path style="fill:#98e4bd; stroke:none;" d="M1016 691C1017.25 691.685 1017.55 691.749 1019 692C1017.75 691.315 1017.45 691.251 1016 691z"/>
<path style="fill:#56ab81; stroke:none;" d="M1019.67 691.333C1019.22 691.778 1020.28 691.722 1020.33 691.667C1020.78 691.222 1019.72 691.278 1019.67 691.333z"/>
<path style="fill:#65c391; stroke:none;" d="M1066 691C1067.25 691.685 1067.55 691.749 1069 692C1067.75 691.315 1067.45 691.251 1066 691z"/>
<path style="fill:#9efcca; stroke:none;" d="M1069 691C1070.25 691.685 1070.55 691.749 1072 692C1070.75 691.315 1070.45 691.251 1069 691z"/>
<path style="fill:#bee6ce; stroke:none;" d="M1024 692C1026.61 693.098 1029.16 692.993 1032 693C1029.39 691.902 1026.84 692.007 1024 692z"/>
<path style="fill:#75aa8e; stroke:none;" d="M1032 692C1038.2 694.603 1047.3 693 1054 693C1047.8 690.397 1038.7 692 1032 692z"/>
<path style="fill:#b4e0c9; stroke:none;" d="M1054 692C1055.77 692.779 1057.04 692.912 1059 693C1057.23 692.221 1055.96 692.088 1054 692z"/>
<path style="fill:#e6fff8; stroke:none;" d="M1059 692L1059 694L1072 694L1072 692L1059 692z"/>
<path style="fill:#2b8d5e; stroke:none;" d="M100 693L100 704C101.431 700.59 101.431 696.41 100 693z"/>
<path style="fill:#d8fff1; stroke:none;" d="M1028 693C1036.37 696.511 1049.92 694 1059 694C1050.63 690.489 1037.08 693 1028 693z"/>
<path style="fill:#c7ffee; stroke:none;" d="M481 694L481 698C481.71 696.241 481.71 695.759 481 694z"/>
<path style="fill:#70cda0; stroke:none;" d="M703.333 694.667C703.278 694.722 703.222 695.778 703.667 695.333C703.722 695.278 703.778 694.222 703.333 694.667z"/>
<path style="fill:#edffff; stroke:none;" d="M487 695C486.1 698.041 486.008 700.831 486 704L488 704C487.998 700.874 488.213 697.89 487 695z"/>
<path style="fill:#7fc39a; stroke:none;" d="M657.333 696.667C657.278 696.722 657.222 697.778 657.667 697.333C657.722 697.278 657.778 696.222 657.333 696.667z"/>
<path style="fill:#c4ebd6; stroke:none;" d="M99 697L99 700C99.6961 698.446 99.6961 698.554 99 697z"/>
<path style="fill:#76c8a0; stroke:none;" d="M480.333 698.667C480.278 698.722 480.222 699.778 480.667 699.333C480.722 699.278 480.778 698.222 480.333 698.667z"/>
<path style="fill:#b4eac8; stroke:none;" d="M657.333 698.667C657.278 698.722 657.222 699.778 657.667 699.333C657.722 699.278 657.778 698.222 657.333 698.667z"/>
<path style="fill:#1b9564; stroke:none;" d="M658 698C658.058 700.784 658.352 703.291 659 706C660.038 703.102 659.433 700.696 658 698z"/>
<path style="fill:#94d5bf; stroke:none;" d="M704 698L705 699L704 698z"/>
<path style="fill:#e4fbf3; stroke:none;" d="M705 698C705.375 702.589 705.851 707.464 710 710C708.467 706 708.563 700.518 705 698z"/>
<path style="fill:#2c8768; stroke:none;" d="M704 699L704 704C704.83 701.97 704.83 701.03 704 699z"/>
<path style="fill:#7da992; stroke:none;" d="M99 700L99 704C99.7102 702.24 99.7102 701.76 99 700z"/>
<path style="fill:#a8f0cb; stroke:none;" d="M480.333 700.667C480.278 700.722 480.222 701.778 480.667 701.333C480.722 701.278 480.778 700.222 480.333 700.667z"/>
<path style="fill:#c4ffe4; stroke:none;" d="M480.333 702.667C480.278 702.722 480.222 703.778 480.667 703.333C480.722 703.278 480.778 702.222 480.333 702.667z"/>
<path style="fill:#51957e; stroke:none;" d="M705.333 702.667C705.278 702.722 705.222 703.778 705.667 703.333C705.722 703.278 705.778 702.222 705.333 702.667z"/>
<path style="fill:#cfffee; stroke:none;" d="M98 704L98 712C99.1614 709.23 99.1614 706.77 98 704z"/>
<path style="fill:#1c8856; stroke:none;" d="M99 704L99 720C100.812 715.681 100.812 708.319 99 704z"/>
<path style="fill:#168b56; stroke:none;" d="M477 704C474.401 723.297 476 743.357 476 763C476 768.379 476.629 773.65 476.776 778.985C476.855 781.856 475.335 784.277 475.219 787.089C475.037 791.483 477.417 795.112 479 799C480.877 794.192 478.887 787.095 478.285 782C477.467 775.062 477.834 767.975 477.17 761C475.41 742.508 477.681 722.347 480 704L477 704z"/>
<path style="fill:#8cd2b6; stroke:none;" d="M658.333 704.667C658.278 704.722 658.222 705.778 658.667 705.333C658.722 705.278 658.778 704.222 658.333 704.667z"/>
<path style="fill:#d1fff3; stroke:none;" d="M658 706L658 710C658.71 708.241 658.71 707.759 658 706z"/>
<path style="fill:#268754; stroke:none;" d="M706 706C706.058 708.784 706.352 711.291 707 714C708.038 711.102 707.433 708.696 706 706z"/>
<path style="fill:#5cc992; stroke:none;" d="M479.333 707.667C479.278 707.722 479.222 708.778 479.667 708.333C479.722 708.278 479.778 707.222 479.333 707.667z"/>
<path style="fill:#86eab8; stroke:none;" d="M479 709L479 712C479.696 710.446 479.696 710.554 479 709z"/>
<path style="fill:#84bd9c; stroke:none;" d="M707 709L708 710L707 709z"/>
<path style="fill:#8bd6b5; stroke:none;" d="M659.333 710.667C659.278 710.722 659.222 711.778 659.667 711.333C659.722 711.278 659.778 710.222 659.333 710.667z"/>
<path style="fill:#218350; stroke:none;" d="M660 710L661 720C662.459 716.516 662.123 713.108 660 710z"/>
<path style="fill:#b8edcf; stroke:none;" d="M708.333 710.667C708.278 710.722 708.222 711.778 708.667 711.333C708.722 711.278 708.778 710.222 708.333 710.667z"/>
<path style="fill:#9adcc0; stroke:none;" d="M98 712L98 717C98.8299 714.97 98.8299 714.03 98 712z"/>
<path style="fill:#c1ffe8; stroke:none;" d="M479 712L479 720C480.161 717.23 480.161 714.77 479 712z"/>
<path style="fill:#ddfeeb; stroke:none;" d="M659 712L659 716C659.71 714.24 659.71 713.759 659 712z"/>
<path style="fill:#5aa880; stroke:none;" d="M708.333 712.667C708.278 712.722 708.222 713.778 708.667 713.333C708.722 713.278 708.778 712.222 708.333 712.667z"/>
<path style="fill:#95c6a8; stroke:none;" d="M660.333 715.667C660.278 715.722 660.222 716.778 660.667 716.333C660.722 716.278 660.778 715.222 660.333 715.667z"/>
<path style="fill:#83cfa9; stroke:none;" d="M709 715L710 716L709 715z"/>
<path style="fill:#73ba9c; stroke:none;" d="M98 717L98 720C98.6961 718.446 98.6961 718.554 98 717z"/>
<path style="fill:#83caaa; stroke:none;" d="M710 718L711 719L710 718z"/>
<path style="fill:#79b899; stroke:none;" d="M478 719L478 722C478.696 720.446 478.696 720.554 478 719z"/>
<path style="fill:#e0fbea; stroke:none;" d="M97 720L97 736C98.8123 731.681 98.8123 724.319 97 720z"/>
<path style="fill:#51b785; stroke:none;" d="M98 720L98 723C98.6961 721.446 98.6961 721.554 98 720z"/>
<path style="fill:#7ac29c; stroke:none;" d="M661.333 720.667C661.278 720.722 661.222 721.778 661.667 721.333C661.722 721.278 661.778 720.222 661.333 720.667z"/>
<path style="fill:#89c9a7; stroke:none;" d="M711 721L712 722L711 721z"/>
<path style="fill:#b7ecd0; stroke:none;" d="M478 722L478 726C478.71 724.241 478.71 723.76 478 722z"/>
<path style="fill:#d5fff5; stroke:none;" d="M661 722L662 736L664 736C663.632 731.349 663.205 726.146 661 722z"/>
<path style="fill:#169158; stroke:none;" d="M98 723L98 784C99.9806 779.28 99 773.08 99 768L99 738C99 733.191 99.8752 727.469 98 723z"/>
<path style="fill:#64b78d; stroke:none;" d="M712 724L713 725L712 724z"/>
<path style="fill:#60b989; stroke:none;" d="M662 725L663 726L662 725z"/>
<path style="fill:#edfff1; stroke:none;" d="M96 726L96 770C97.552 766.301 97 761.983 97 758L97 738C97 734.017 97.552 729.699 96 726z"/>
<path style="fill:#edfbec; stroke:none;" d="M478 726L478 738C479.512 734.396 479.512 729.604 478 726z"/>
<path style="fill:#8cd8b1; stroke:none;" d="M713 726L714 727L713 726z"/>
<path style="fill:#eaffff; stroke:none;" d="M656 728L656 736L658 736L658 728L656 728z"/>
<path style="fill:#a0dcba; stroke:none;" d="M714 729L715 730L714 729z"/>
<path style="fill:#a8f4ce; stroke:none;" d="M663.333 730.667C663.278 730.722 663.222 731.778 663.667 731.333C663.722 731.278 663.778 730.222 663.333 730.667z"/>
<path style="fill:#79d0a6; stroke:none;" d="M715 732L716 733L715 732z"/>
<path style="fill:#8af1c4; stroke:none;" d="M664.333 734.667C664.278 734.722 664.222 735.778 664.667 735.333C664.722 735.278 664.778 734.222 664.333 734.667z"/>
<path style="fill:#92dfbd; stroke:none;" d="M716 734L717 735L716 734z"/>
<path style="fill:#66b391; stroke:none;" d="M716 735L717 736L716 735z"/>
<path style="fill:#a3dfbd; stroke:none;" d="M97 736L97 768C98.3283 764.834 97.9998 761.419 98 758C98.0004 751.314 99.599 742.194 97 736z"/>
<path style="fill:#5bbc93; stroke:none;" d="M477 736L477 764C479.604 757.793 479.604 742.207 477 736z"/>
<path style="fill:#eafff4; stroke:none;" d="M664 736C664.001 740.763 664.404 745.284 665 750L668 750C668.038 754.549 667.971 759.24 672 762C670.309 753.828 668.304 743.137 664 736z"/>
<path style="fill:#377d59; stroke:none;" d="M716 736L717 740C717.393 738.076 717.146 737.64 716 736z"/>
<path style="fill:#92c9ac; stroke:none;" d="M717 737L718 738L717 737z"/>
<path style="fill:#81c6a9; stroke:none;" d="M665 738L666 739L665 738z"/>
<path style="fill:#5fba8e; stroke:none;" d="M718 740L719 741L718 740z"/>
<path style="fill:#4aa97d; stroke:none;" d="M666 741L667 742L666 741z"/>
<path style="fill:#b1fdd9; stroke:none;" d="M666.333 742.667C666.278 742.722 666.222 743.778 666.667 743.333C666.722 743.278 666.778 742.222 666.333 742.667z"/>
<path style="fill:#228856; stroke:none;" d="M667 742C667.3 745.44 668.232 748.643 669 752C670.509 748.291 669.428 745.053 667 742z"/>
<path style="fill:#75dbab; stroke:none;" d="M719 742L720 743L719 742z"/>
<path style="fill:#effffa; stroke:none;" d="M656 744L656 752L658 752L658 744L656 744z"/>
<path style="fill:#78a68c; stroke:none;" d="M720 745L721 746L720 745z"/>
<path style="fill:#d9ffee; stroke:none;" d="M721 746L723 752C723.976 749.204 723.4 747.69 721 746z"/>
<path style="fill:#90cdac; stroke:none;" d="M721 747L722 748L721 747z"/>
<path style="fill:#96e7bc; stroke:none;" d="M722 749L723 750L722 749z"/>
<path style="fill:#51b382; stroke:none;" d="M722 750L723 751L722 750z"/>
<path style="fill:#79c09e; stroke:none;" d="M669 752L670 753L669 752z"/>
<path style="fill:#1f8c55; stroke:none;" d="M670 752C670.83 761.51 675.685 771.082 678.947 780C680.448 784.104 681.215 788.499 685 791C681.555 781.758 678.308 772.501 675.596 763.089C674.431 759.043 672.238 756.391 672 752L670 752z"/>
<path style="fill:#b7f7d5; stroke:none;" d="M724.333 752.667C724.278 752.722 724.222 753.778 724.667 753.333C724.722 753.278 724.778 752.222 724.333 752.667z"/>
<path style="fill:#72c79d; stroke:none;" d="M724 754L725 755L724 754z"/>
<path style="fill:#78cda4; stroke:none;" d="M725 756L726 757L725 756z"/>
<path style="fill:#84dfb6; stroke:none;" d="M726 758L727 759L726 758z"/>
<path style="fill:#96cab3; stroke:none;" d="M671 759L672 760L671 759z"/>
<path style="fill:#92eec5; stroke:none;" d="M727 760L728 761L727 760z"/>
<path style="fill:#d4fff0; stroke:none;" d="M728 760C728.397 762.301 728.957 763.904 730 766C730.444 763.417 729.631 762.043 728 760z"/>
<path style="fill:#5ab68d; stroke:none;" d="M727 761L728 762L727 761z"/>
<path style="fill:#8cd9af; stroke:none;" d="M672 762L673 763L672 762z"/>
<path style="fill:#dffff1; stroke:none;" d="M672 763C672.168 765.402 672.197 766.46 674 768C673.52 766.04 673.062 764.693 672 763z"/>
<path style="fill:#66c198; stroke:none;" d="M728 763L729 764L728 763z"/>
<path style="fill:#76cc9f; stroke:none;" d="M673 765L674 766L673 765z"/>
<path style="fill:#74c6a2; stroke:none;" d="M729 765L730 766L729 765z"/>
<path style="fill:#449c6c; stroke:none;" d="M674.333 766.667C674.278 766.722 674.222 767.778 674.667 767.333C674.722 767.278 674.778 766.222 674.333 766.667z"/>
<path style="fill:#97d1b8; stroke:none;" d="M730.333 766.667C730.278 766.722 730.222 767.777 730.667 767.333C730.722 767.278 730.778 766.222 730.333 766.667z"/>
<path style="fill:#d1f4e0; stroke:none;" d="M97 768L97 776C98.1614 773.23 98.1614 770.77 97 768z"/>
<path style="fill:#dafce3; stroke:none;" d="M478 768L478 776C479.161 773.23 479.161 770.77 478 768z"/>
<path style="fill:#effff3; stroke:none;" d="M479 768L479 784C480.812 779.681 480.812 772.319 479 768z"/>
<path style="fill:#dffff5; stroke:none;" d="M674 768L675 778L677 778C676.681 774.35 675.958 771.116 674 768z"/>
<path style="fill:#deffee; stroke:none;" d="M731 768L734 774C734.596 771.003 733.544 769.55 731 768z"/>
<path style="fill:#6dc89c; stroke:none;" d="M731 769L732 770L731 769z"/>
<path style="fill:#63b38e; stroke:none;" d="M675 770L676 771L675 770z"/>
<path style="fill:#1e8a56; stroke:none;" d="M731 770C731.18 773.562 731.131 777.175 735 778C734.113 774.929 733.036 772.466 731 770z"/>
<path style="fill:#81c4a3; stroke:none;" d="M732 771L733 772L732 771z"/>
<path style="fill:#5aba8c; stroke:none;" d="M676 773L677 774L676 773z"/>
<path style="fill:#90c8ad; stroke:none;" d="M733 773L734 774L733 773z"/>
<path style="fill:#87d9b1; stroke:none;" d="M734.333 774.667C734.278 774.722 734.222 775.778 734.667 775.333C734.722 775.278 734.778 774.222 734.333 774.667z"/>
<path style="fill:#edffff; stroke:none;" d="M735 774C735.712 777.949 737.641 781.259 739 785C740.949 780.3 738.724 776.951 735 774z"/>
<path style="fill:#e7feee; stroke:none;" d="M97 776L97 784C98.1614 781.23 98.1614 778.77 97 776z"/>
<path style="fill:#b2dfc2; stroke:none;" d="M478 776L478 779C478.696 777.446 478.696 777.554 478 776z"/>
<path style="fill:#a8ebca; stroke:none;" d="M677.333 776.667C677.278 776.722 677.222 777.778 677.667 777.333C677.722 777.278 677.778 776.222 677.333 776.667z"/>
<path style="fill:#82dbaf; stroke:none;" d="M735 777L736 778L735 777z"/>
<path style="fill:#67a888; stroke:none;" d="M678 778L679 779L678 778z"/>
<path style="fill:#87c3a1; stroke:none;" d="M478.333 779.667C478.278 779.722 478.222 780.778 478.667 780.333C478.722 780.278 478.778 779.222 478.333 779.667z"/>
<path style="fill:#95d6b6; stroke:none;" d="M678 779L679 780L678 779z"/>
<path style="fill:#84c3ae; stroke:none;" d="M736 779L737 780L736 779z"/>
<path style="fill:#1d7e5d; stroke:none;" d="M736 780C736.307 782.123 736.382 782.686 738 784C737.436 782.248 737.081 781.458 736 780z"/>
<path style="fill:#6bab88; stroke:none;" d="M478 781L478 784C478.696 782.446 478.696 782.554 478 781z"/>
<path style="fill:#72b594; stroke:none;" d="M679 781L680 782L679 781z"/>
<path style="fill:#84c8b1; stroke:none;" d="M737 781L738 782L737 781z"/>
<path style="fill:#97dbc4; stroke:none;" d="M738.333 782.667C738.278 782.722 738.222 783.778 738.667 783.333C738.722 783.278 738.778 782.222 738.333 782.667z"/>
<path style="fill:#fbfffe; stroke:none;" d="M1028 784C1028 793.003 1029.35 802.044 1028.96 811C1027.95 834.164 1026 856.788 1026 880L1028 880L1028 864L1031 864L1030 880C1033.4 872.929 1031.44 864.571 1032.17 857C1034.52 832.729 1034 808.438 1034 784C1043 790.402 1048.11 804.934 1059 808C1058.23 806.686 1058.31 806.768 1057 806C1062.6 796.905 1067.97 810.685 1069 816L1072 816C1071.99 811.731 1072.14 802.88 1067.57 800.603C1064.48 799.062 1059.38 800 1056 800C1055.64 794.343 1055.83 789.633 1056 784L1052 784C1050.97 788.589 1049.61 792.31 1045 794C1044.47 789.624 1046.18 787.745 1048 784C1045.6 784.168 1044.54 784.197 1043 786C1037.55 782.129 1034.12 783.222 1028 784z"/>
<path style="fill:#52a181; stroke:none;" d="M98.3333 784.667C98.2778 784.722 98.2222 785.778 98.6667 785.333C98.7223 785.278 98.7778 784.222 98.3333 784.667z"/>
<path style="fill:#1d8c59; stroke:none;" d="M99 784L99 800C100.812 795.681 100.812 788.319 99 784z"/>
<path style="fill:#c4ffe6; stroke:none;" d="M479 784L479 790C479.951 787.715 479.951 786.285 479 784z"/>
<path style="fill:#c2e9d6; stroke:none;" d="M680.333 784.667C680.278 784.722 680.222 785.778 680.667 785.333C680.722 785.278 680.778 784.222 680.333 784.667z"/>
<path style="fill:#2e8f5c; stroke:none;" d="M738 784L739 788C739.393 786.076 739.146 785.641 738 784z"/>
<path style="fill:#d0ffeb; stroke:none;" d="M1034 784L1034 796C1035.51 792.396 1035.51 787.604 1034 784z"/>
<path style="fill:#1c8f56; stroke:none;" d="M1035 784L1035 797C1035.98 794.669 1035.98 792.543 1036 790C1037.86 791.048 1038.94 791.513 1041 792C1039.4 788.88 1037.54 786.419 1035 784z"/>
<path style="fill:#8bbc9e; stroke:none;" d="M739 785L740 786L739 785z"/>
<path style="fill:#76c1a0; stroke:none;" d="M1036 785L1037 786L1036 785z"/>
<path style="fill:#6ab999; stroke:none;" d="M98 786L98 790C98.7102 788.24 98.7102 787.76 98 786z"/>
<path style="fill:#70cfa5; stroke:none;" d="M1037 786L1038 787L1037 786z"/>
<path style="fill:#a0e3c2; stroke:none;" d="M681 787L682 788L681 787z"/>
<path style="fill:#85b59d; stroke:none;" d="M740 787L741 788L740 787z"/>
<path style="fill:#a4d6b9; stroke:none;" d="M1038 787L1039 788L1038 787z"/>
<path style="fill:#9de5bd; stroke:none;" d="M1039 788L1040 789L1039 788z"/>
<path style="fill:#80d2aa; stroke:none;" d="M682 789L683 790L682 789z"/>
<path style="fill:#73be9d; stroke:none;" d="M741 789L742 790L741 789z"/>
<path style="fill:#97e0c2; stroke:none;" d="M98 790L98 794C98.7102 792.241 98.7102 791.759 98 790z"/>
<path style="fill:#8eebbc; stroke:none;" d="M479.333 790.667C479.278 790.722 479.222 791.778 479.667 791.333C479.722 791.278 479.778 790.222 479.333 790.667z"/>
<path style="fill:#84cba9; stroke:none;" d="M742.333 790.667C742.278 790.722 742.222 791.778 742.667 791.333C742.722 791.278 742.778 790.222 742.333 790.667z"/>
<path style="fill:#54b687; stroke:none;" d="M1040 790L1041 791L1040 790z"/>
<path style="fill:#76c29e; stroke:none;" d="M1041.33 790.667C1041.28 790.723 1041.22 791.777 1041.67 791.333C1041.72 791.278 1041.78 790.222 1041.33 790.667z"/>
<path style="fill:#62b78d; stroke:none;" d="M683 791L684 792L683 791z"/>
<path style="fill:#5cc491; stroke:none;" d="M479.333 792.667C479.278 792.722 479.222 793.778 479.667 793.333C479.722 793.277 479.778 792.222 479.333 792.667z"/>
<path style="fill:#0f8a53; stroke:none;" d="M742 792C742.334 796.515 745.12 799.654 746 804C747.728 799.516 744.932 795.378 742 792z"/>
<path style="fill:#aae2c5; stroke:none;" d="M743 792L744 793L743 792z"/>
<path style="fill:#64bd8f; stroke:none;" d="M1042 792L1043 793L1042 792z"/>
<path style="fill:#4ea77b; stroke:none;" d="M684 793L685 794L684 793z"/>
<path style="fill:#72aa8d; stroke:none;" d="M743 793L744 794L743 793z"/>
<path style="fill:#0b8d4f; stroke:none;" d="M1041 793C1042.09 796.281 1043.56 795.772 1043 798L1048 800C1046.22 796.605 1044.47 794.653 1041 793z"/>
<path style="fill:#8dc8a8; stroke:none;" d="M1043 793L1044 794L1043 793z"/>
<path style="fill:#c7ffec; stroke:none;" d="M98 794L98 800C98.9511 797.715 98.9511 796.285 98 794z"/>
<path style="fill:#9ad2b5; stroke:none;" d="M684 794L685 795L684 794z"/>
<path style="fill:#9ee2bd; stroke:none;" d="M744 794L745 795L744 794z"/>
<path style="fill:#5ea27d; stroke:none;" d="M744 795L745 796L744 795z"/>
<path style="fill:#83c4a4; stroke:none;" d="M685 796L686 797L685 796z"/>
<path style="fill:#86ddb3; stroke:none;" d="M745 796L746 797L745 796z"/>
<path style="fill:#dcfbeb; stroke:none;" d="M746 796L747 800C747.553 797.992 747.303 797.668 746 796z"/>
<path style="fill:#e3fff4; stroke:none;" d="M1034 796L1034 816C1036.09 811.027 1036.09 800.973 1034 796z"/>
<path style="fill:#68b48e; stroke:none;" d="M1035 797L1035 824C1037.54 817.945 1037.54 803.055 1035 797z"/>
<path style="fill:#5bb68d; stroke:none;" d="M686 798L687 799L686 798z"/>
<path style="fill:#8ad4b1; stroke:none;" d="M746 798L747 799L746 798z"/>
<path style="fill:#69b996; stroke:none;" d="M1047 798L1048 799L1047 798z"/>
<path style="fill:#91ecc3; stroke:none;" d="M686 799L687 800L686 799z"/>
<path style="fill:#9cccb6; stroke:none;" d="M1048 799L1049 800L1048 799z"/>
<path style="fill:#dffff1; stroke:none;" d="M98 800C98 805.339 97.4789 810.854 99 816C100.861 811.554 99.854 804.377 98 800z"/>
<path style="fill:#4e8369; stroke:none;" d="M99.3333 800.667C99.2778 800.722 99.2222 801.778 99.6667 801.333C99.7222 801.278 99.7778 800.222 99.3333 800.667z"/>
<path style="fill:#10945a; stroke:none;" d="M100 800L101 824C102.993 819.252 102.045 813.145 103 808C99.4145 808 101.927 802.79 102 800L100 800z"/>
<path style="fill:#8cd8b2; stroke:none;" d="M480 800L480 803C480.696 801.446 480.696 801.554 480 800z"/>
<path style="fill:#6d8b81; stroke:none;" d="M687 800L688 801L687 800z"/>
<path style="fill:#66c293; stroke:none;" d="M747 800L748 801L747 800z"/>
<path style="fill:#78e4aa; stroke:none;" d="M1049 800L1050 801L1049 800z"/>
<path style="fill:#a8c6bc; stroke:none;" d="M687 801L688 802L687 801z"/>
<path style="fill:#28905b; stroke:none;" d="M747 801L748 808C752.527 807.01 749.301 803.159 747 801z"/>
<path style="fill:#90e6b7; stroke:none;" d="M1050 801L1051 802L1050 801z"/>
<path style="fill:#6d9f86; stroke:none;" d="M99 802L99 805C99.6961 803.446 99.6961 803.554 99 802z"/>
<path style="fill:#299559; stroke:none;" d="M690 802C689.826 807.807 692.82 812.756 695 818C696.6 813.375 692.749 805.836 690 802z"/>
<path style="fill:#5fa984; stroke:none;" d="M748 802L749 803L748 802z"/>
<path style="fill:#248854; stroke:none;" d="M1050 802L1050 806L1054 807C1052.9 804.643 1052.04 803.624 1050 802z"/>
<path style="fill:#0e955b; stroke:none;" d="M480 803L480 816L482 816C481.671 811.826 481.612 806.834 480 803z"/>
<path style="fill:#61dc9b; stroke:none;" d="M688 803L689 804L688 803z"/>
<path style="fill:#c2ffe5; stroke:none;" d="M481 804L481 808C481.71 806.24 481.71 805.759 481 804z"/>
<path style="fill:#98c8b0; stroke:none;" d="M99 805L99 808C99.6961 806.446 99.6961 806.554 99 805z"/>
<path style="fill:#63c18d; stroke:none;" d="M689 805L690 806L689 805z"/>
<path style="fill:#a0dfc2; stroke:none;" d="M750 805L751 806L750 805z"/>
<path style="fill:#4b9d6f; stroke:none;" d="M690.333 806.667C690.278 806.722 690.222 807.778 690.667 807.333C690.722 807.278 690.778 806.222 690.333 806.667z"/>
<path style="fill:#92deba; stroke:none;" d="M751.333 806.667C751.278 806.722 751.222 807.778 751.667 807.333C751.722 807.278 751.778 806.222 751.333 806.667z"/>
<path style="fill:#119450; stroke:none;" d="M1051 806C1051.97 808.492 1052.47 809.146 1055 810L1054 816C1055.31 815.232 1055.23 815.314 1056 814C1059.49 816.115 1061.49 818.192 1063 822L1067 822C1063.05 816.036 1057.95 808.413 1051 806z"/>
<path style="fill:#64c59a; stroke:none;" d="M1054 806L1055 807L1054 806z"/>
<path style="fill:#90d7b9; stroke:none;" d="M1055 807L1056 808L1055 807z"/>
<path style="fill:#8adeba; stroke:none;" d="M481.333 808.667C481.278 808.722 481.222 809.778 481.667 809.333C481.722 809.278 481.778 808.222 481.333 808.667z"/>
<path style="fill:#dffff6; stroke:none;" d="M752 808L756 816C757.313 812.163 755.375 809.771 752 808z"/>
<path style="fill:#ace5c4; stroke:none;" d="M1056 808L1057 809L1056 808z"/>
<path style="fill:#e2fffb; stroke:none;" d="M1057 808C1058.65 811.502 1060.5 813.351 1064 815C1062.38 811.421 1060.87 809.084 1057 808z"/>
<path style="fill:#8bb09e; stroke:none;" d="M752 809L753 810L752 809z"/>
<path style="fill:#39a277; stroke:none;" d="M481.333 810.667C481.278 810.722 481.222 811.778 481.667 811.333C481.722 811.278 481.778 810.222 481.333 810.667z"/>
<path style="fill:#92cfae; stroke:none;" d="M691 810L692 811L691 810z"/>
<path style="fill:#52a87b; stroke:none;" d="M1057 810L1058 811L1057 810z"/>
<path style="fill:#70ab8d; stroke:none;" d="M753 811L754 812L753 811z"/>
<path style="fill:#62ad86; stroke:none;" d="M1058 811L1059 812L1058 811z"/>
<path style="fill:#89c5a1; stroke:none;" d="M692 812L693 813L692 812z"/>
<path style="fill:#5aba8c; stroke:none;" d="M1059 812L1060 813L1059 812z"/>
<path style="fill:#c1eed7; stroke:none;" d="M482 813L482 816C482.696 814.446 482.696 814.554 482 813z"/>
<path style="fill:#84cbab; stroke:none;" d="M1060 813L1061 814L1060 813z"/>
<path style="fill:#35ac76; stroke:none;" d="M100.333 814.667C100.278 814.722 100.222 815.778 100.667 815.333C100.722 815.278 100.778 814.222 100.333 814.667z"/>
<path style="fill:#90caa4; stroke:none;" d="M693 814L694 815L693 814z"/>
<path style="fill:#90ddb3; stroke:none;" d="M755 814L756 815L755 814z"/>
<path style="fill:#8eccb3; stroke:none;" d="M1061 814L1062 815L1061 814z"/>
<path style="fill:#aad4c6; stroke:none;" d="M1062 815L1063 816L1062 815z"/>
<path style="fill:#7ac19f; stroke:none;" d="M100 816L100 819C100.696 817.446 100.696 817.554 100 816z"/>
<path style="fill:#76c49c; stroke:none;" d="M482.333 816.667C482.278 816.722 482.222 817.778 482.667 817.333C482.722 817.278 482.778 816.222 482.333 816.667z"/>
<path style="fill:#dafde5; stroke:none;" d="M483 816L483 822C483.951 819.715 483.951 818.285 483 816z"/>
<path style="fill:#9de7c2; stroke:none;" d="M694.333 816.667C694.278 816.722 694.222 817.778 694.667 817.333C694.722 817.278 694.778 816.222 694.333 816.667z"/>
<path style="fill:#45986c; stroke:none;" d="M756.333 816.667C756.278 816.722 756.222 817.778 756.667 817.333C756.722 817.278 756.778 816.222 756.333 816.667z"/>
<path style="fill:#d3ffee; stroke:none;" d="M1034 816L1034 836C1036.09 831.027 1036.09 820.973 1034 816z"/>
<path style="fill:#d8fff1; stroke:none;" d="M1063 816L1064 818C1065.63 817.455 1065.45 817.635 1066 816L1063 816z"/>
<path style="fill:#ade2c4; stroke:none;" d="M757 817L758 818L757 817z"/>
<path style="fill:#46ad76; stroke:none;" d="M1063 817L1064 818L1063 817z"/>
<path style="fill:#2c8859; stroke:none;" d="M482 818L482 824C482.951 821.715 482.951 820.285 482 818z"/>
<path style="fill:#6fc49a; stroke:none;" d="M695 818L696 819L695 818z"/>
<path style="fill:#61b486; stroke:none;" d="M1064 818L1065 819L1064 818z"/>
<path style="fill:#c8f8de; stroke:none;" d="M100 819L100 824C100.83 821.97 100.83 821.031 100 819z"/>
<path style="fill:#a4dcc1; stroke:none;" d="M758 819L759 820L758 819z"/>
<path style="fill:#6da383; stroke:none;" d="M1065 819L1066 820L1065 819z"/>
<path style="fill:#72b691; stroke:none;" d="M696 820L697 821L696 820z"/>
<path style="fill:#7acba2; stroke:none;" d="M1066 820L1067 821L1066 820z"/>
<path style="fill:#a6eac5; stroke:none;" d="M696 821L697 822L696 821z"/>
<path style="fill:#87ccad; stroke:none;" d="M759 821L760 822L759 821z"/>
<path style="fill:#9ed9bb; stroke:none;" d="M1067 821L1068 822L1067 821z"/>
<path style="fill:#78c09a; stroke:none;" d="M483.333 822.667C483.278 822.722 483.222 823.778 483.667 823.333C483.722 823.278 483.778 822.222 483.333 822.667z"/>
<path style="fill:#d9fff4; stroke:none;" d="M484 822L486 838C487.789 833.614 486.648 825.876 484 822z"/>
<path style="fill:#77ae8f; stroke:none;" d="M697 822L698 823L697 822z"/>
<path style="fill:#158756; stroke:none;" d="M1067 822C1068.56 827.2 1072.1 831.582 1077 834C1074.82 829.258 1071.12 825.191 1067 822z"/>
<path style="fill:#c7f9e0; stroke:none;" d="M1068 822C1068.55 823.635 1068.36 823.455 1070 824L1070 822L1068 822z"/>
<path style="fill:#e4fff3; stroke:none;" d="M1070 822C1071.67 826.948 1074.88 830.845 1079 834L1080 833C1077.3 828.708 1074.04 825.068 1070 822z"/>
<path style="fill:#a4dbbc; stroke:none;" d="M697 823L698 824L697 823z"/>
<path style="fill:#80caa7; stroke:none;" d="M760 823L761 824L760 823z"/>
<path style="fill:#b7ffde; stroke:none;" d="M761.333 823.667C761.278 823.722 761.222 824.778 761.667 824.333C761.722 824.278 761.778 823.222 761.333 823.667z"/>
<path style="fill:#e7fff4; stroke:none;" d="M100 824C100.027 833.701 100.75 844.554 103 854C104.24 850.96 103.539 848.216 103.155 845C102.355 838.298 102.944 830.127 100 824z"/>
<path style="fill:#49976f; stroke:none;" d="M101 824L101 827C101.696 825.446 101.696 825.554 101 824z"/>
<path style="fill:#369366; stroke:none;" d="M483.333 824.667C483.278 824.722 483.222 825.778 483.667 825.333C483.722 825.278 483.778 824.222 483.333 824.667z"/>
<path style="fill:#dffdf1; stroke:none;" d="M762 824C762.739 826.567 763.682 828.673 765 831C765.85 827.78 764.401 826.115 762 824z"/>
<path style="fill:#1e8d5a; stroke:none;" d="M1035 824L1034 864C1035.58 860.243 1034.86 856.029 1035.04 852C1035.41 843.496 1038.39 832.076 1035 824z"/>
<path style="fill:#a4e4bf; stroke:none;" d="M1070 824L1071 825L1070 824z"/>
<path style="fill:#96e0bd; stroke:none;" d="M698 825L699 826L698 825z"/>
<path style="fill:#5aa481; stroke:none;" d="M761 825L762 826L761 825z"/>
<path style="fill:#79b994; stroke:none;" d="M1070 825L1071 826L1070 825z"/>
<path style="fill:#a3f8d1; stroke:none;" d="M484 826L484 829C484.696 827.446 484.696 827.554 484 826z"/>
<path style="fill:#5ea583; stroke:none;" d="M699 826L700 827L699 826z"/>
<path style="fill:#89e0b6; stroke:none;" d="M762 826L763 827L762 826z"/>
<path style="fill:#98d5b3; stroke:none;" d="M101.333 827.667C101.278 827.722 101.222 828.778 101.667 828.333C101.722 828.278 101.778 827.222 101.333 827.667z"/>
<path style="fill:#9ce3c1; stroke:none;" d="M699 827L700 828L699 827z"/>
<path style="fill:#8fccad; stroke:none;" d="M1072 827L1073 828L1072 827z"/>
<path style="fill:#159155; stroke:none;" d="M102 828C102.056 834.728 103.472 841.311 104 848C106.043 842.992 104.526 832.751 102 828z"/>
<path style="fill:#6aab89; stroke:none;" d="M700 828L701 829L700 828z"/>
<path style="fill:#55b68b; stroke:none;" d="M763 828L764 829L763 828z"/>
<path style="fill:#86e2b9; stroke:none;" d="M1073 828L1074 829L1073 828z"/>
<path style="fill:#9de4c2; stroke:none;" d="M1074 829L1075 830L1074 829z"/>
<path style="fill:#62b088; stroke:none;" d="M701 830L702 831L701 830z"/>
<path style="fill:#5eb290; stroke:none;" d="M764 830L765 831L764 830z"/>
<path style="fill:#159057; stroke:none;" d="M763 831L762 836C768.243 837.681 769.548 845.15 775 848C772.221 842.054 768.433 836.589 765 831L763 831z"/>
<path style="fill:#a5d1c0; stroke:none;" d="M765 831L766 832L765 831z"/>
<path style="fill:#719b8d; stroke:none;" d="M702 832L703 833L702 832z"/>
<path style="fill:#457867; stroke:none;" d="M703 832L703 835C703.696 833.446 703.696 833.554 703 832z"/>
<path style="fill:#bafff1; stroke:none;" d="M766 832L767 835C767.685 833.751 767.749 833.452 768 832L766 832z"/>
<path style="fill:#ecfff1; stroke:none;" d="M768 832C768.919 840.92 775.889 848.278 780 856C781.071 852.908 779.314 850.826 777.931 848C775.293 842.609 771.72 838.122 771 832L768 832z"/>
<path style="fill:#a8d2c4; stroke:none;" d="M702 833L703 834L702 833z"/>
<path style="fill:#67d1a3; stroke:none;" d="M766 833L767 834L766 833z"/>
<path style="fill:#7ab796; stroke:none;" d="M1077 833L1078 834L1077 833z"/>
<path style="fill:#4da076; stroke:none;" d="M485.333 834.667C485.278 834.722 485.222 835.778 485.667 835.333C485.722 835.278 485.778 834.222 485.333 834.667z"/>
<path style="fill:#75c7a1; stroke:none;" d="M1078 834L1079 835L1078 834z"/>
<path style="fill:#74c49d; stroke:none;" d="M102.333 835.667C102.278 835.722 102.222 836.778 102.667 836.333C102.722 836.278 102.778 835.222 102.333 835.667z"/>
<path style="fill:#abd5c7; stroke:none;" d="M703 835L704 836L703 835z"/>
<path style="fill:#49b287; stroke:none;" d="M767 835L768 836L767 835z"/>
<path style="fill:#94ccb3; stroke:none;" d="M1079 835L1080 836L1079 835z"/>
<path style="fill:#5fac7e; stroke:none;" d="M704 836L705 837L704 836z"/>
<path style="fill:#91ddb9; stroke:none;" d="M768 836L769 837L768 836z"/>
<path style="fill:#97dab9; stroke:none;" d="M1034 836L1034 842C1034.95 839.715 1034.95 838.285 1034 836z"/>
<path style="fill:#a8ebcc; stroke:none;" d="M1080 836L1081 837L1080 836z"/>
<path style="fill:#a2ecc7; stroke:none;" d="M102 837L102 840C102.696 838.446 102.696 838.554 102 837z"/>
<path style="fill:#15984a; stroke:none;" d="M480 837L480 848L482 848C482 844.01 482.474 840.266 480 837z"/>
<path style="fill:#9eebbd; stroke:none;" d="M704 837L705 838L704 837z"/>
<path style="fill:#c1ddcf; stroke:none;" d="M1081 837L1082 838L1081 837z"/>
<path style="fill:#74bc97; stroke:none;" d="M486.333 838.667C486.278 838.722 486.222 839.778 486.667 839.333C486.722 839.278 486.778 838.222 486.333 838.667z"/>
<path style="fill:#6ba984; stroke:none;" d="M705 838L706 839L705 838z"/>
<path style="fill:#72cea5; stroke:none;" d="M769 838L770 839L769 838z"/>
<path style="fill:#5fb48d; stroke:none;" d="M1081 838L1082 839L1081 838z"/>
<path style="fill:#e5ffef; stroke:none;" d="M487 839L488 846C488.965 843.295 488.466 841.45 487 839z"/>
<path style="fill:#a7e5c0; stroke:none;" d="M705 839L706 840L705 839z"/>
<path style="fill:#79b496; stroke:none;" d="M1082 839L1083 840L1082 839z"/>
<path style="fill:#128750; stroke:none;" d="M486 840L486 848C487.161 845.23 487.161 842.77 486 840z"/>
<path style="fill:#68b891; stroke:none;" d="M706 840L707 841L706 840z"/>
<path style="fill:#349164; stroke:none;" d="M1083.33 840.667C1083.28 840.723 1083.22 841.777 1083.67 841.333C1083.72 841.278 1083.78 840.222 1083.33 840.667z"/>
<path style="fill:#9dedc6; stroke:none;" d="M706 841L707 842L706 841z"/>
<path style="fill:#9dddbb; stroke:none;" d="M771 841L772 842L771 841z"/>
<path style="fill:#85c5a2; stroke:none;" d="M1084 841L1085 842L1084 841z"/>
<path style="fill:#65b38c; stroke:none;" d="M707 842L708 843L707 842z"/>
<path style="fill:#6cb693; stroke:none;" d="M1034 842L1034 846C1034.71 844.24 1034.71 843.76 1034 842z"/>
<path style="fill:#8ed6b0; stroke:none;" d="M1085 842L1086 843L1085 842z"/>
<path style="fill:#e8fff7; stroke:none;" d="M1086 842C1087.69 844.909 1089.34 846.95 1092 849C1093.13 844.157 1090.35 843.059 1086 842z"/>
<path style="fill:#76cfa1; stroke:none;" d="M103 843L103 846C103.696 844.446 103.696 844.554 103 843z"/>
<path style="fill:#97d8b8; stroke:none;" d="M487 843L488 844L487 843z"/>
<path style="fill:#80c09d; stroke:none;" d="M772 843L773 844L772 843z"/>
<path style="fill:#b3d7bb; stroke:none;" d="M1086 843L1087 844L1086 843z"/>
<path style="fill:#2c8158; stroke:none;" d="M487 844L487 848C487.71 846.24 487.71 845.759 487 844z"/>
<path style="fill:#7eb794; stroke:none;" d="M708 844L709 845L708 844z"/>
<path style="fill:#a0f1c8; stroke:none;" d="M1087 844L1088 845L1087 844z"/>
<path style="fill:#51a279; stroke:none;" d="M1087 845L1088 846L1087 845z"/>
<path style="fill:#acffd5; stroke:none;" d="M103.333 846.667C103.278 846.722 103.222 847.778 103.667 847.333C103.722 847.278 103.778 846.222 103.333 846.667z"/>
<path style="fill:#c2ead2; stroke:none;" d="M488.333 846.667C488.278 846.722 488.222 847.778 488.667 847.333C488.722 847.278 488.778 846.222 488.333 846.667z"/>
<path style="fill:#81bf98; stroke:none;" d="M709 846L710 847L709 846z"/>
<path style="fill:#95e5be; stroke:none;" d="M774 846L775 847L774 846z"/>
<path style="fill:#8bb4a6; stroke:none;" d="M1088 846L1089 847L1088 846z"/>
<path style="fill:#3f685a; stroke:none;" d="M1088 847L1089 848L1088 847z"/>
<path style="fill:#94b4a9; stroke:none;" d="M1089 847L1090 848L1089 847z"/>
<path style="fill:#2b865b; stroke:none;" d="M104 848C104.625 851.363 104.972 854.578 105 858C106.361 854.756 106.789 850.437 104 848z"/>
<path style="fill:#39a470; stroke:none;" d="M488.333 848.667C488.278 848.722 488.222 849.778 488.667 849.333C488.722 849.278 488.778 848.222 488.333 848.667z"/>
<path style="fill:#b5e7ca; stroke:none;" d="M710.333 848.667C710.278 848.722 710.222 849.778 710.667 849.333C710.722 849.278 710.778 848.222 710.333 848.667z"/>
<path style="fill:#11975a; stroke:none;" d="M775 848C775.479 853.792 778.499 858.452 780 864L784 863C781.594 857.853 778.648 852.357 775 848z"/>
<path style="fill:#b7fad7; stroke:none;" d="M1033 848L1033 856C1034.16 853.23 1034.16 850.77 1033 848z"/>
<path style="fill:#7ed9ad; stroke:none;" d="M1090 848L1091 849L1090 848z"/>
<path style="fill:#429f70; stroke:none;" d="M711 849L712 850L711 849z"/>
<path style="fill:#a3e7c2; stroke:none;" d="M776 849L777 850L776 849z"/>
<path style="fill:#acd4bc; stroke:none;" d="M1091 849L1092 850L1091 849z"/>
<path style="fill:#7dbc9f; stroke:none;" d="M104.333 850.667C104.278 850.722 104.222 851.778 104.667 851.333C104.722 851.278 104.778 850.222 104.333 850.667z"/>
<path style="fill:#a6f0cb; stroke:none;" d="M489 850L489 853C489.696 851.446 489.696 851.554 489 850z"/>
<path style="fill:#ebfff5; stroke:none;" d="M710 850C710.583 854.102 712.497 857.726 716 860C714.76 856.085 712.952 852.862 710 850z"/>
<path style="fill:#8fd3ae; stroke:none;" d="M711 850L712 851L711 850z"/>
<path style="fill:#93c8aa; stroke:none;" d="M777 851L778 852L777 851z"/>
<path style="fill:#b7efd4; stroke:none;" d="M104.333 852.667C104.278 852.722 104.222 853.778 104.667 853.333C104.722 853.278 104.778 852.222 104.333 852.667z"/>
<path style="fill:#98d4b2; stroke:none;" d="M712 852L713 853L712 852z"/>
<path style="fill:#bdfadb; stroke:none;" d="M490 853L490 856C490.696 854.446 490.696 854.554 490 853z"/>
<path style="fill:#70b893; stroke:none;" d="M778 853L779 854L778 853z"/>
<path style="fill:#66a988; stroke:none;" d="M1094 853L1095 854L1094 853z"/>
<path style="fill:#169057; stroke:none;" d="M106 854L107 870C108.861 865.554 107.854 858.377 106 854z"/>
<path style="fill:#9ed0b3; stroke:none;" d="M713 854L714 855L713 854z"/>
<path style="fill:#8ee0ba; stroke:none;" d="M779 854L780 855L779 854z"/>
<path style="fill:#d4ffe9; stroke:none;" d="M1032 854L1032 864C1033.35 860.793 1033.35 857.207 1032 854z"/>
<path style="fill:#7ad2a8; stroke:none;" d="M1095 854L1096 855L1095 854z"/>
<path style="fill:#96d5b8; stroke:none;" d="M1096 855L1097 856L1096 855z"/>
<path style="fill:#83d1aa; stroke:none;" d="M490 856L491 857L490 856z"/>
<path style="fill:#87d7b0; stroke:none;" d="M780 856L781 857L780 856z"/>
<path style="fill:#f0fcf8; stroke:none;" d="M782 858C785.435 865.709 791.777 879.585 799 884L798 880L800 880L800 876C798.418 873.943 798.174 872.612 798 870C795.67 870.315 794.082 870.902 792 872C791.102 866.491 788.484 862.266 785 858L789 859C786.892 856.331 785.006 856.809 782 858z"/>
<path style="fill:#6db993; stroke:none;" d="M1033 856L1033 860C1033.71 858.24 1033.71 857.76 1033 856z"/>
<path style="fill:#1a8251; stroke:none;" d="M490 857C490.032 859.875 489.792 862.074 492 864C491.652 861.428 491.222 859.283 490 857z"/>
<path style="fill:#5bbe8f; stroke:none;" d="M715 857L716 858L715 857z"/>
<path style="fill:#2e7b57; stroke:none;" d="M1096.67 857.333C1096.22 857.777 1097.28 857.722 1097.33 857.667C1097.78 857.222 1096.72 857.278 1096.67 857.333z"/>
<path style="fill:#a3e6c3; stroke:none;" d="M105.333 858.667C105.278 858.722 105.222 859.778 105.667 859.333C105.722 859.278 105.778 858.222 105.333 858.667z"/>
<path style="fill:#66b68f; stroke:none;" d="M781 858L782 859L781 858z"/>
<path style="fill:#0e814a; stroke:none;" d="M1098 858L1099 862C1099.55 859.992 1099.3 859.668 1098 858z"/>
<path style="fill:#eafff8; stroke:none;" d="M1099 858L1103 864C1103.87 861.948 1103.95 860.253 1104 858L1099 858z"/>
<path style="fill:#5bb287; stroke:none;" d="M716 859L717 860L716 859z"/>
<path style="fill:#a7e4c2; stroke:none;" d="M782 859L783 860L782 859z"/>
<path style="fill:#84caa8; stroke:none;" d="M491 860L492 861L491 860z"/>
<path style="fill:#378961; stroke:none;" d="M1033 860L1033 864C1033.71 862.241 1033.71 861.76 1033 860z"/>
<path style="fill:#32895f; stroke:none;" d="M1100.33 860.667C1100.28 860.722 1100.22 861.777 1100.67 861.333C1100.72 861.278 1100.78 860.222 1100.33 860.667z"/>
<path style="fill:#80d1a6; stroke:none;" d="M717 861L718 862L717 861z"/>
<path style="fill:#72cda2; stroke:none;" d="M783 861L784 862L783 861z"/>
<path style="fill:#77b397; stroke:none;" d="M1101 861L1102 862L1101 861z"/>
<path style="fill:#90cbab; stroke:none;" d="M1102 862L1103 863L1102 862z"/>
<path style="fill:#60da9f; stroke:none;" d="M718 863L719 864L718 863z"/>
<path style="fill:#8ba59c; stroke:none;" d="M784 863L785 864L784 863z"/>
<path style="fill:#528d6d; stroke:none;" d="M1102 863L1103 864L1102 863z"/>
<path style="fill:#7ad0ab; stroke:none;" d="M106.333 864.667C106.278 864.722 106.222 865.778 106.667 865.333C106.722 865.278 106.778 864.222 106.333 864.667z"/>
<path style="fill:#359e73; stroke:none;" d="M492.333 864.667C492.278 864.722 492.222 865.778 492.667 865.333C492.722 865.278 492.778 864.222 492.333 864.667z"/>
<path style="fill:#6e857d; stroke:none;" d="M719 864L720 865L719 864z"/>
<path style="fill:#90eebc; stroke:none;" d="M785 864L786 865L785 864z"/>
<path style="fill:#c6ffe0; stroke:none;" d="M786.333 864.667C786.278 864.722 786.222 865.778 786.667 865.333C786.722 865.278 786.778 864.222 786.333 864.667z"/>
<path style="fill:#a0f6c7; stroke:none;" d="M1032 864L1032 868C1032.71 866.24 1032.71 865.76 1032 864z"/>
<path style="fill:#edfff6; stroke:none;" d="M1104 864C1106.58 867.772 1109.37 871.221 1113 874C1111.69 869.036 1109.33 864.863 1104 864z"/>
<path style="fill:#b3cac2; stroke:none;" d="M719 865L720 866L719 865z"/>
<path style="fill:#619678; stroke:none;" d="M1104 865L1105 866L1104 865z"/>
<path style="fill:#c6ffea; stroke:none;" d="M106 866L106 870C106.71 868.24 106.71 867.76 106 866z"/>
<path style="fill:#2b8a5e; stroke:none;" d="M492 866L493 872C493.889 869.492 493.495 868.185 492 866z"/>
<path style="fill:#7dcba1; stroke:none;" d="M786 866L787 867L786 866z"/>
<path style="fill:#218d59; stroke:none;" d="M1104 866C1105.15 868.249 1105.75 868.846 1108 870C1106.74 868.049 1105.98 867.16 1104 866z"/>
<path style="fill:#93d3b8; stroke:none;" d="M493 867L494 868L493 867z"/>
<path style="fill:#92edc1; stroke:none;" d="M720 867L721 868L720 867z"/>
<path style="fill:#339266; stroke:none;" d="M722 867L723 871C723.393 869.076 723.146 868.64 722 867z"/>
<path style="fill:#72b592; stroke:none;" d="M1106 867L1107 868L1106 867z"/>
<path style="fill:#6eb28d; stroke:none;" d="M721 868L722 869L721 868z"/>
<path style="fill:#5cb887; stroke:none;" d="M1032 868L1032 871C1032.7 869.446 1032.7 869.554 1032 868z"/>
<path style="fill:#79d2a4; stroke:none;" d="M1107 868L1108 869L1107 868z"/>
<path style="fill:#90d3b2; stroke:none;" d="M788 869L789 870L788 869z"/>
<path style="fill:#98d8b5; stroke:none;" d="M1108 869L1109 870L1108 869z"/>
<path style="fill:#83d1aa; stroke:none;" d="M107.333 870.667C107.278 870.722 107.222 871.778 107.667 871.333C107.722 871.278 107.778 870.222 107.333 870.667z"/>
<path style="fill:#208c52; stroke:none;" d="M108 870L109 881C110.549 877.307 110.258 873.305 108 870z"/>
<path style="fill:#82bf9e; stroke:none;" d="M494.333 870.667C494.278 870.722 494.222 871.778 494.667 871.333C494.722 871.278 494.778 870.222 494.333 870.667z"/>
<path style="fill:#6fac8b; stroke:none;" d="M722 870L723 871L722 870z"/>
<path style="fill:#38a271; stroke:none;" d="M789.333 871.667C789.278 871.722 789.222 872.778 789.667 872.333C789.722 872.278 789.778 871.222 789.333 871.667z"/>
<path style="fill:#c1fedd; stroke:none;" d="M107.333 872.667C107.278 872.722 107.222 873.778 107.667 873.333C107.722 873.278 107.778 872.222 107.333 872.667z"/>
<path style="fill:#3b9464; stroke:none;" d="M494.333 872.667C494.278 872.722 494.222 873.778 494.667 873.333C494.722 873.278 494.778 872.222 494.333 872.667z"/>
<path style="fill:#bff6d6; stroke:none;" d="M495.333 872.667C495.278 872.722 495.222 873.778 495.667 873.333C495.722 873.278 495.778 872.222 495.333 872.667z"/>
<path style="fill:#8dd3b1; stroke:none;" d="M723 872L724 873L723 872z"/>
<path style="fill:#96d7b5; stroke:none;" d="M790 872L791 873L790 872z"/>
<path style="fill:#dafff1; stroke:none;" d="M723 873C723.526 875.543 723.878 876.535 726 878C725.251 875.746 724.656 874.686 723 873z"/>
<path style="fill:#007e3d; stroke:none;" d="M789.333 873.667C789.278 873.722 789.222 874.778 789.667 874.333C789.722 874.278 789.778 873.222 789.333 873.667z"/>
<path style="fill:#6aab89; stroke:none;" d="M790 873L791 874L790 873z"/>
<path style="fill:#62ad84; stroke:none;" d="M1111 873L1112 874L1111 873z"/>
<path style="fill:#86d3a7; stroke:none;" d="M495 874L496 875L495 874z"/>
<path style="fill:#1d945e; stroke:none;" d="M725 874C726.53 878.204 728.743 882.137 731 886C731.732 881.629 728.924 876.049 725 874z"/>
<path style="fill:#7fcba4; stroke:none;" d="M791 874L792 875L791 874z"/>
<path style="fill:#67b085; stroke:none;" d="M1112 874L1113 875L1112 874z"/>
<path style="fill:#a1debf; stroke:none;" d="M1031.33 875.667C1031.28 875.723 1031.22 876.777 1031.67 876.333C1031.72 876.278 1031.78 875.222 1031.33 875.667z"/>
<path style="fill:#98baa1; stroke:none;" d="M1113 875L1114 876L1113 875z"/>
<path style="fill:#9ad4ae; stroke:none;" d="M108.333 876.667C108.278 876.722 108.222 877.778 108.667 877.333C108.722 877.278 108.778 876.222 108.333 876.667z"/>
<path style="fill:#189050; stroke:none;" d="M734 876L734 882L736 882L736 876L734 876z"/>
<path style="fill:#61c495; stroke:none;" d="M792 876L793 877L792 876z"/>
<path style="fill:#82deaf; stroke:none;" d="M1114 876L1115 877L1114 876z"/>
<path style="fill:#e3fffa; stroke:none;" d="M1115 876L1117 880C1117.68 878.494 1117.83 877.686 1118 876L1115 876z"/>
<path style="fill:#b4d2c8; stroke:none;" d="M496 877L497 878L496 877z"/>
<path style="fill:#5da982; stroke:none;" d="M726 877L727 878L726 877z"/>
<path style="fill:#a1e7c5; stroke:none;" d="M793 877L794 878L793 877z"/>
<path style="fill:#76b596; stroke:none;" d="M1031 877L1031 880C1031.7 878.446 1031.7 878.554 1031 877z"/>
<path style="fill:#a0e1bf; stroke:none;" d="M1115 877L1116 878L1115 877z"/>
<path style="fill:#789c90; stroke:none;" d="M496 878L497 879L496 878z"/>
<path style="fill:#56ae84; stroke:none;" d="M793 878L794 879L793 878z"/>
<path style="fill:#129553; stroke:none;" d="M1115 878L1115 886L1123 888L1123 892L1130 896C1126.53 889.193 1120.76 882.98 1115 878z"/>
<path style="fill:#61b486; stroke:none;" d="M727 879L728 880L727 879z"/>
<path style="fill:#9ac1ac; stroke:none;" d="M794 879L795 880L794 879z"/>
<path style="fill:#039b50; stroke:none;" d="M110 880C110.112 885.432 111.571 890.608 112 896L115 896C115.856 915.931 123.386 934.615 127 954C132.743 955.819 130.525 960.241 131.966 964.999C134.43 973.137 138.561 980.642 140.545 989C141.616 993.514 140.965 998.248 146 1000L145 1002L148 1002C148 1005.96 147.639 1010.21 149.147 1013.96C150.199 1016.57 152.427 1018.47 153.416 1021.09C154.876 1024.96 155.402 1029.08 157 1033C159.875 1024.47 155.391 1018.48 152.664 1010.75C151.062 1006.22 150.615 1000.76 150 996C147.054 994.974 145.684 994.243 143 996C142.227 989.672 143.767 982.486 144 976L138 976C138.985 970.473 141.84 965.544 143 960C139.314 955.918 137.569 950.032 144 948L144 944L128 944C127.88 939.033 125.464 935.676 124.479 930.985C123.856 928.016 124.109 924.961 123.402 922C122.439 917.968 120.231 913.936 119.745 909.83C119.221 905.41 120 900.482 120 896L122 896L122 900C122.683 898.494 122.826 897.685 123 896L128 896L128 880L110 880z"/>
<path style="fill:#9ffccf; stroke:none;" d="M497 880L498 881L497 880z"/>
<path style="fill:#248a58; stroke:none;" d="M794 880C794.574 883.8 795.178 886.67 799 888C797.685 884.957 796.268 882.421 794 880z"/>
<path style="fill:#d1ffe9; stroke:none;" d="M1030 880L1030 884C1030.71 882.24 1030.71 881.76 1030 880z"/>
<path style="fill:#53bc91; stroke:none;" d="M1117 880L1118 881L1117 880z"/>
<path style="fill:#c3fffd; stroke:none;" d="M1118 880C1118.55 881.635 1118.36 881.455 1120 882L1120 880L1118 880z"/>
<path style="fill:#6bab86; stroke:none;" d="M109 881L110 882L109 881z"/>
<path style="fill:#6ac79a; stroke:none;" d="M497 881L498 882L497 881z"/>
<path style="fill:#7bcba6; stroke:none;" d="M728 881L729 882L728 881z"/>
<path style="fill:#5cb887; stroke:none;" d="M795 881L796 882L795 881z"/>
<path style="fill:#5ec79e; stroke:none;" d="M1118 881L1119 882L1118 881z"/>
<path style="fill:#d3ffe6; stroke:none;" d="M109 882L109 886C109.71 884.24 109.71 883.76 109 882z"/>
<path style="fill:#0f9153; stroke:none;" d="M497 882C496.612 886.435 498.412 893.139 502 896C500.905 891.267 499.595 886.124 497 882z"/>
<path style="fill:#93deb7; stroke:none;" d="M796 882L797 883L796 882z"/>
<path style="fill:#63cca1; stroke:none;" d="M1119 882L1120 883L1119 882z"/>
<path style="fill:#85e2b6; stroke:none;" d="M729 883L730 884L729 883z"/>
<path style="fill:#9fd5bd; stroke:none;" d="M1120 883L1121 884L1120 883z"/>
<path style="fill:#6ebc92; stroke:none;" d="M498 884L499 885L498 884z"/>
<path style="fill:#d8ffe6; stroke:none;" d="M499.333 884.667C499.278 884.722 499.222 885.778 499.667 885.333C499.722 885.278 499.778 884.222 499.333 884.667z"/>
<path style="fill:#7dc9a3; stroke:none;" d="M797 884L798 885L797 884z"/>
<path style="fill:#b6f6d4; stroke:none;" d="M798.333 884.667C798.278 884.722 798.222 885.778 798.667 885.333C798.722 885.278 798.778 884.222 798.333 884.667z"/>
<path style="fill:#8ad2ac; stroke:none;" d="M1030.33 884.667C1030.28 884.723 1030.22 885.777 1030.67 885.333C1030.72 885.278 1030.78 884.222 1030.33 884.667z"/>
<path style="fill:#9be6c7; stroke:none;" d="M1121 884L1122 885L1121 884z"/>
<path style="fill:#dbfff3; stroke:none;" d="M1122 884C1123.3 886.763 1124.55 888.164 1127 890L1128 889L1125 884L1122 884z"/>
<path style="fill:#9be7c0; stroke:none;" d="M730 885L731 886L730 885z"/>
<path style="fill:#70cba2; stroke:none;" d="M110.333 886.667C110.278 886.722 110.222 887.778 110.667 887.333C110.722 887.278 110.778 886.222 110.333 886.667z"/>
<path style="fill:#89cda8; stroke:none;" d="M499.333 886.667C499.278 886.722 499.222 887.778 499.667 887.333C499.722 887.278 499.778 886.222 499.333 886.667z"/>
<path style="fill:#6eab89; stroke:none;" d="M731 886L732 887L731 886z"/>
<path style="fill:#5cc793; stroke:none;" d="M798 886L799 887L798 886z"/>
<path style="fill:#a2eec8; stroke:none;" d="M799.333 886.667C799.278 886.722 799.222 887.778 799.667 887.333C799.722 887.278 799.778 886.222 799.333 886.667z"/>
<path style="fill:#64b78d; stroke:none;" d="M1030.33 886.667C1030.28 886.723 1030.22 887.777 1030.67 887.333C1030.72 887.278 1030.78 886.222 1030.33 886.667z"/>
<path style="fill:#d4ffec; stroke:none;" d="M110 888L110 892C110.71 890.24 110.71 889.759 110 888z"/>
<path style="fill:#7abd9a; stroke:none;" d="M732 888L733 889L732 888z"/>
<path style="fill:#adeac9; stroke:none;" d="M500 889L501 890L500 889z"/>
<path style="fill:#93b9a2; stroke:none;" d="M800 889L801 890L800 889z"/>
<path style="fill:#83c4a2; stroke:none;" d="M1125 889L1126 890L1125 889z"/>
<path style="fill:#61bc90; stroke:none;" d="M500 890L501 891L500 890z"/>
<path style="fill:#daffed; stroke:none;" d="M501 890L502 894C502.393 892.076 502.146 891.64 501 890z"/>
<path style="fill:#91d2b2; stroke:none;" d="M733 890L734 891L733 890z"/>
<path style="fill:#d2fee5; stroke:none;" d="M801 890L804 896C804.676 893.837 804.387 892.268 804 890L801 890z"/>
<path style="fill:#82d5a9; stroke:none;" d="M1126 890L1127 891L1126 890z"/>
<path style="fill:#ebfff4; stroke:none;" d="M1127 890C1131.12 897.142 1137.35 905.106 1144 910C1142.8 906.507 1140.72 903.668 1140 900C1134.14 898.172 1133.23 891.342 1127 890z"/>
<path style="fill:#58ad8c; stroke:none;" d="M111 891L112 892L111 891z"/>
<path style="fill:#6fac8b; stroke:none;" d="M801 891L802 892L801 891z"/>
<path style="fill:#9bd8b7; stroke:none;" d="M1127 891L1128 892L1127 891z"/>
<path style="fill:#aef7dc; stroke:none;" d="M111.333 892.667C111.278 892.722 111.222 893.778 111.667 893.333C111.722 893.278 111.778 892.222 111.333 892.667z"/>
<path style="fill:#a6ceb6; stroke:none;" d="M734 892L735 893L734 892z"/>
<path style="fill:#8be7b8; stroke:none;" d="M802 892L803 893L802 892z"/>
<path style="fill:#7dc9a5; stroke:none;" d="M1029.33 892.667C1029.28 892.722 1029.22 893.778 1029.67 893.333C1029.72 893.278 1029.78 892.222 1029.33 892.667z"/>
<path style="fill:#a5f1ca; stroke:none;" d="M1128 892L1129 893L1128 892z"/>
<path style="fill:#7dcba4; stroke:none;" d="M501 893L502 894L501 893z"/>
<path style="fill:#68a083; stroke:none;" d="M735 893L736 894L735 893z"/>
<path style="fill:#baead4; stroke:none;" d="M502.333 894.667C502.278 894.722 502.222 895.778 502.667 895.333C502.722 895.278 502.778 894.222 502.333 894.667z"/>
<path style="fill:#c4d4c7; stroke:none;" d="M735 894L736 895L735 894z"/>
<path style="fill:#87d5ae; stroke:none;" d="M803 894L804 895L803 894z"/>
<path style="fill:#51a37d; stroke:none;" d="M1029.33 894.667C1029.28 894.722 1029.22 895.778 1029.67 895.333C1029.72 895.278 1029.78 894.222 1029.33 894.667z"/>
<path style="fill:#6bb791; stroke:none;" d="M1129 894L1130 895L1129 894z"/>
<path style="fill:#36845d; stroke:none;" d="M802.667 895.333C802.222 895.778 803.278 895.722 803.333 895.667C803.778 895.222 802.722 895.278 802.667 895.333z"/>
<path style="fill:#8abaa0; stroke:none;" d="M1130 895L1131 896L1130 895z"/>
<path style="fill:#94d7ae; stroke:none;" d="M112.333 896.667C112.278 896.722 112.222 897.778 112.667 897.333C112.722 897.278 112.778 896.222 112.333 896.667z"/>
<path style="fill:#1c8f58; stroke:none;" d="M113 896C113.193 900.436 114.41 904.62 115 909C116.745 904.833 115.346 900.378 115 896L113 896z"/>
<path style="fill:#3e9a6b; stroke:none;" d="M502 896L502 899C502.696 897.446 502.696 897.554 502 896z"/>
<path style="fill:#1f8254; stroke:none;" d="M737 896L739 900C739.369 897.777 738.86 897.266 737 896z"/>
<path style="fill:#188d58; stroke:none;" d="M804 896C804.318 898.877 804.62 900.334 807 902C806.291 899.588 805.573 897.949 804 896z"/>
<path style="fill:#308657; stroke:none;" d="M1029 896L1029 902C1029.95 899.715 1029.95 898.285 1029 896z"/>
<path style="fill:#70d6a4; stroke:none;" d="M1131 896L1132 897L1131 896z"/>
<path style="fill:#90bea4; stroke:none;" d="M737 897L738 898L737 897z"/>
<path style="fill:#95d6b6; stroke:none;" d="M805 897L806 898L805 897z"/>
<path style="fill:#9ddfbb; stroke:none;" d="M1132 897L1133 898L1132 897z"/>
<path style="fill:#dcffec; stroke:none;" d="M112 898C112.058 900.784 112.352 903.291 113 906C114.038 903.102 113.433 900.696 112 898z"/>
<path style="fill:#91d9b3; stroke:none;" d="M503 898L504 899L503 898z"/>
<path style="fill:#addbbe; stroke:none;" d="M1028.33 898.667C1028.28 898.723 1028.22 899.777 1028.67 899.333C1028.72 899.278 1028.78 898.222 1028.33 898.667z"/>
<path style="fill:#a5e5c3; stroke:none;" d="M1133 898L1134 899L1133 898z"/>
<path style="fill:#84d1ad; stroke:none;" d="M738 899L739 900L738 899z"/>
<path style="fill:#90c6ac; stroke:none;" d="M806 899L807 900L806 899z"/>
<path style="fill:#62a280; stroke:none;" d="M1133 899L1134 900L1133 899z"/>
<path style="fill:#5fa782; stroke:none;" d="M739 900L740 901L739 900z"/>
<path style="fill:#dbfff8; stroke:none;" d="M807 900L808 902C809.635 901.455 809.455 901.635 810 900L807 900z"/>
<path style="fill:#69ab87; stroke:none;" d="M1028.33 900.667C1028.28 900.723 1028.22 901.777 1028.67 901.333C1028.72 901.278 1028.78 900.222 1028.33 900.667z"/>
<path style="fill:#6ec092; stroke:none;" d="M113 901L114 902L113 901z"/>
<path style="fill:#86cea8; stroke:none;" d="M504 901L505 902L504 901z"/>
<path style="fill:#97dfba; stroke:none;" d="M739 901L740 902L739 901z"/>
<path style="fill:#65b592; stroke:none;" d="M807 901L808 902L807 901z"/>
<path style="fill:#6cb693; stroke:none;" d="M1135 901L1136 902L1135 901z"/>
<path style="fill:#b3f6d3; stroke:none;" d="M505.333 902.667C505.278 902.722 505.222 903.778 505.667 903.333C505.722 903.278 505.778 902.222 505.333 902.667z"/>
<path style="fill:#6ca585; stroke:none;" d="M740 902L741 903L740 902z"/>
<path style="fill:#8becc1; stroke:none;" d="M808 902L809 903L808 902z"/>
<path style="fill:#109657; stroke:none;" d="M1028 902L1028 908L1030 907L1030 911C1031.17 907.999 1030.4 905.159 1030 902L1028 902z"/>
<path style="fill:#6ac79a; stroke:none;" d="M1136 902L1137 903L1136 902z"/>
<path style="fill:#afe8c8; stroke:none;" d="M740 903L741 904L740 903z"/>
<path style="fill:#90d3b2; stroke:none;" d="M1137 903L1138 904L1137 903z"/>
<path style="fill:#3a9768; stroke:none;" d="M505.333 904.667C505.278 904.722 505.222 905.778 505.667 905.333C505.722 905.278 505.778 904.222 505.333 904.667z"/>
<path style="fill:#77b493; stroke:none;" d="M741 904L742 905L741 904z"/>
<path style="fill:#159258; stroke:none;" d="M742 904C742.849 906.807 743.548 908.388 746 910C745.151 907.193 744.452 905.612 742 904z"/>
<path style="fill:#83d8af; stroke:none;" d="M809 904L810 905L809 904z"/>
<path style="fill:#acf1d2; stroke:none;" d="M1027 904L1027 907C1027.7 905.446 1027.7 905.554 1027 904z"/>
<path style="fill:#97edc0; stroke:none;" d="M1138 904L1139 905L1138 904z"/>
<path style="fill:#5bab84; stroke:none;" d="M114 905L115 906L114 905z"/>
<path style="fill:#a9e6c5; stroke:none;" d="M741 905L742 906L741 905z"/>
<path style="fill:#148b51; stroke:none;" d="M1137 905C1138.78 908.457 1140.15 910.916 1144 912C1142.03 908.882 1140.3 906.674 1137 905z"/>
<path style="fill:#ceffe9; stroke:none;" d="M114 906L114 912C114.951 909.715 114.951 908.285 114 906z"/>
<path style="fill:#93e1b9; stroke:none;" d="M506 906L507 907L506 906z"/>
<path style="fill:#d4fff3; stroke:none;" d="M507 906L508 910C508.393 908.076 508.146 907.64 507 906z"/>
<path style="fill:#acebcc; stroke:none;" d="M742.333 906.667C742.278 906.722 742.222 907.778 742.667 907.333C742.722 907.278 742.778 906.222 742.333 906.667z"/>
<path style="fill:#6abf95; stroke:none;" d="M810 906L811 907L810 906z"/>
<path style="fill:#5ba981; stroke:none;" d="M506 907L507 908L506 907z"/>
<path style="fill:#3ba277; stroke:none;" d="M1027.33 907.667C1027.28 907.723 1027.22 908.777 1027.67 908.333C1027.72 908.278 1027.78 907.222 1027.33 907.667z"/>
<path style="fill:#a3d3b9; stroke:none;" d="M743 908L744 909L743 908z"/>
<path style="fill:#3db27e; stroke:none;" d="M811 908L812 909L811 908z"/>
<path style="fill:#cef7e5; stroke:none;" d="M812 908C812.684 909.58 812.805 909.777 814 911L814 908L812 908z"/>
<path style="fill:#b3efd6; stroke:none;" d="M1026 908L1026 912C1026.71 910.24 1026.71 909.76 1026 908z"/>
<path style="fill:#50ad80; stroke:none;" d="M1141 908L1142 909L1141 908z"/>
<path style="fill:#52ad82; stroke:none;" d="M115 909L116 910L115 909z"/>
<path style="fill:#76c8a2; stroke:none;" d="M507 909L508 910L507 909z"/>
<path style="fill:#7ac19f; stroke:none;" d="M1142 909L1143 910L1142 909z"/>
<path style="fill:#a3f8d1; stroke:none;" d="M115.333 910.667C115.278 910.722 115.222 911.778 115.667 911.333C115.722 911.278 115.778 910.222 115.333 910.667z"/>
<path style="fill:#a9f6d6; stroke:none;" d="M508.333 910.667C508.278 910.722 508.222 911.778 508.667 911.333C508.722 911.278 508.778 910.222 508.333 910.667z"/>
<path style="fill:#9ed7b7; stroke:none;" d="M744 910L745 911L744 910z"/>
<path style="fill:#49ad7b; stroke:none;" d="M745.333 910.667C745.278 910.722 745.222 911.778 745.667 911.333C745.722 911.278 745.778 910.222 745.333 910.667z"/>
<path style="fill:#92d5b6; stroke:none;" d="M1143 910L1144 911L1143 910z"/>
<path style="fill:#cff4e2; stroke:none;" d="M1144.33 910.667C1144.28 910.722 1144.22 911.777 1144.67 911.333C1144.72 911.278 1144.78 910.222 1144.33 910.667z"/>
<path style="fill:#8fc9b1; stroke:none;" d="M813 911L814 912L813 911z"/>
<path style="fill:#1e8454; stroke:none;" d="M116 912L117 918C118.039 915.471 118.093 913.824 116 912z"/>
<path style="fill:#258758; stroke:none;" d="M508 912C508.287 914.868 509.009 917.294 510 920C510.845 916.96 509.71 914.592 508 912z"/>
<path style="fill:#b5f0d4; stroke:none;" d="M509.333 912.667C509.278 912.722 509.222 913.778 509.667 913.333C509.722 913.278 509.778 912.222 509.333 912.667z"/>
<path style="fill:#eafffa; stroke:none;" d="M510 912L511 918C511.994 915.425 511.654 914.177 510 912z"/>
<path style="fill:#289159; stroke:none;" d="M746 912L749 919C749.874 915.822 748.684 913.779 746 912z"/>
<path style="fill:#bffff9; stroke:none;" d="M814 912L815 915C815.685 913.752 815.749 913.452 816 912L814 912z"/>
<path style="fill:#62c09b; stroke:none;" d="M1026.33 912.667C1026.28 912.723 1026.22 913.777 1026.67 913.333C1026.72 913.278 1026.78 912.222 1026.33 912.667z"/>
<path style="fill:#179154; stroke:none;" d="M1144 912C1145.63 918.694 1151.08 926.092 1158 928C1154.08 922.365 1149.35 916.315 1144 912z"/>
<path style="fill:#dcffef; stroke:none;" d="M1145 912L1151 920C1151.43 917.143 1149.97 914.73 1149 912L1145 912z"/>
<path style="fill:#61bb95; stroke:none;" d="M746 913L747 914L746 913z"/>
<path style="fill:#60c8a3; stroke:none;" d="M814 913L815 914L814 913z"/>
<path style="fill:#b0ebcd; stroke:none;" d="M116.333 914.667C116.278 914.722 116.222 915.778 116.667 915.333C116.722 915.278 116.778 914.222 116.333 914.667z"/>
<path style="fill:#80c7a5; stroke:none;" d="M509 914L510 915L509 914z"/>
<path style="fill:#60bf93; stroke:none;" d="M747 915L748 916L747 915z"/>
<path style="fill:#4db68d; stroke:none;" d="M815 915L816 916L815 915z"/>
<path style="fill:#6cb38b; stroke:none;" d="M1147 915L1148 916L1147 915z"/>
<path style="fill:#edfff8; stroke:none;" d="M116 916L115 926C115.683 924.494 115.826 923.685 116 922C117.043 924.096 117.603 925.699 118 928C119.525 924.084 117.949 919.586 116 916z"/>
<path style="fill:#8be2b8; stroke:none;" d="M510 916L511 917L510 916z"/>
<path style="fill:#8cd9b5; stroke:none;" d="M816 916L817 917L816 916z"/>
<path style="fill:#77c7a0; stroke:none;" d="M1148 916L1149 917L1148 916z"/>
<path style="fill:#7cc79e; stroke:none;" d="M748 917L749 918L748 917z"/>
<path style="fill:#4a9773; stroke:none;" d="M816 917L817 918L816 917z"/>
<path style="fill:#8fccab; stroke:none;" d="M1025 917L1025 920C1025.7 918.446 1025.7 918.554 1025 917z"/>
<path style="fill:#98d3b7; stroke:none;" d="M1149 917L1150 918L1149 917z"/>
<path style="fill:#9fe2bf; stroke:none;" d="M117.333 918.667C117.278 918.722 117.222 919.778 117.667 919.333C117.722 919.278 117.778 918.222 117.333 918.667z"/>
<path style="fill:#278855; stroke:none;" d="M118 918L119 926C120.232 922.977 119.825 920.672 118 918z"/>
<path style="fill:#7ed3aa; stroke:none;" d="M511.333 918.667C511.278 918.722 511.222 919.778 511.667 919.333C511.722 919.278 511.778 918.222 511.333 918.667z"/>
<path style="fill:#65d7a6; stroke:none;" d="M817 918L818 919L817 918z"/>
<path style="fill:#d8ffe8; stroke:none;" d="M1024 918L1024 923C1024.83 920.97 1024.83 920.031 1024 918z"/>
<path style="fill:#139349; stroke:none;" d="M1030 918L1030 928L1032 928C1031.99 924.304 1032.12 921.109 1030 918z"/>
<path style="fill:#ccf6de; stroke:none;" d="M749.333 919.667C749.278 919.722 749.222 920.778 749.667 920.333C749.722 920.278 749.778 919.222 749.333 919.667z"/>
<path style="fill:#50ab80; stroke:none;" d="M750 920L751 921L750 920z"/>
<path style="fill:#67bd90; stroke:none;" d="M818 920L819 921L818 920z"/>
<path style="fill:#29905b; stroke:none;" d="M1025 920L1025 924C1025.71 922.24 1025.71 921.76 1025 920z"/>
<path style="fill:#e0fff0; stroke:none;" d="M1152 920L1158 928C1158.58 926.63 1158.8 925.493 1159 924L1157 924L1158 922L1155 922L1156 920L1152 920z"/>
<path style="fill:#a8c3b4; stroke:none;" d="M512 921L513 922L512 921z"/>
<path style="fill:#8de8bd; stroke:none;" d="M750 921L751 922L750 921z"/>
<path style="fill:#148a4e; stroke:none;" d="M818 921C818.526 923.543 818.878 924.535 821 926C820.199 923.896 819.503 922.663 818 921z"/>
<path style="fill:#639979; stroke:none;" d="M1152 921L1153 922L1152 921z"/>
<path style="fill:#81ba99; stroke:none;" d="M118 922L119 923L118 922z"/>
<path style="fill:#178f4f; stroke:none;" d="M512 922C512.547 927.456 514.219 933.925 519 937C517.308 931.957 515.212 926.244 512 922z"/>
<path style="fill:#f1fff6; stroke:none;" d="M513 922C514.742 927.115 517.591 931.247 520 936C521.494 931.454 517.488 923.48 513 922z"/>
<path style="fill:#64ab8d; stroke:none;" d="M751 922L752 923L751 922z"/>
<path style="fill:#57aa7e; stroke:none;" d="M819 922L820 923L819 922z"/>
<path style="fill:#64b187; stroke:none;" d="M1153 922L1154 923L1153 922z"/>
<path style="fill:#a8d5be; stroke:none;" d="M513 923L514 924L513 923z"/>
<path style="fill:#addec0; stroke:none;" d="M820 923L821 924L820 923z"/>
<path style="fill:#77c395; stroke:none;" d="M1024.33 923.667C1024.28 923.722 1024.22 924.778 1024.67 924.333C1024.72 924.278 1024.78 923.222 1024.33 923.667z"/>
<path style="fill:#8cd2ad; stroke:none;" d="M1154 923L1155 924L1154 923z"/>
<path style="fill:#6bcc9f; stroke:none;" d="M752 924L753 925L752 924z"/>
<path style="fill:#d5ffe7; stroke:none;" d="M821 924L822 927C822.685 925.752 822.749 925.452 823 924L821 924z"/>
<path style="fill:#80d9ad; stroke:none;" d="M1155 924L1156 925L1155 924z"/>
<path style="fill:#e5fff7; stroke:none;" d="M747 925C746.014 926.479 746 926.203 746 928L750 928C748.897 926.543 748.457 926.103 747 925z"/>
<path style="fill:#bfffee; stroke:none;" d="M752 925C752.406 927.092 752.217 927.02 754 928C753.309 926.611 753.016 926.139 752 925z"/>
<path style="fill:#88d2ad; stroke:none;" d="M821 925L822 926L821 925z"/>
<path style="fill:#388a5c; stroke:none;" d="M1024 925L1024 928C1024.7 926.446 1024.7 926.554 1024 925z"/>
<path style="fill:#a6e9ca; stroke:none;" d="M1156 925L1157 926L1156 925z"/>
<path style="fill:#9be6bd; stroke:none;" d="M119.333 926.667C119.278 926.722 119.222 927.778 119.667 927.333C119.722 927.278 119.778 926.222 119.333 926.667z"/>
<path style="fill:#60bc93; stroke:none;" d="M514 926L515 927L514 926z"/>
<path style="fill:#71d4a5; stroke:none;" d="M753 926L754 927L753 926z"/>
<path style="fill:#169256; stroke:none;" d="M821 926C820.412 929.464 821.07 931.103 823 934C824.098 930.833 823.037 928.561 821 926z"/>
<path style="fill:#83be9e; stroke:none;" d="M822 927L823 928L822 927z"/>
<path style="fill:#82d9a6; stroke:none;" d="M515 928L516 929L515 928z"/>
<path style="fill:#90d1af; stroke:none;" d="M754 928L755 929L754 928z"/>
<path style="fill:#ddfff6; stroke:none;" d="M823 928C825.78 933.283 828.703 938.483 831 944C832.835 938.647 827.281 933.559 827 928L823 928z"/>
<path style="fill:#d8fff0; stroke:none;" d="M1021 928L1020 942C1022.23 937.821 1022.8 932.698 1023 928L1021 928z"/>
<path style="fill:#74c3a4; stroke:none;" d="M1023.33 928.667C1023.28 928.722 1023.22 929.778 1023.67 929.333C1023.72 929.278 1023.78 928.222 1023.33 928.667z"/>
<path style="fill:#55a67b; stroke:none;" d="M120 929L121 930L120 929z"/>
<path style="fill:#dbfff3; stroke:none;" d="M754 929L754 934C757.704 933.19 756.306 930.977 754 929z"/>
<path style="fill:#69be95; stroke:none;" d="M823 929L824 930L823 929z"/>
<path style="fill:#67b486; stroke:none;" d="M1159 929L1160 930L1159 929z"/>
<path style="fill:#acf7d0; stroke:none;" d="M120.333 930.667C120.278 930.722 120.222 931.778 120.667 931.333C120.722 931.278 120.778 930.222 120.333 930.667z"/>
<path style="fill:#8ed2a9; stroke:none;" d="M516 930L517 931L516 930z"/>
<path style="fill:#a1e8c6; stroke:none;" d="M824 930L825 931L824 930z"/>
<path style="fill:#84c79e; stroke:none;" d="M1160 930L1161 931L1160 930z"/>
<path style="fill:#5ea583; stroke:none;" d="M824 931L825 932L824 931z"/>
<path style="fill:#b2d6bc; stroke:none;" d="M1161 931L1162 932L1161 931z"/>
<path style="fill:#95e3bb; stroke:none;" d="M517 932L518 933L517 932z"/>
<path style="fill:#d1fff2; stroke:none;" d="M518 932C518.426 934.469 519.132 936.645 520 939C520.797 936.111 519.836 934.308 518 932z"/>
<path style="fill:#248758; stroke:none;" d="M824 932C824.276 935.892 827.083 943.165 831 944C829.199 939.675 827.35 935.315 824 932z"/>
<path style="fill:#97debc; stroke:none;" d="M825 932L826 933L825 932z"/>
<path style="fill:#98e6bf; stroke:none;" d="M1162 932L1163 933L1162 932z"/>
<path style="fill:#daffee; stroke:none;" d="M1163 932C1163.94 933.948 1164.39 934.55 1166 936L1166 932L1163 932z"/>
<path style="fill:#53b584; stroke:none;" d="M121 933L122 934L121 933z"/>
<path style="fill:#57a57d; stroke:none;" d="M517 933L518 934L517 933z"/>
<path style="fill:#9fdbc1; stroke:none;" d="M1022 933L1023 934L1022 933z"/>
<path style="fill:#b7fdd9; stroke:none;" d="M121.333 934.667C121.278 934.722 121.222 935.778 121.667 935.333C121.722 935.278 121.778 934.222 121.333 934.667z"/>
<path style="fill:#95e1bb; stroke:none;" d="M826 934L827 935L826 934z"/>
<path style="fill:#4e9e79; stroke:none;" d="M1022.33 934.667C1022.28 934.722 1022.22 935.778 1022.67 935.333C1022.72 935.278 1022.78 934.222 1022.33 934.667z"/>
<path style="fill:#62ae8a; stroke:none;" d="M1164 935L1165 936L1164 935z"/>
<path style="fill:#85d3ac; stroke:none;" d="M827 936L828 937L827 936z"/>
<path style="fill:#66b68f; stroke:none;" d="M1165 936L1166 937L1165 936z"/>
<path style="fill:#64b98f; stroke:none;" d="M122 937L123 938L122 937z"/>
<path style="fill:#67bc95; stroke:none;" d="M519 937L520 938L519 937z"/>
<path style="fill:#58c493; stroke:none;" d="M759 937L760 938L759 937z"/>
<path style="fill:#8cc1a3; stroke:none;" d="M1166 937L1167 938L1166 937z"/>
<path style="fill:#ddfff3; stroke:none;" d="M122 938C122.077 940.645 121.997 942.266 124 944C123.532 941.798 123.063 939.969 122 938z"/>
<path style="fill:#71bf98; stroke:none;" d="M828 938L829 939L828 938z"/>
<path style="fill:#92dfb5; stroke:none;" d="M1021 938L1022 939L1021 938z"/>
<path style="fill:#8edab3; stroke:none;" d="M1167 938L1168 939L1167 938z"/>
<path style="fill:#7ac09c; stroke:none;" d="M520 939L521 940L520 939z"/>
<path style="fill:#64c399; stroke:none;" d="M760 939L761 940L760 939z"/>
<path style="fill:#a2d7c3; stroke:none;" d="M1168 939L1169 940L1168 939z"/>
<path style="fill:#1d8552; stroke:none;" d="M519 940C519.776 942.132 520.13 942.771 522 944C521.223 941.868 520.87 941.229 519 940z"/>
<path style="fill:#68b591; stroke:none;" d="M829 940L830 941L829 940z"/>
<path style="fill:#1d865e; stroke:none;" d="M1168 940C1168.78 942.132 1169.13 942.771 1171 944C1170.06 942.051 1169.61 941.45 1168 940z"/>
<path style="fill:#70cb9f; stroke:none;" d="M123 941L124 942L123 941z"/>
<path style="fill:#83c4a4; stroke:none;" d="M521 941L522 942L521 941z"/>
<path style="fill:#80c6a4; stroke:none;" d="M761 941L762 942L761 941z"/>
<path style="fill:#98d4b8; stroke:none;" d="M522.333 942.667C522.278 942.722 522.222 943.778 522.667 943.333C522.722 943.278 522.778 942.222 522.333 942.667z"/>
<path style="fill:#e5fff4; stroke:none;" d="M761 942C761.75 946.427 762.388 952.38 767 954C765.752 949.667 763.883 945.48 761 942z"/>
<path style="fill:#67ba8e; stroke:none;" d="M762.333 942.667C762.278 942.722 762.222 943.778 762.667 943.333C762.722 943.278 762.778 942.222 762.333 942.667z"/>
<path style="fill:#64b992; stroke:none;" d="M830 942L831 943L830 942z"/>
<path style="fill:#94cbab; stroke:none;" d="M1020.33 942.667C1020.28 942.722 1020.22 943.778 1020.67 943.333C1020.72 943.278 1020.78 942.222 1020.33 942.667z"/>
<path style="fill:#6eaa91; stroke:none;" d="M1171 943L1172 944L1171 943z"/>
<path style="fill:#aae0c0; stroke:none;" d="M124.333 944.667C124.278 944.722 124.222 945.778 124.667 945.333C124.722 945.278 124.778 944.222 124.333 944.667z"/>
<path style="fill:#238753; stroke:none;" d="M125 944L125 948C125.71 946.24 125.71 945.759 125 944z"/>
<path style="fill:#ecfef2; stroke:none;" d="M523 944C524.702 947.97 526.535 954.058 531 955C531.61 960.487 532.936 966.126 538 969C535.751 963.591 533.921 953.869 528 952L526 944L523 944z"/>
<path style="fill:#258f5f; stroke:none;" d="M763 944L771 961C772.063 959.031 772.532 957.202 773 955C768.255 952.375 767.674 946.66 763 944z"/>
<path style="fill:#dcfff1; stroke:none;" d="M1018 944L1017 954C1018.69 950.889 1019.49 947.499 1020 944L1018 944z"/>
<path style="fill:#79d2a6; stroke:none;" d="M1172 944L1173 945L1172 944z"/>
<path style="fill:#71cea1; stroke:none;" d="M523 945L524 946L523 945z"/>
<path style="fill:#95cdb0; stroke:none;" d="M763 945L764 946L763 945z"/>
<path style="fill:#a2e1c2; stroke:none;" d="M832 945L833 946L832 945z"/>
<path style="fill:#97d2b4; stroke:none;" d="M1173 945L1174 946L1173 945z"/>
<path style="fill:#e6fffd; stroke:none;" d="M124 946C124.389 950.922 125.942 955.256 127 960C128.579 955.829 126.244 949.715 124 946z"/>
<path style="fill:#2a855a; stroke:none;" d="M832.333 946.667C832.278 946.722 832.222 947.778 832.667 947.333C832.722 947.278 832.778 946.222 832.333 946.667z"/>
<path style="fill:#95e7c1; stroke:none;" d="M1174 946L1175 947L1174 946z"/>
<path style="fill:#e3fdf2; stroke:none;" d="M1175 946C1175.94 947.948 1176.39 948.55 1178 950C1177.22 947.868 1176.87 947.229 1175 946z"/>
<path style="fill:#78be9c; stroke:none;" d="M524 947L525 948L524 947z"/>
<path style="fill:#84d5aa; stroke:none;" d="M764 947L765 948L764 947z"/>
<path style="fill:#a4d9bf; stroke:none;" d="M833 947L834 948L833 947z"/>
<path style="fill:#59b286; stroke:none;" d="M1019.33 947.667C1019.28 947.722 1019.22 948.778 1019.67 948.333C1019.72 948.278 1019.78 947.222 1019.33 947.667z"/>
<path style="fill:#8cd7b0; stroke:none;" d="M125 948L126 949L125 948z"/>
<path style="fill:#86c5a8; stroke:none;" d="M525 949L526 950L525 949z"/>
<path style="fill:#82d8ab; stroke:none;" d="M765 949L766 950L765 949z"/>
<path style="fill:#76b799; stroke:none;" d="M1176 949L1177 950L1176 949z"/>
<path style="fill:#7cd3a9; stroke:none;" d="M526.333 950.667C526.278 950.722 526.222 951.778 526.667 951.333C526.722 951.278 526.778 950.222 526.333 950.667z"/>
<path style="fill:#4fb180; stroke:none;" d="M834 950L835 951L834 950z"/>
<path style="fill:#b8f3d3; stroke:none;" d="M835.333 950.667C835.278 950.722 835.222 951.778 835.667 951.333C835.722 951.278 835.778 950.222 835.333 950.667z"/>
<path style="fill:#a9f0ce; stroke:none;" d="M1018.33 950.667C1018.28 950.722 1018.22 951.778 1018.67 951.333C1018.72 951.278 1018.78 950.222 1018.33 950.667z"/>
<path style="fill:#71c39d; stroke:none;" d="M1177 950L1178 951L1177 950z"/>
<path style="fill:#66c198; stroke:none;" d="M126 951L127 952L126 951z"/>
<path style="fill:#88c6a1; stroke:none;" d="M766 951L767 952L766 951z"/>
<path style="fill:#89c1a6; stroke:none;" d="M1178 951L1179 952L1178 951z"/>
<path style="fill:#c0fae1; stroke:none;" d="M126.333 952.667C126.278 952.722 126.222 953.778 126.667 953.333C126.722 953.278 126.778 952.222 126.333 952.667z"/>
<path style="fill:#399667; stroke:none;" d="M835.333 952.667C835.278 952.722 835.222 953.778 835.667 953.333C835.722 953.278 835.778 952.222 835.333 952.667z"/>
<path style="fill:#459c72; stroke:none;" d="M1018.33 952.667C1018.28 952.722 1018.22 953.778 1018.67 953.333C1018.72 953.278 1018.78 952.222 1018.33 952.667z"/>
<path style="fill:#8ae5bc; stroke:none;" d="M1179 952L1180 953L1179 952z"/>
<path style="fill:#62c495; stroke:none;" d="M527 953L528 954L527 953z"/>
<path style="fill:#a1d3b8; stroke:none;" d="M767 953L768 954L767 953z"/>
<path style="fill:#a1d8b8; stroke:none;" d="M836 953L837 954L836 953z"/>
<path style="fill:#a4e1c2; stroke:none;" d="M1180 953L1181 954L1180 953z"/>
<path style="fill:#71b59c; stroke:none;" d="M127 954L128 955L127 954z"/>
<path style="fill:#119450; stroke:none;" d="M128 954L130 964C131.786 960.635 131.801 957.684 131 954L128 954z"/>
<path style="fill:#a2e3c5; stroke:none;" d="M1017 954L1017 957C1017.7 955.446 1017.7 955.554 1017 954z"/>
<path style="fill:#a8eeca; stroke:none;" d="M1181 954L1182 955L1181 954z"/>
<path style="fill:#edfef8; stroke:none;" d="M1182 954C1183.33 956.92 1185.2 959.337 1187 962C1188.86 957.4 1186.34 955.171 1182 954z"/>
<path style="fill:#76b998; stroke:none;" d="M528 955L529 956L528 955z"/>
<path style="fill:#d0ffe7; stroke:none;" d="M530 955C530.307 957.123 530.382 957.686 532 959C531.436 957.248 531.081 956.458 530 955z"/>
<path style="fill:#88d8b5; stroke:none;" d="M768 955L769 956L768 955z"/>
<path style="fill:#a3e5c1; stroke:none;" d="M837 955L838 956L837 955z"/>
<path style="fill:#64aa86; stroke:none;" d="M1181 955L1182 956L1181 955z"/>
<path style="fill:#99e6c2; stroke:none;" d="M529 956L530 957L529 956z"/>
<path style="fill:#4cb884; stroke:none;" d="M837 956L838 957L837 956z"/>
<path style="fill:#40bd7b; stroke:none;" d="M128 957L129 958L128 957z"/>
<path style="fill:#91dcbb; stroke:none;" d="M769 957L770 958L769 957z"/>
<path style="fill:#a5e5c3; stroke:none;" d="M838 957L839 958L838 957z"/>
<path style="fill:#c5eedc; stroke:none;" d="M1016 957L1016 960C1016.7 958.446 1016.7 958.554 1016 957z"/>
<path style="fill:#2b9d6a; stroke:none;" d="M1017 957L1016 962C1017.48 960.065 1017.79 959.307 1017 957z"/>
<path style="fill:#6bb68d; stroke:none;" d="M1183 957L1184 958L1183 957z"/>
<path style="fill:#9fffd4; stroke:none;" d="M128.333 958.667C128.278 958.722 128.222 959.778 128.667 959.333C128.722 959.278 128.778 958.222 128.333 958.667z"/>
<path style="fill:#60b389; stroke:none;" d="M838 958L839 959L838 958z"/>
<path style="fill:#9cc0b4; stroke:none;" d="M1184 958L1185 959L1184 958z"/>
<path style="fill:#99e4c3; stroke:none;" d="M770 959L771 960L770 959z"/>
<path style="fill:#22a363; stroke:none;" d="M838 959C838.684 960.58 838.805 960.777 840 962C839.316 960.42 839.195 960.223 838 959z"/>
<path style="fill:#577b6f; stroke:none;" d="M1184 959L1185 960L1184 959z"/>
<path style="fill:#b7d2c9; stroke:none;" d="M1185 959L1186 960L1185 959z"/>
<path style="fill:#edfeee; stroke:none;" d="M128 960L129 966C129.889 963.492 129.495 962.185 128 960z"/>
<path style="fill:#649b7b; stroke:none;" d="M129 960L130 961L129 960z"/>
<path style="fill:#90e3b7; stroke:none;" d="M531 960L532 961L531 960z"/>
<path style="fill:#ddfff3; stroke:none;" d="M840 960L841 964C841.751 961.954 841.656 961.404 840 960z"/>
<path style="fill:#dcfff3; stroke:none;" d="M1014 960L1014 966C1015.22 963.987 1015.65 962.342 1016 960L1014 960z"/>
<path style="fill:#99f2c6; stroke:none;" d="M1186 960L1187 961L1186 960z"/>
<path style="fill:#97ceae; stroke:none;" d="M129 961L130 962L129 961z"/>
<path style="fill:#8ddab6; stroke:none;" d="M771 961L772 962L771 961z"/>
<path style="fill:#7bc19d; stroke:none;" d="M532 962L533 963L532 962z"/>
<path style="fill:#61c28f; stroke:none;" d="M840 962L841 963L840 962z"/>
<path style="fill:#278256; stroke:none;" d="M1187.33 962.667C1187.28 962.723 1187.22 963.777 1187.67 963.333C1187.72 963.278 1187.78 962.222 1187.33 962.667z"/>
<path style="fill:#7bd0a6; stroke:none;" d="M772 963L773 964L772 963z"/>
<path style="fill:#109057; stroke:none;" d="M840 963C840.321 969.179 843.584 976.285 846 982C849.099 976.11 843.557 967.778 840 963z"/>
<path style="fill:#94e6c0; stroke:none;" d="M1015 963L1016 964L1015 963z"/>
<path style="fill:#629f7e; stroke:none;" d="M1188 963L1189 964L1188 963z"/>
<path style="fill:#aff7d2; stroke:none;" d="M130.333 964.667C130.278 964.722 130.222 965.778 130.667 965.333C130.722 965.278 130.778 964.222 130.333 964.667z"/>
<path style="fill:#12894f; stroke:none;" d="M131 964C132.621 973.533 137.006 982.815 140 992C141.224 988.766 139.958 986.216 138.958 983C136.983 976.645 135.214 969.262 131 964z"/>
<path style="fill:#66b890; stroke:none;" d="M533 964L534 965L533 964z"/>
<path style="fill:#449167; stroke:none;" d="M841.333 964.667C841.278 964.722 841.222 965.778 841.667 965.333C841.722 965.278 841.778 964.222 841.333 964.667z"/>
<path style="fill:#62c596; stroke:none;" d="M1189 964L1190 965L1189 964z"/>
<path style="fill:#e8fff8; stroke:none;" d="M1190 964C1191.46 966.486 1192.78 968.173 1195 970L1197 964L1190 964z"/>
<path style="fill:#7bc99f; stroke:none;" d="M773 965L774 966L773 965z"/>
<path style="fill:#8cc9aa; stroke:none;" d="M1190 965L1191 966L1190 965z"/>
<path style="fill:#57bd8d; stroke:none;" d="M534 966L535 967L534 966z"/>
<path style="fill:#82cda6; stroke:none;" d="M842 966L843 967L842 966z"/>
<path style="fill:#e1fced; stroke:none;" d="M843 966L845 972C845.802 970.009 845.829 968.153 846 966L843 966z"/>
<path style="fill:#dfffef; stroke:none;" d="M1013 966L1012 974C1013.62 971.295 1014.16 968.966 1013 966z"/>
<path style="fill:#9ce8c2; stroke:none;" d="M1014.33 966.667C1014.28 966.722 1014.22 967.778 1014.67 967.333C1014.72 967.278 1014.78 966.222 1014.33 966.667z"/>
<path style="fill:#97e9c1; stroke:none;" d="M1191 966L1192 967L1191 966z"/>
<path style="fill:#78c8a1; stroke:none;" d="M131 967L132 968L131 967z"/>
<path style="fill:#74cda1; stroke:none;" d="M774 967L775 968L774 967z"/>
<path style="fill:#34865e; stroke:none;" d="M1190.67 967.333C1190.22 967.777 1191.28 967.722 1191.33 967.667C1191.78 967.222 1190.72 967.278 1190.67 967.333z"/>
<path style="fill:#7dd2a9; stroke:none;" d="M843 968L844 969L843 968z"/>
<path style="fill:#1f955b; stroke:none;" d="M1192 968C1192.78 970.132 1193.13 970.771 1195 972C1194.04 970.233 1193.43 969.392 1192 968z"/>
<path style="fill:#4ba277; stroke:none;" d="M132 969L133 970L132 969z"/>
<path style="fill:#90d2ae; stroke:none;" d="M536 969L537 970L536 969z"/>
<path style="fill:#5dc092; stroke:none;" d="M775 969L776 970L775 969z"/>
<path style="fill:#91ccac; stroke:none;" d="M132 970L133 971L132 970z"/>
<path style="fill:#e3ffee; stroke:none;" d="M537 970L540 976C542.112 972.57 540.797 970.661 537 970z"/>
<path style="fill:#198a56; stroke:none;" d="M776 970L778 974C778.683 972.494 778.826 971.685 779 970L776 970z"/>
<path style="fill:#82dfb3; stroke:none;" d="M844 970L845 971L844 970z"/>
<path style="fill:#62aa84; stroke:none;" d="M1194 970L1195 971L1194 970z"/>
<path style="fill:#edfffb; stroke:none;" d="M1195 970C1196.46 972.486 1197.78 974.173 1200 976L1200 970L1195 970z"/>
<path style="fill:#76c19a; stroke:none;" d="M537 971L538 972L537 971z"/>
<path style="fill:#68be97; stroke:none;" d="M776 971L777 972L776 971z"/>
<path style="fill:#78c39c; stroke:none;" d="M1013 971L1014 972L1013 971z"/>
<path style="fill:#91b29d; stroke:none;" d="M1195 971L1196 972L1195 971z"/>
<path style="fill:#9cecc7; stroke:none;" d="M845 972L846 973L845 972z"/>
<path style="fill:#2c8c5c; stroke:none;" d="M1013 972L1012 976C1013.66 974.596 1013.75 974.046 1013 972z"/>
<path style="fill:#82d9af; stroke:none;" d="M1196 972L1197 973L1196 972z"/>
<path style="fill:#7bc29a; stroke:none;" d="M133 973L134 974L133 973z"/>
<path style="fill:#51a780; stroke:none;" d="M777 973L778 974L777 973z"/>
<path style="fill:#2c8359; stroke:none;" d="M1195.67 973.333C1195.22 973.778 1196.28 973.722 1196.33 973.667C1196.78 973.222 1195.72 973.278 1195.67 973.333z"/>
<path style="fill:#3da16b; stroke:none;" d="M134.333 974.667C134.278 974.722 134.222 975.778 134.667 975.333C134.722 975.278 134.778 974.222 134.333 974.667z"/>
<path style="fill:#8ee4bd; stroke:none;" d="M539 974L540 975L539 974z"/>
<path style="fill:#94dfbe; stroke:none;" d="M777 974L778 975L777 974z"/>
<path style="fill:#b4dcc2; stroke:none;" d="M846 974L847 975L846 974z"/>
<path style="fill:#96d3b2; stroke:none;" d="M1012 974L1013 975L1012 974z"/>
<path style="fill:#80a88e; stroke:none;" d="M846 975L847 976L846 975z"/>
<path style="fill:#bfe9d1; stroke:none;" d="M134.333 976.667C134.278 976.722 134.222 977.778 134.667 977.333C134.722 977.278 134.778 976.222 134.333 976.667z"/>
<path style="fill:#71c39b; stroke:none;" d="M540 976L541 977L540 976z"/>
<path style="fill:#e9fff8; stroke:none;" d="M778 976C778.261 982.053 779.691 986.914 783 992C784.719 987.042 780.781 980.139 778 976z"/>
<path style="fill:#22935b; stroke:none;" d="M779 976L783 987C784.426 983.311 782.638 979.771 782 976L779 976z"/>
<path style="fill:#e2fff4; stroke:none;" d="M1200 976C1203.22 981.155 1208.73 989.968 1215 991C1211.61 986.306 1205.76 976.952 1200 976z"/>
<path style="fill:#afdfc7; stroke:none;" d="M541 977L542 978L541 977z"/>
<path style="fill:#67d89a; stroke:none;" d="M847 977L848 978L847 977z"/>
<path style="fill:#99efca; stroke:none;" d="M1011 977L1012 978L1011 977z"/>
<path style="fill:#8d998d; stroke:none;" d="M1200 977L1201 978L1200 977z"/>
<path style="fill:#65ab89; stroke:none;" d="M541 978L542 979L541 978z"/>
<path style="fill:#8ccdad; stroke:none;" d="M779 978L780 979L779 978z"/>
<path style="fill:#149552; stroke:none;" d="M1011 978C1010.44 982.139 1009.27 986.029 1008 990C1012.3 987.49 1012.86 982.427 1011 978z"/>
<path style="fill:#8cc9aa; stroke:none;" d="M1201 978L1202 979L1201 978z"/>
<path style="fill:#8bd1ad; stroke:none;" d="M135 979L136 980L135 979z"/>
<path style="fill:#a1d1b9; stroke:none;" d="M542 979L543 980L542 979z"/>
<path style="fill:#a1d2bf; stroke:none;" d="M848 979L849 980L848 979z"/>
<path style="fill:#a3dec0; stroke:none;" d="M1202 979L1203 980L1202 979z"/>
<path style="fill:#e0fffa; stroke:none;" d="M135 980C135.15 987.01 138.81 993.023 139 1000L143 1001C141.425 994.355 138.973 985.546 135 980z"/>
<path style="fill:#edfdf3; stroke:none;" d="M543 980C546.352 986.298 550.025 992.847 555 998C555.402 996.057 555.402 995.943 555 994C557.323 993.972 557.597 993.844 559 992C556.217 991.909 553.69 991.851 551.638 989.682C548.063 985.903 548.835 980.971 543 980z"/>
<path style="fill:#73c19a; stroke:none;" d="M780 980L781 981L780 980z"/>
<path style="fill:#238959; stroke:none;" d="M848 980L849 986C849.736 983.567 849.305 982.181 848 980z"/>
<path style="fill:#e1fff3; stroke:none;" d="M849 980L853 992C854.314 988.211 852.205 984 852 980L849 980z"/>
<path style="fill:#80c3a4; stroke:none;" d="M543 981L544 982L543 981z"/>
<path style="fill:#93d2b5; stroke:none;" d="M1010 981L1011 982L1010 981z"/>
<path style="fill:#1e9156; stroke:none;" d="M1202 981C1203.29 983.747 1204.12 985.027 1207 986C1205.74 983.434 1204.55 982.273 1202 981z"/>
<path style="fill:#a9e0c1; stroke:none;" d="M544 982L545 983L544 982z"/>
<path style="fill:#6ebc94; stroke:none;" d="M781 982L782 983L781 982z"/>
<path style="fill:#75b897; stroke:none;" d="M849 982L850 983L849 982z"/>
<path style="fill:#659c7d; stroke:none;" d="M544 983L545 984L544 983z"/>
<path style="fill:#64a17f; stroke:none;" d="M1205 983L1206 984L1205 983z"/>
<path style="fill:#97ddbb; stroke:none;" d="M137.333 984.667C137.278 984.722 137.222 985.778 137.667 985.333C137.722 985.278 137.778 984.222 137.333 984.667z"/>
<path style="fill:#189657; stroke:none;" d="M544 984C544.55 987.762 545.704 990.035 549 992C548.52 994.36 548.481 995.663 549 998L552 996L552 994C549.029 990.86 547.219 986.953 544 984z"/>
<path style="fill:#97d7b4; stroke:none;" d="M545 984L546 985L545 984z"/>
<path style="fill:#14914f; stroke:none;" d="M784 984C784.199 988.352 785.445 991.962 787 996C789.661 990.986 788.722 987.072 784 984z"/>
<path style="fill:#4ca275; stroke:none;" d="M850.333 984.667C850.278 984.722 850.222 985.778 850.667 985.333C850.722 985.278 850.778 984.222 850.333 984.667z"/>
<path style="fill:#98d0b3; stroke:none;" d="M782 985L783 986L782 985z"/>
<path style="fill:#68b38a; stroke:none;" d="M1009 985L1010 986L1009 985z"/>
<path style="fill:#81c5a0; stroke:none;" d="M1207 985L1208 986L1207 985z"/>
<path style="fill:#58b586; stroke:none;" d="M546 986L547 987L546 986z"/>
<path style="fill:#208450; stroke:none;" d="M850 986C850.013 989.194 849.597 991.271 853 992C852.291 989.588 851.573 987.949 850 986z"/>
<path style="fill:#8ad2aa; stroke:none;" d="M1208 986L1209 987L1208 986z"/>
<path style="fill:#7dc7a2; stroke:none;" d="M138 987L139 988L138 987z"/>
<path style="fill:#93dbb6; stroke:none;" d="M547 987L548 988L547 987z"/>
<path style="fill:#88bea7; stroke:none;" d="M783 987L784 988L783 987z"/>
<path style="fill:#5fad83; stroke:none;" d="M851 987L852 988L851 987z"/>
<path style="fill:#6ac991; stroke:none;" d="M1008 988L1009 989L1008 988z"/>
<path style="fill:#a0ecc6; stroke:none;" d="M1210 988L1211 989L1210 988z"/>
<path style="fill:#68af8d; stroke:none;" d="M548 989L549 990L548 989z"/>
<path style="fill:#48c080; stroke:none;" d="M784 989L785 990L784 989z"/>
<path style="fill:#88c3a3; stroke:none;" d="M852 989L853 990L852 989z"/>
<path style="fill:#5da983; stroke:none;" d="M1210 989L1211 990L1210 989z"/>
<path style="fill:#8dd3af; stroke:none;" d="M139 990L140 991L139 990z"/>
<path style="fill:#b3ffe4; stroke:none;" d="M784.333 990.667C784.277 990.723 784.222 991.778 784.667 991.333C784.722 991.278 784.778 990.222 784.333 990.667z"/>
<path style="fill:#a6bdb5; stroke:none;" d="M1007.33 990.667C1007.28 990.722 1007.22 991.778 1007.67 991.333C1007.72 991.278 1007.78 990.222 1007.33 990.667z"/>
<path style="fill:#50b687; stroke:none;" d="M1211 990L1212 991L1211 990z"/>
<path style="fill:#bcfff0; stroke:none;" d="M1006 991L1006 994C1006.7 992.446 1006.7 992.554 1006 991z"/>
<path style="fill:#1a8c59; stroke:none;" d="M1211 991C1211.68 994.746 1212.76 996.947 1216 999C1215.41 995.177 1214.06 993.331 1211 991z"/>
<path style="fill:#79bc9d; stroke:none;" d="M1212 991L1213 992L1212 991z"/>
<path style="fill:#65b089; stroke:none;" d="M140 992L141 993L140 992z"/>
<path style="fill:#469b71; stroke:none;" d="M550.333 992.667C550.278 992.722 550.222 993.778 550.667 993.333C550.722 993.278 550.778 992.222 550.333 992.667z"/>
<path style="fill:#caf1de; stroke:none;" d="M785.333 992.667C785.278 992.722 785.222 993.778 785.667 993.333C785.722 993.278 785.778 992.222 785.333 992.667z"/>
<path style="fill:#33a571; stroke:none;" d="M853.333 992.667C853.277 992.723 853.222 993.778 853.667 993.333C853.722 993.278 853.778 992.222 853.333 992.667z"/>
<path style="fill:#e1ffee; stroke:none;" d="M854 992C855.095 996.734 856.405 1001.88 859 1006C859.403 1001.34 857.764 994.997 854 992z"/>
<path style="fill:#72d2ac; stroke:none;" d="M1213 992L1214 993L1213 992z"/>
<path style="fill:#c4fff7; stroke:none;" d="M1214 992C1214.55 993.635 1214.36 993.455 1216 994L1216 992L1214 992z"/>
<path style="fill:#90dbb4; stroke:none;" d="M140 993L141 994L140 993z"/>
<path style="fill:#aad8be; stroke:none;" d="M551 993L552 994L551 993z"/>
<path style="fill:#7bdcb3; stroke:none;" d="M1214 993L1215 994L1214 993z"/>
<path style="fill:#75c09f; stroke:none;" d="M786 994L787 995L786 994z"/>
<path style="fill:#88cfad; stroke:none;" d="M854 994L855 995L854 994z"/>
<path style="fill:#3ba978; stroke:none;" d="M1006.33 994.667C1006.28 994.723 1006.22 995.778 1006.67 995.333C1006.72 995.278 1006.78 994.222 1006.33 994.667z"/>
<path style="fill:#8debc6; stroke:none;" d="M1215 994L1216 995L1215 994z"/>
<path style="fill:#70ce9c; stroke:none;" d="M141 995L142 996L141 995z"/>
<path style="fill:#7bbe9d; stroke:none;" d="M552 995L553 996L552 995z"/>
<path style="fill:#edfffb; stroke:none;" d="M786 995C786.251 999.839 788.168 1003.59 790 1008C791.284 1003.65 788.429 998.616 786 995z"/>
<path style="fill:#1d8c57; stroke:none;" d="M854 995C854.167 998.252 854.886 1000.95 856 1004C856.961 1000.75 855.773 997.822 854 995z"/>
<path style="fill:#9bf0c9; stroke:none;" d="M553 996L554 997L553 996z"/>
<path style="fill:#52a780; stroke:none;" d="M787 996L788 997L787 996z"/>
<path style="fill:#45b082; stroke:none;" d="M1216 996L1217 997L1216 996z"/>
<path style="fill:#4ea37c; stroke:none;" d="M553 997L554 998L553 997z"/>
<path style="fill:#98edc6; stroke:none;" d="M787 997L788 998L787 997z"/>
<path style="fill:#91c6aa; stroke:none;" d="M855 997L856 998L855 997z"/>
<path style="fill:#8cc8a6; stroke:none;" d="M1005 997L1006 998L1005 997z"/>
<path style="fill:#6abb9a; stroke:none;" d="M1217 997L1218 998L1217 997z"/>
<path style="fill:#95c7ac; stroke:none;" d="M142 998L143 999L142 998z"/>
<path style="fill:#74c29a; stroke:none;" d="M554 998L555 999L554 998z"/>
<path style="fill:#11944e; stroke:none;" d="M788 998C791.299 1006.55 794.946 1015.04 797 1024C798.355 1020.22 796.945 1015.84 796 1012C798.782 1009.57 798 1005.51 798 1002L792 1004C791.373 1000.92 790.719 999.573 788 998z"/>
<path style="fill:#63bb91; stroke:none;" d="M1218 998L1219 999L1218 998z"/>
<path style="fill:#b0d8c0; stroke:none;" d="M555 999L556 1000L555 999z"/>
<path style="fill:#72caa0; stroke:none;" d="M788 999L789 1000L788 999z"/>
<path style="fill:#8ac5a5; stroke:none;" d="M1219 999L1220 1000L1219 999z"/>
<path style="fill:#8db2a0; stroke:none;" d="M143 1000L144 1001L143 1000z"/>
<path style="fill:#22955a; stroke:none;" d="M144 1000C144.599 1003.48 145.872 1006.66 147 1010C147.774 1009.23 149.066 1004.13 148 1002C148.369 1002.74 144.64 1000.78 145 1001L146 1000L144 1000z"/>
<path style="fill:#7fd2a8; stroke:none;" d="M856 1000L857 1001L856 1000z"/>
<path style="fill:#d8ffee; stroke:none;" d="M1003 1000L1002 1006C1003.31 1003.82 1003.74 1002.43 1003 1000z"/>
<path style="fill:#49a979; stroke:none;" d="M1004.33 1000.67C1004.28 1000.72 1004.22 1001.78 1004.67 1001.33C1004.72 1001.28 1004.78 1000.22 1004.33 1000.67z"/>
<path style="fill:#80e4b2; stroke:none;" d="M1220 1000L1221 1001L1220 1000z"/>
<path style="fill:#b8ddcb; stroke:none;" d="M143 1001L144 1002L143 1001z"/>
<path style="fill:#75bb97; stroke:none;" d="M556 1001L557 1002L556 1001z"/>
<path style="fill:#5db58b; stroke:none;" d="M789 1001L790 1002L789 1001z"/>
<path style="fill:#a2e3c1; stroke:none;" d="M1221 1001L1222 1002L1221 1001z"/>
<path style="fill:#119355; stroke:none;" d="M556 1002L555 1007C556.8 1007.56 558.122 1007.82 560 1008C558.995 1005.41 558.02 1003.9 556 1002z"/>
<path style="fill:#93e8bf; stroke:none;" d="M557 1002L558 1003L557 1002z"/>
<path style="fill:#a8efc7; stroke:none;" d="M1222 1002L1223 1003L1222 1002z"/>
<path style="fill:#80d9a9; stroke:none;" d="M144 1003L145 1004L144 1003z"/>
<path style="fill:#8ad5ae; stroke:none;" d="M857 1003L858 1004L857 1003z"/>
<path style="fill:#69be95; stroke:none;" d="M1003 1003L1004 1004L1003 1003z"/>
<path style="fill:#54c291; stroke:none;" d="M558 1004L559 1005L558 1004z"/>
<path style="fill:#7bcba8; stroke:none;" d="M790 1004L791 1005L790 1004z"/>
<path style="fill:#1f8f57; stroke:none;" d="M1003 1004C1000.54 1014.4 994.922 1023.78 992 1034L997 1031C996.931 1022.34 1006.36 1012.32 1003 1004z"/>
<path style="fill:#58b586; stroke:none;" d="M1223 1004L1224 1005L1223 1004z"/>
<path style="fill:#5fb888; stroke:none;" d="M145 1005L146 1006L145 1005z"/>
<path style="fill:#7ec9a8; stroke:none;" d="M559 1005L560 1006L559 1005z"/>
<path style="fill:#73b792; stroke:none;" d="M1224 1005L1225 1006L1224 1005z"/>
<path style="fill:#c3ddd4; stroke:none;" d="M560 1006L561 1007L560 1006z"/>
<path style="fill:#96c8ab; stroke:none;" d="M858 1006L859 1007L858 1006z"/>
<path style="fill:#72be9a; stroke:none;" d="M1002 1006L1003 1007L1002 1006z"/>
<path style="fill:#7ecaa4; stroke:none;" d="M1225 1006L1226 1007L1225 1006z"/>
<path style="fill:#708a81; stroke:none;" d="M560 1007L561 1008L560 1007z"/>
<path style="fill:#67d3a2; stroke:none;" d="M791 1007L792 1008L791 1007z"/>
<path style="fill:#b2cfbb; stroke:none;" d="M1226 1007L1227 1008L1226 1007z"/>
<path style="fill:#90cab1; stroke:none;" d="M146 1008L147 1009L146 1008z"/>
<path style="fill:#baffe1; stroke:none;" d="M561 1008C562.103 1009.46 562.543 1009.9 564 1011C563.101 1008.88 563.124 1008.9 561 1008z"/>
<path style="fill:#d9fff5; stroke:none;" d="M791 1008C791.27 1010.69 791.818 1012.57 793 1015C793.589 1012.17 792.707 1010.29 791 1008z"/>
<path style="fill:#cbffe9; stroke:none;" d="M1000 1008L1000 1011C1001.02 1009.86 1001.31 1009.39 1002 1008L1000 1008z"/>
<path style="fill:#e4fff2; stroke:none;" d="M1235 1012L1231 1012C1233.77 1016.44 1236.92 1020.7 1241 1024C1240.24 1018.76 1236.3 1015.58 1236 1010L1240 1010L1240 1008C1236.93 1008.5 1235.97 1009.04 1235 1012z"/>
<path style="fill:#55b188; stroke:none;" d="M792 1009L793 1010L792 1009z"/>
<path style="fill:#85d5b0; stroke:none;" d="M859 1009L860 1010L859 1009z"/>
<path style="fill:#62bd91; stroke:none;" d="M1001 1009L1002 1010L1001 1009z"/>
<path style="fill:#64b690; stroke:none;" d="M147 1010L148 1011L147 1010z"/>
<path style="fill:#d8fff2; stroke:none;" d="M564 1010C564.931 1012.74 565.819 1014.1 568 1016C567.069 1013.26 566.181 1011.9 564 1010z"/>
<path style="fill:#1f8e59; stroke:none;" d="M859 1010C859.021 1012.85 859.19 1015.27 860 1018C861.038 1015.1 860.433 1012.7 859 1010z"/>
<path style="fill:#168b57; stroke:none;" d="M1228 1010C1228.93 1013.24 1229.99 1014.5 1233 1016C1231.49 1013.74 1229.97 1011.87 1228 1010z"/>
<path style="fill:#a1f3cd; stroke:none;" d="M147 1011L148 1012L147 1011z"/>
<path style="fill:#6bb292; stroke:none;" d="M563 1011L564 1012L563 1011z"/>
<path style="fill:#91d4b3; stroke:none;" d="M1000 1011L1001 1012L1000 1011z"/>
<path style="fill:#71ac8e; stroke:none;" d="M1229 1011L1230 1012L1229 1011z"/>
<path style="fill:#4ca177; stroke:none;" d="M148 1012L149 1013L148 1012z"/>
<path style="fill:#75d2a5; stroke:none;" d="M564 1012L565 1013L564 1012z"/>
<path style="fill:#6aba93; stroke:none;" d="M793 1012L794 1013L793 1012z"/>
<path style="fill:#a7d9be; stroke:none;" d="M860 1012L861 1013L860 1012z"/>
<path style="fill:#e8fff2; stroke:none;" d="M861 1012L863 1023C864.706 1018.93 864.135 1015.07 861 1012z"/>
<path style="fill:#60bb90; stroke:none;" d="M1230 1012L1231 1013L1230 1012z"/>
<path style="fill:#8adfb5; stroke:none;" d="M148 1013L149 1014L148 1013z"/>
<path style="fill:#2f9b67; stroke:none;" d="M563 1013C564.223 1014.2 564.42 1014.32 566 1015C564.777 1013.8 564.58 1013.68 563 1013z"/>
<path style="fill:#75a78c; stroke:none;" d="M860 1013L861 1014L860 1013z"/>
<path style="fill:#7bc0a1; stroke:none;" d="M1231 1013L1232 1014L1231 1013z"/>
<path style="fill:#eefdf6; stroke:none;" d="M568 1014C568.889 1018.39 570.647 1020.75 575 1022C573.326 1018.79 570.552 1016.58 568 1014z"/>
<path style="fill:#6abb92; stroke:none;" d="M794.333 1014.67C794.278 1014.72 794.222 1015.78 794.667 1015.33C794.722 1015.28 794.778 1014.22 794.333 1014.67z"/>
<path style="fill:#d5ffef; stroke:none;" d="M998 1014C996.359 1018 994.645 1021.7 994 1026C996.585 1022.8 999.14 1018.11 998 1014z"/>
<path style="fill:#74bf98; stroke:none;" d="M999 1014L1000 1015L999 1014z"/>
<path style="fill:#8be6ba; stroke:none;" d="M1232 1014L1233 1015L1232 1014z"/>
<path style="fill:#6ec399; stroke:none;" d="M149 1015L150 1016L149 1015z"/>
<path style="fill:#32a56a; stroke:none;" d="M566.333 1015.67C566.278 1015.72 566.222 1016.78 566.667 1016.33C566.722 1016.28 566.778 1015.22 566.333 1015.67z"/>
<path style="fill:#9edfbf; stroke:none;" d="M1233 1015L1234 1016L1233 1015z"/>
<path style="fill:#70c196; stroke:none;" d="M567 1016L568 1017L567 1016z"/>
<path style="fill:#c8fadd; stroke:none;" d="M794.333 1016.67C794.278 1016.72 794.222 1017.78 794.667 1017.33C794.722 1017.28 794.778 1016.22 794.333 1016.67z"/>
<path style="fill:#489871; stroke:none;" d="M861.333 1016.67C861.278 1016.72 861.222 1017.78 861.667 1017.33C861.722 1017.28 861.778 1016.22 861.333 1016.67z"/>
<path style="fill:#159353; stroke:none;" d="M1233 1016C1233.9 1020.37 1235.88 1022.37 1240 1024C1237.94 1021 1235.75 1018.38 1233 1016z"/>
<path style="fill:#53bb8a; stroke:none;" d="M150 1017L151 1018L150 1017z"/>
<path style="fill:#179355; stroke:none;" d="M567 1017C566.455 1018.64 566.635 1018.45 565 1019C568.705 1022.22 570.483 1025.32 572 1030L577 1030C574.56 1025.1 571.251 1020.46 567 1017z"/>
<path style="fill:#aee0c3; stroke:none;" d="M568 1017L569 1018L568 1017z"/>
<path style="fill:#67b58d; stroke:none;" d="M998 1017L999 1018L998 1017z"/>
<path style="fill:#15955a; stroke:none;" d="M151 1018C151.547 1019.88 152.11 1021.26 153 1023C153.873 1020.39 153.276 1019.45 151 1018z"/>
<path style="fill:#92cbab; stroke:none;" d="M795 1018L796 1019L795 1018z"/>
<path style="fill:#56af7f; stroke:none;" d="M1235 1018L1236 1019L1235 1018z"/>
<path style="fill:#8ddab0; stroke:none;" d="M862 1019L863 1020L862 1019z"/>
<path style="fill:#83d3ac; stroke:none;" d="M997 1019L998 1020L997 1019z"/>
<path style="fill:#7abc96; stroke:none;" d="M1236 1019L1237 1020L1236 1019z"/>
<path style="fill:#91c3a8; stroke:none;" d="M151 1020L152 1021L151 1020z"/>
<path style="fill:#6fd5a3; stroke:none;" d="M570 1020L571 1021L570 1020z"/>
<path style="fill:#328b5b; stroke:none;" d="M862 1020L862 1024C862.71 1022.24 862.71 1021.76 862 1020z"/>
<path style="fill:#76d1a5; stroke:none;" d="M1237 1020L1238 1021L1237 1020z"/>
<path style="fill:#a3ebc6; stroke:none;" d="M571 1021L572 1022L571 1021z"/>
<path style="fill:#74c099; stroke:none;" d="M796 1021L797 1022L796 1021z"/>
<path style="fill:#78b393; stroke:none;" d="M152 1022L153 1023L152 1022z"/>
<path style="fill:#d3e9dc; stroke:none;" d="M572 1022C572.545 1023.64 572.365 1023.45 574 1024L574 1022L572 1022z"/>
<path style="fill:#d9ffed; stroke:none;" d="M796 1022C796.097 1026.14 796.372 1029.59 800 1032C798.882 1028.56 797.925 1025.06 796 1022z"/>
<path style="fill:#6bbd97; stroke:none;" d="M996 1022L997 1023L996 1022z"/>
<path style="fill:#4f9a79; stroke:none;" d="M153.333 1023.67C153.278 1023.72 153.222 1024.78 153.667 1024.33C153.722 1024.28 153.778 1023.22 153.333 1023.67z"/>
<path style="fill:#95d1ad; stroke:none;" d="M863 1023L864 1024L863 1023z"/>
<path style="fill:#5cc48f; stroke:none;" d="M573 1024L574 1025L573 1024z"/>
<path style="fill:#c3ffea; stroke:none;" d="M574 1024L575 1027C575.685 1025.75 575.749 1025.45 576 1024L574 1024z"/>
<path style="fill:#7dc4a2; stroke:none;" d="M797 1024L798 1025L797 1024z"/>
<path style="fill:#258f61; stroke:none;" d="M798 1024L799 1030C799.874 1027.95 799.953 1026.25 800 1024L798 1024z"/>
<path style="fill:#82d2ab; stroke:none;" d="M995 1024L996 1025L995 1024z"/>
<path style="fill:#e2ffee; stroke:none;" d="M1241 1024C1242.45 1025.61 1243.05 1026.06 1245 1027C1243.7 1025.08 1243.21 1024.68 1241 1024z"/>
<path style="fill:#95e0bf; stroke:none;" d="M153 1025L154 1026L153 1025z"/>
<path style="fill:#70d7a2; stroke:none;" d="M574 1025L575 1026L574 1025z"/>
<path style="fill:#e2fff6; stroke:none;" d="M993 1026L992 1030C993.303 1028.33 993.553 1028.01 993 1026z"/>
<path style="fill:#a5e2c3; stroke:none;" d="M994 1026L995 1027L994 1026z"/>
<path style="fill:#67b88d; stroke:none;" d="M1242 1026L1243 1027L1242 1026z"/>
<path style="fill:#71d8a9; stroke:none;" d="M154 1027L155 1028L154 1027z"/>
<path style="fill:#6fbb97; stroke:none;" d="M798 1027L799 1028L798 1027z"/>
<path style="fill:#8bbda4; stroke:none;" d="M864 1027L865 1028L864 1027z"/>
<path style="fill:#69a687; stroke:none;" d="M994 1027L995 1028L994 1027z"/>
<path style="fill:#87be9f; stroke:none;" d="M1243 1027L1244 1028L1243 1027z"/>
<path style="fill:#53c298; stroke:none;" d="M576 1028L577 1029L576 1028z"/>
<path style="fill:#2a895d; stroke:none;" d="M864 1028L864 1032C864.71 1030.24 864.71 1029.76 864 1028z"/>
<path style="fill:#319066; stroke:none;" d="M1243 1028C1243.9 1030.12 1243.88 1030.1 1246 1031C1244.9 1029.54 1244.46 1029.1 1243 1028z"/>
<path style="fill:#98debc; stroke:none;" d="M1244 1028L1245 1029L1244 1028z"/>
<path style="fill:#dbfff5; stroke:none;" d="M1245 1028C1245.94 1029.95 1246.39 1030.55 1248 1032C1247.22 1029.87 1246.87 1029.23 1245 1028z"/>
<path style="fill:#73d0a3; stroke:none;" d="M155 1029L156 1030L155 1029z"/>
<path style="fill:#6ac8a4; stroke:none;" d="M577 1029L578 1030L577 1029z"/>
<path style="fill:#8ad2ad; stroke:none;" d="M993 1029L994 1030L993 1029z"/>
<path style="fill:#79d1a9; stroke:none;" d="M578 1030L579 1031L578 1030z"/>
<path style="fill:#72a68f; stroke:none;" d="M799 1030L800 1031L799 1030z"/>
<path style="fill:#96d2b6; stroke:none;" d="M865.333 1030.67C865.278 1030.72 865.222 1031.78 865.667 1031.33C865.722 1031.28 865.778 1030.22 865.333 1030.67z"/>
<path style="fill:#aefad4; stroke:none;" d="M992.333 1030.67C992.277 1030.72 992.222 1031.78 992.667 1031.33C992.722 1031.28 992.778 1030.22 992.333 1030.67z"/>
<path style="fill:#5fb88c; stroke:none;" d="M156 1031L157 1032L156 1031z"/>
<path style="fill:#459d75; stroke:none;" d="M578 1031L579 1032L578 1031z"/>
<path style="fill:#b8e6cc; stroke:none;" d="M579 1031L580 1032L579 1031z"/>
<path style="fill:#c5fcdf; stroke:none;" d="M156.333 1032.67C156.278 1032.72 156.222 1033.78 156.667 1033.33C156.722 1033.28 156.778 1032.22 156.333 1032.67z"/>
<path style="fill:#a8f0ca; stroke:none;" d="M580 1032L581 1033L580 1032z"/>
<path style="fill:#fffcff; stroke:none;" d="M868 1032C868 1046.64 872.221 1060.75 874.741 1075.09C875.806 1081.15 874.931 1087.86 875.004 1094C875.095 1101.69 876.088 1109.26 875.996 1117C875.696 1142.23 867 1166.85 867 1192L871 1190L870 1184L874 1184L874 1188C877.986 1186.4 883.127 1184.79 883 1180C885.384 1179.71 885.387 1180.09 885 1178C889.478 1176.19 895.675 1172.52 899 1169C893.06 1167.45 886.156 1168 880 1168C880 1158.75 880.605 1149.27 879.946 1140.04C879.65 1135.89 878.008 1132.27 878.002 1128C877.983 1116.32 880.421 1104.79 879.982 1093.01C879.672 1084.72 877.097 1076.2 876.129 1067.96C875.459 1062.25 877.264 1056.63 876.699 1051C876.037 1044.41 872.602 1038.41 871 1032L868 1032z"/>
<path style="fill:#ddfff3; stroke:none;" d="M1248 1032C1249.9 1035.26 1251.6 1037.34 1255 1039C1254.08 1037.26 1253.4 1035.9 1253 1034L1251 1034L1252 1032L1248 1032z"/>
<path style="fill:#54b184; stroke:none;" d="M157 1033L158 1034L157 1033z"/>
<path style="fill:#48b37d; stroke:none;" d="M800 1033L801 1034L800 1033z"/>
<path style="fill:#93b79d; stroke:none;" d="M1248 1033L1249 1034L1248 1033z"/>
<path style="fill:#5aba8a; stroke:none;" d="M581 1034L582 1035L581 1034z"/>
<path style="fill:#d4ffe8; stroke:none;" d="M800 1034C800.077 1036.65 799.997 1038.27 802 1040C801.532 1037.8 801.063 1035.97 800 1034z"/>
<path style="fill:#19905a; stroke:none;" d="M801 1034C802.095 1041.45 804.994 1048.54 806 1056C808.802 1049.31 804.006 1041.12 804 1034L801 1034z"/>
<path style="fill:#95ddb5; stroke:none;" d="M866.333 1034.67C866.278 1034.72 866.222 1035.78 866.667 1035.33C866.722 1035.28 866.778 1034.22 866.333 1034.67z"/>
<path style="fill:#f1fffd; stroke:none;" d="M989 1034C987.785 1037.47 986.542 1038.95 983 1040L985 1048C987.81 1043.82 989.674 1038.85 991 1034L989 1034z"/>
<path style="fill:#81c2aa; stroke:none;" d="M991 1034L992 1035L991 1034z"/>
<path style="fill:#87cea6; stroke:none;" d="M1249 1034L1250 1035L1249 1034z"/>
<path style="fill:#84ba9a; stroke:none;" d="M582 1035L583 1036L582 1035z"/>
<path style="fill:#288a63; stroke:none;" d="M991 1035L990 1040C991.879 1038.38 991.916 1037.3 991 1035z"/>
<path style="fill:#9dcfb4; stroke:none;" d="M158 1036L159 1037L158 1036z"/>
<path style="fill:#7bd6aa; stroke:none;" d="M583 1036L584 1037L583 1036z"/>
<path style="fill:#479d6e; stroke:none;" d="M866.333 1036.67C866.278 1036.72 866.222 1037.78 866.667 1037.33C866.722 1037.28 866.778 1036.22 866.333 1036.67z"/>
<path style="fill:#d4ffe4; stroke:none;" d="M867 1036L867 1040C867.71 1038.24 867.71 1037.76 867 1036z"/>
<path style="fill:#87d0b3; stroke:none;" d="M990 1036L991 1037L990 1036z"/>
<path style="fill:#449c74; stroke:none;" d="M159 1037L160 1038L159 1037z"/>
<path style="fill:#a4dfbf; stroke:none;" d="M584 1037L585 1038L584 1037z"/>
<path style="fill:#65bc8f; stroke:none;" d="M801 1037L802 1038L801 1037z"/>
<path style="fill:#95d2b1; stroke:none;" d="M159 1038L160 1039L159 1038z"/>
<path style="fill:#42a879; stroke:none;" d="M584 1038L585 1039L584 1038z"/>
<path style="fill:#a4dac0; stroke:none;" d="M585 1038L586 1039L585 1038z"/>
<path style="fill:#1f8c53; stroke:none;" d="M866 1038C866.015 1043.33 865.563 1052.41 870 1056C869.258 1050.26 868.725 1043.12 866 1038z"/>
<path style="fill:#98e1c4; stroke:none;" d="M989 1038L990 1039L989 1038z"/>
<path style="fill:#72a88e; stroke:none;" d="M585 1039L586 1040L585 1039z"/>
<path style="fill:#59a285; stroke:none;" d="M989 1039L990 1040L989 1039z"/>
<path style="fill:#75a28e; stroke:none;" d="M1253 1039L1254 1040L1253 1039z"/>
<path style="fill:#a0c7aa; stroke:none;" d="M160 1040L161 1041L160 1040z"/>
<path style="fill:#2d8f5c; stroke:none;" d="M161 1040L163 1046C163.744 1043.27 162.915 1042.03 161 1040z"/>
<path style="fill:#3dbe7d; stroke:none;" d="M586 1040L587 1041L586 1040z"/>
<path style="fill:#7ac6a2; stroke:none;" d="M802.333 1040.67C802.278 1040.72 802.222 1041.78 802.667 1041.33C802.722 1041.28 802.778 1040.22 802.333 1040.67z"/>
<path style="fill:#72c096; stroke:none;" d="M867.333 1040.67C867.278 1040.72 867.222 1041.78 867.667 1041.33C867.722 1041.28 867.778 1040.22 867.333 1040.67z"/>
<path style="fill:#267e4e; stroke:none;" d="M989 1040L988 1044C989.656 1042.6 989.751 1042.05 989 1040z"/>
<path style="fill:#36ad6d; stroke:none;" d="M1254.33 1040.67C1254.28 1040.72 1254.22 1041.78 1254.67 1041.33C1254.72 1041.28 1254.78 1040.22 1254.33 1040.67z"/>
<path style="fill:#ffeff5; stroke:none;" d="M1255 1040L1257 1044L1260 1044L1261 1041C1258.89 1040.36 1257.2 1040.09 1255 1040z"/>
<path style="fill:#e5fff6; stroke:none;" d="M160 1041C160.36 1046.6 163.645 1050.69 165 1056C166.769 1051.01 163.172 1044.83 160 1041z"/>
<path style="fill:#59b282; stroke:none;" d="M587 1041L588 1042L587 1041z"/>
<path style="fill:#76b48d; stroke:none;" d="M988 1041L989 1042L988 1041z"/>
<path style="fill:#81cd9f; stroke:none;" d="M1255 1041L1256 1042L1255 1041z"/>
<path style="fill:#69a782; stroke:none;" d="M161 1042L162 1043L161 1042z"/>
<path style="fill:#7acba2; stroke:none;" d="M588 1042L589 1043L588 1042z"/>
<path style="fill:#dbfffa; stroke:none;" d="M802 1042C802.052 1044.53 802.278 1046.58 803 1049C803.806 1046.36 803.289 1044.43 802 1042z"/>
<path style="fill:#188b50; stroke:none;" d="M1255 1042C1255.21 1044.74 1255.3 1045.42 1258 1046C1257.06 1044.05 1256.61 1043.45 1255 1042z"/>
<path style="fill:#9bdcb4; stroke:none;" d="M1256 1042L1257 1043L1256 1042z"/>
<path style="fill:#93c5aa; stroke:none;" d="M589 1043L590 1044L589 1043z"/>
<path style="fill:#8bc6a6; stroke:none;" d="M987 1043L988 1044L987 1043z"/>
<path style="fill:#69b18c; stroke:none;" d="M162 1044L163 1045L162 1044z"/>
<path style="fill:#8eedc1; stroke:none;" d="M590 1044L591 1045L590 1044z"/>
<path style="fill:#ddfff8; stroke:none;" d="M1258 1044L1259 1046C1260.64 1045.45 1260.45 1045.64 1261 1044L1258 1044z"/>
<path style="fill:#a2dec2; stroke:none;" d="M591 1045L592 1046L591 1045z"/>
<path style="fill:#73cea5; stroke:none;" d="M803 1045L804 1046L803 1045z"/>
<path style="fill:#aae2c5; stroke:none;" d="M868 1045L869 1046L868 1045z"/>
<path style="fill:#8fceaf; stroke:none;" d="M986 1045L987 1046L986 1045z"/>
<path style="fill:#63ad88; stroke:none;" d="M1258 1045L1259 1046L1258 1045z"/>
<path style="fill:#65ab87; stroke:none;" d="M163 1046L164 1047L163 1046z"/>
<path style="fill:#eefff4; stroke:none;" d="M592 1046C594.091 1048.87 595.503 1050.41 599 1051C596.989 1048.06 595.574 1046.58 592 1046z"/>
<path style="fill:#559875; stroke:none;" d="M868.333 1046.67C868.277 1046.72 868.222 1047.78 868.667 1047.33C868.722 1047.28 868.778 1046.22 868.333 1046.67z"/>
<path style="fill:#54ba8b; stroke:none;" d="M1259 1046L1260 1047L1259 1046z"/>
<path style="fill:#eefffd; stroke:none;" d="M1260 1046C1262.49 1049.98 1265.18 1053.25 1269 1056C1267.4 1051.52 1264.66 1047.39 1260 1046z"/>
<path style="fill:#56a37f; stroke:none;" d="M592 1047L593 1048L592 1047z"/>
<path style="fill:#83caa8; stroke:none;" d="M1260 1047L1261 1048L1260 1047z"/>
<path style="fill:#308d60; stroke:none;" d="M164 1048L165 1051C165.685 1049.75 165.749 1049.45 166 1048L164 1048z"/>
<path style="fill:#42b280; stroke:none;" d="M593 1048L594 1049L593 1048z"/>
<path style="fill:#88cfad; stroke:none;" d="M804.333 1048.67C804.278 1048.72 804.222 1049.78 804.667 1049.33C804.722 1049.28 804.778 1048.22 804.333 1048.67z"/>
<path style="fill:#c2f9dc; stroke:none;" d="M869 1048L869 1051C869.696 1049.45 869.696 1049.55 869 1048z"/>
<path style="fill:#3f8762; stroke:none;" d="M985.333 1048.67C985.278 1048.72 985.222 1049.78 985.667 1049.33C985.722 1049.28 985.778 1048.22 985.333 1048.67z"/>
<path style="fill:#85cda8; stroke:none;" d="M1261 1048L1262 1049L1261 1048z"/>
<path style="fill:#5eb58b; stroke:none;" d="M594 1049L595 1050L594 1049z"/>
<path style="fill:#bad7c5; stroke:none;" d="M984 1049L985 1050L984 1049z"/>
<path style="fill:#a4d5b8; stroke:none;" d="M1262 1049L1263 1050L1262 1049z"/>
<path style="fill:#64c192; stroke:none;" d="M595 1050L596 1051L595 1050z"/>
<path style="fill:#a7e8c6; stroke:none;" d="M165 1051L166 1052L165 1051z"/>
<path style="fill:#93d0ae; stroke:none;" d="M596 1051L597 1052L596 1051z"/>
<path style="fill:#6ab891; stroke:none;" d="M869.333 1051.67C869.278 1051.72 869.222 1052.78 869.667 1052.33C869.722 1052.28 869.778 1051.22 869.333 1051.67z"/>
<path style="fill:#59a17b; stroke:none;" d="M1263 1051L1264 1052L1263 1051z"/>
<path style="fill:#5fad85; stroke:none;" d="M166 1052L167 1053L166 1052z"/>
<path style="fill:#77d0a2; stroke:none;" d="M597 1052L598 1053L597 1052z"/>
<path style="fill:#e3fff3; stroke:none;" d="M598 1052C600.596 1056.49 603.763 1060.01 608 1063C606.667 1058.28 602.734 1053.55 598 1052z"/>
<path style="fill:#64a585; stroke:none;" d="M805 1052L806 1053L805 1052z"/>
<path style="fill:#c9ffe8; stroke:none;" d="M870 1052L870 1056C870.71 1054.24 870.71 1053.76 870 1052z"/>
<path style="fill:#5cb78c; stroke:none;" d="M983 1052L984 1053L983 1052z"/>
<path style="fill:#5fc49c; stroke:none;" d="M1264 1052L1265 1053L1264 1052z"/>
<path style="fill:#c6ffe5; stroke:none;" d="M166 1053C166.406 1055.09 166.217 1055.02 168 1056C167.309 1054.61 167.016 1054.14 166 1053z"/>
<path style="fill:#8dd0ad; stroke:none;" d="M598 1053L599 1054L598 1053z"/>
<path style="fill:#bffadc; stroke:none;" d="M805.333 1053.67C805.278 1053.72 805.222 1054.78 805.667 1054.33C805.722 1054.28 805.778 1053.22 805.333 1053.67z"/>
<path style="fill:#72bca1; stroke:none;" d="M1265 1053L1266 1054L1265 1053z"/>
<path style="fill:#4ab27f; stroke:none;" d="M167 1054L168 1055L167 1054z"/>
<path style="fill:#8ce7bc; stroke:none;" d="M599 1054L600 1055L599 1054z"/>
<path style="fill:#75c19d; stroke:none;" d="M982 1054L983 1055L982 1054z"/>
<path style="fill:#76cdad; stroke:none;" d="M1266 1054L1267 1055L1266 1054z"/>
<path style="fill:#9ed8c2; stroke:none;" d="M1267 1055L1268 1056L1267 1055z"/>
<path style="fill:#6c9c82; stroke:none;" d="M168 1056L169 1057L168 1056z"/>
<path style="fill:#6fb791; stroke:none;" d="M806.333 1056.67C806.278 1056.72 806.222 1057.78 806.667 1057.33C806.722 1057.28 806.778 1056.22 806.333 1056.67z"/>
<path style="fill:#89f5c4; stroke:none;" d="M870.333 1056.67C870.278 1056.72 870.222 1057.78 870.667 1057.33C870.722 1057.28 870.778 1056.22 870.333 1056.67z"/>
<path style="fill:#d3fff5; stroke:none;" d="M871 1056L871 1064C872.161 1061.23 872.161 1058.77 871 1056z"/>
<path style="fill:#e8fff7; stroke:none;" d="M979 1056L970 1074C975.566 1070.86 978.913 1061.91 981 1056L979 1056z"/>
<path style="fill:#93eac0; stroke:none;" d="M1268 1056L1269 1057L1268 1056z"/>
<path style="fill:#b0e0c6; stroke:none;" d="M168 1057L169 1058L168 1057z"/>
<path style="fill:#adf1cc; stroke:none;" d="M980 1057L981 1058L980 1057z"/>
<path style="fill:#b0e8cd; stroke:none;" d="M1269 1057L1270 1058L1269 1057z"/>
<path style="fill:#5da782; stroke:none;" d="M169 1058L170 1059L169 1058z"/>
<path style="fill:#358a61; stroke:none;" d="M602 1058L603 1063L608 1064C606.205 1061.49 604.508 1059.8 602 1058z"/>
<path style="fill:#3dab7a; stroke:none;" d="M870.333 1058.67C870.278 1058.72 870.222 1059.78 870.667 1059.33C870.722 1059.28 870.778 1058.22 870.333 1058.67z"/>
<path style="fill:#6fbd93; stroke:none;" d="M980 1058L981 1059L980 1058z"/>
<path style="fill:#288c5a; stroke:none;" d="M981 1058L978 1064C980.544 1062.45 981.596 1061 981 1058z"/>
<path style="fill:#4db082; stroke:none;" d="M1269 1058L1270 1059L1269 1058z"/>
<path style="fill:#a6f0cb; stroke:none;" d="M169 1059L170 1060L169 1059z"/>
<path style="fill:#5da786; stroke:none;" d="M1270 1059L1271 1060L1270 1059z"/>
<path style="fill:#58a37c; stroke:none;" d="M170 1060L171 1061L170 1060z"/>
<path style="fill:#0f7742; stroke:none;" d="M171.333 1060.67C171.278 1060.72 171.222 1061.78 171.667 1061.33C171.722 1061.28 171.778 1060.22 171.333 1060.67z"/>
<path style="fill:#71b994; stroke:none;" d="M979 1060L980 1061L979 1060z"/>
<path style="fill:#10965b; stroke:none;" d="M1270 1060C1271.51 1064.7 1274.58 1071.1 1280 1071C1277.22 1066.82 1273.99 1063.05 1270 1060z"/>
<path style="fill:#62be97; stroke:none;" d="M1271 1060L1272 1061L1271 1060z"/>
<path style="fill:#a8f3cc; stroke:none;" d="M170 1061L171 1062L170 1061z"/>
<path style="fill:#93cbb2; stroke:none;" d="M1272 1061L1273 1062L1272 1061z"/>
<path style="fill:#629f7d; stroke:none;" d="M171 1062L172 1063L171 1062z"/>
<path style="fill:#238957; stroke:none;" d="M172 1062L173 1066C173.393 1064.08 173.146 1063.64 172 1062z"/>
<path style="fill:#a4ecc6; stroke:none;" d="M807.333 1062.67C807.278 1062.72 807.222 1063.78 807.667 1063.33C807.722 1063.28 807.778 1062.22 807.333 1062.67z"/>
<path style="fill:#208b57; stroke:none;" d="M808 1062L809 1072C810.459 1068.52 810.123 1065.11 808 1062z"/>
<path style="fill:#bdffe3; stroke:none;" d="M977.333 1062.67C977.277 1062.72 977.222 1063.78 977.667 1063.33C977.722 1063.28 977.778 1062.22 977.333 1062.67z"/>
<path style="fill:#6fc199; stroke:none;" d="M978 1062L979 1063L978 1062z"/>
<path style="fill:#8dd6b8; stroke:none;" d="M1273 1062L1274 1063L1273 1062z"/>
<path style="fill:#539c7e; stroke:none;" d="M1273 1063L1274 1064L1273 1063z"/>
<path style="fill:#aeddc9; stroke:none;" d="M1274 1063L1275 1064L1274 1063z"/>
<path style="fill:#278457; stroke:none;" d="M608 1064L608 1068L613 1070C611.73 1067.39 610.277 1065.81 608 1064z"/>
<path style="fill:#e7fff1; stroke:none;" d="M609 1064C611.291 1067.25 613.755 1069.71 617 1072C616.297 1067.17 613.71 1064.96 609 1064z"/>
<path style="fill:#84dcb6; stroke:none;" d="M871 1064L871 1067C871.696 1065.45 871.696 1065.55 871 1064z"/>
<path style="fill:#d7ffee; stroke:none;" d="M872 1064L872 1072C873.161 1069.23 873.161 1066.77 872 1064z"/>
<path style="fill:#3ea673; stroke:none;" d="M977.333 1064.67C977.278 1064.72 977.222 1065.78 977.667 1065.33C977.722 1065.28 977.778 1064.22 977.333 1064.67z"/>
<path style="fill:#a5e5c3; stroke:none;" d="M172 1065L173 1066L172 1065z"/>
<path style="fill:#9ef1c5; stroke:none;" d="M976 1065L977 1066L976 1065z"/>
<path style="fill:#e9fff3; stroke:none;" d="M172 1066C172.781 1068.94 173.395 1070.42 176 1072C174.911 1069.54 173.909 1067.88 172 1066z"/>
<path style="fill:#67aa89; stroke:none;" d="M173 1066L174 1067L173 1066z"/>
<path style="fill:#5bb68a; stroke:none;" d="M610 1066L611 1067L610 1066z"/>
<path style="fill:#82c5a2; stroke:none;" d="M808.333 1066.67C808.277 1066.72 808.222 1067.78 808.667 1067.33C808.722 1067.28 808.778 1066.22 808.333 1066.67z"/>
<path style="fill:#52af83; stroke:none;" d="M1276 1066L1277 1067L1276 1066z"/>
<path style="fill:#77b296; stroke:none;" d="M1277 1067L1278 1068L1277 1067z"/>
<path style="fill:#76b394; stroke:none;" d="M174 1068L175 1069L174 1068z"/>
<path style="fill:#d9fff2; stroke:none;" d="M808 1068C808.019 1074.74 809.157 1081.33 810 1088C812.078 1082.91 810.231 1072.97 808 1068z"/>
<path style="fill:#1c8864; stroke:none;" d="M975 1068L974 1072C975.656 1070.6 975.751 1070.05 975 1068z"/>
<path style="fill:#75d2a3; stroke:none;" d="M1278 1068L1279 1069L1278 1068z"/>
<path style="fill:#61a981; stroke:none;" d="M613 1069L614 1070L613 1069z"/>
<path style="fill:#a3e1cc; stroke:none;" d="M974 1069L975 1070L974 1069z"/>
<path style="fill:#9bdbb9; stroke:none;" d="M1279 1069L1280 1070L1279 1069z"/>
<path style="fill:#74be9d; stroke:none;" d="M175 1070L176 1071L175 1070z"/>
<path style="fill:#3f8058; stroke:none;" d="M612 1071C613.506 1071.68 614.315 1071.83 616 1072C614.19 1070.76 614.223 1070.82 612 1071z"/>
<path style="fill:#63b69a; stroke:none;" d="M974 1070L975 1071L974 1070z"/>
<path style="fill:#b6d6cb; stroke:none;" d="M1280 1070L1281 1071L1280 1070z"/>
<path style="fill:#a1dac7; stroke:none;" d="M973 1071L974 1072L973 1071z"/>
<path style="fill:#6a8a7f; stroke:none;" d="M1280 1071L1281 1072L1280 1071z"/>
<path style="fill:#bafcd8; stroke:none;" d="M176.333 1072.67C176.278 1072.72 176.222 1073.78 176.667 1073.33C176.722 1073.28 176.778 1072.22 176.333 1072.67z"/>
<path style="fill:#299460; stroke:none;" d="M177 1073C178.161 1075.79 179.478 1078.39 181 1081C182.195 1079.78 182.316 1079.58 183 1078C181.488 1076.11 180.852 1074.29 180 1072C178.203 1072 178.479 1072.01 177 1073z"/>
<path style="fill:#1f8657; stroke:none;" d="M616 1072C616.609 1075.14 616.7 1077.3 620 1078C618.837 1075.69 617.769 1073.87 616 1072z"/>
<path style="fill:#d8fff8; stroke:none;" d="M617 1072L618 1074C619.635 1073.46 619.455 1073.64 620 1072L617 1072z"/>
<path style="fill:#8ccdaf; stroke:none;" d="M809.333 1072.67C809.278 1072.72 809.222 1073.78 809.667 1073.33C809.722 1073.28 809.778 1072.22 809.333 1072.67z"/>
<path style="fill:#248155; stroke:none;" d="M810 1072L810 1078C810.951 1075.71 810.951 1074.29 810 1072z"/>
<path style="fill:#9af0c3; stroke:none;" d="M872 1072L872 1075C872.696 1073.45 872.696 1073.55 872 1072z"/>
<path style="fill:#d9ffed; stroke:none;" d="M873 1072L873 1084C874.512 1080.4 874.512 1075.6 873 1072z"/>
<path style="fill:#effff5; stroke:none;" d="M874 1072C874 1085.97 875.32 1100.12 874.985 1114C874.776 1122.67 873.179 1131.33 873 1140C875.2 1135.86 874.735 1131.54 875.17 1127C876.304 1115.13 875.058 1103.82 875.001 1092C874.971 1085.83 876.402 1077.73 874 1072z"/>
<path style="fill:#319861; stroke:none;" d="M973.333 1072.67C973.277 1072.72 973.222 1073.78 973.667 1073.33C973.722 1073.28 973.778 1072.22 973.333 1072.67z"/>
<path style="fill:#90d0ab; stroke:none;" d="M972 1073L973 1074L972 1073z"/>
<path style="fill:#63a984; stroke:none;" d="M1282 1073L1283 1074L1282 1073z"/>
<path style="fill:#f0fffa; stroke:none;" d="M176 1074C176.307 1076.12 176.382 1076.69 178 1078C177.969 1082.51 179.943 1085.94 184 1088C182.586 1083.09 180.271 1077 176 1074z"/>
<path style="fill:#39956c; stroke:none;" d="M618 1074C619.154 1076.25 619.751 1076.85 622 1078C620.67 1076.25 619.753 1075.33 618 1074z"/>
<path style="fill:#c8f9dc; stroke:none;" d="M970.667 1074.33C970.223 1074.78 971.277 1074.72 971.333 1074.67C971.778 1074.22 970.722 1074.28 970.667 1074.33z"/>
<path style="fill:#1f9058; stroke:none;" d="M972 1074L970 1080C973.364 1079.02 974.1 1076.78 972 1074z"/>
<path style="fill:#74c59c; stroke:none;" d="M1283 1074L1284 1075L1283 1074z"/>
<path style="fill:#efffff; stroke:none;" d="M1284 1074C1285.61 1077.12 1287.58 1079.45 1290 1082C1292.67 1079.63 1293.61 1077.54 1294 1074C1291.88 1074.31 1291.31 1074.38 1290 1076L1284 1074z"/>
<path style="fill:#5db688; stroke:none;" d="M872.333 1075.67C872.278 1075.72 872.222 1076.78 872.667 1076.33C872.722 1076.28 872.778 1075.22 872.333 1075.67z"/>
<path style="fill:#8abb9e; stroke:none;" d="M971 1075L972 1076L971 1075z"/>
<path style="fill:#a1d8b9; stroke:none;" d="M1284 1075L1285 1076L1284 1075z"/>
<path style="fill:#9dd8ba; stroke:none;" d="M178 1076L179 1077L178 1076z"/>
<path style="fill:#1f8a56; stroke:none;" d="M872 1077L872 1088C873.431 1084.59 873.431 1080.41 872 1077z"/>
<path style="fill:#72b290; stroke:none;" d="M970 1077L971 1078L970 1077z"/>
<path style="fill:#84d6ae; stroke:none;" d="M810.333 1078.67C810.278 1078.72 810.222 1079.78 810.667 1079.33C810.722 1079.28 810.778 1078.22 810.333 1078.67z"/>
<path style="fill:#198b50; stroke:none;" d="M811 1078C811.072 1086.72 812.999 1095.25 813 1104C815.748 1097.45 814.638 1084.07 811 1078z"/>
<path style="fill:#36a570; stroke:none;" d="M969.333 1079.67C969.278 1079.72 969.222 1080.78 969.667 1080.33C969.722 1080.28 969.778 1079.22 969.333 1079.67z"/>
<path style="fill:#258351; stroke:none;" d="M182 1080C182.397 1082.3 182.957 1083.9 184 1086C184.444 1083.42 183.631 1082.04 182 1080z"/>
<path style="fill:#329165; stroke:none;" d="M624 1080L624 1084L628 1084C626.67 1082.25 625.753 1081.33 624 1080z"/>
<path style="fill:#d5fff4; stroke:none;" d="M625 1080C627.503 1083.54 630.054 1086.16 634 1088C632.438 1083.52 629.77 1080.7 625 1080z"/>
<path style="fill:#acf2d0; stroke:none;" d="M810.333 1080.67C810.277 1080.72 810.222 1081.78 810.667 1081.33C810.722 1081.28 810.778 1080.22 810.333 1080.67z"/>
<path style="fill:#8edbb1; stroke:none;" d="M968 1080L969 1081L968 1080z"/>
<path style="fill:#29965f; stroke:none;" d="M1288 1080C1289.24 1083.64 1290.92 1085.71 1294 1088C1292.54 1084.72 1290.7 1082.37 1288 1080z"/>
<path style="fill:#75c19b; stroke:none;" d="M181 1081L182 1082L181 1081z"/>
<path style="fill:#1e8951; stroke:none;" d="M968 1081L965 1088C967.712 1086.23 968.744 1083.97 970 1081L968 1081z"/>
<path style="fill:#84d3a6; stroke:none;" d="M967 1082L968 1083L967 1082z"/>
<path style="fill:#87c7a4; stroke:none;" d="M1290 1082L1291 1083L1290 1082z"/>
<path style="fill:#78c09a; stroke:none;" d="M182 1083L183 1084L182 1083z"/>
<path style="fill:#bdd6c3; stroke:none;" d="M1291 1083L1292 1084L1291 1083z"/>
<path style="fill:#e5fff2; stroke:none;" d="M1292 1084C1293.45 1085.61 1294.05 1086.06 1296 1087L1295 1083L1292 1084z"/>
<path style="fill:#2c905c; stroke:none;" d="M628 1084C629.154 1086.25 629.751 1086.85 632 1088C630.67 1086.25 629.752 1085.33 628 1084z"/>
<path style="fill:#9cdcba; stroke:none;" d="M873 1084L873 1088C873.71 1086.24 873.71 1085.76 873 1084z"/>
<path style="fill:#5fbd8b; stroke:none;" d="M966 1084L967 1085L966 1084z"/>
<path style="fill:#91d5ae; stroke:none;" d="M183 1085L184 1086L183 1085z"/>
<path style="fill:#7dcba4; stroke:none;" d="M811.333 1085.67C811.278 1085.72 811.222 1086.78 811.667 1086.33C811.722 1086.28 811.778 1085.22 811.333 1085.67z"/>
<path style="fill:#61b486; stroke:none;" d="M184 1086L185 1087L184 1086z"/>
<path style="fill:#56b889; stroke:none;" d="M965 1086L966 1087L965 1086z"/>
<path style="fill:#99ecbe; stroke:none;" d="M184 1087L185 1088L184 1087z"/>
<path style="fill:#ade2c6; stroke:none;" d="M811 1087L811 1090C811.696 1088.45 811.696 1088.55 811 1087z"/>
<path style="fill:#9fe2c1; stroke:none;" d="M964 1087L965 1088L964 1087z"/>
<path style="fill:#78917c; stroke:none;" d="M1294 1087L1295 1088L1294 1087z"/>
<path style="fill:#75af96; stroke:none;" d="M185 1088L186 1089L185 1088z"/>
<path style="fill:#93efc6; stroke:none;" d="M633 1088L634 1089L633 1088z"/>
<path style="fill:#eafff6; stroke:none;" d="M810 1088L811 1104C812.913 1099.44 812.811 1092.11 810 1088z"/>
<path style="fill:#5bbb8d; stroke:none;" d="M873 1088L873 1093C873.83 1090.97 873.83 1090.03 873 1088z"/>
<path style="fill:#1f9259; stroke:none;" d="M964 1088L960 1096C963.293 1094.23 965.099 1091.77 964 1088z"/>
<path style="fill:#46d68e; stroke:none;" d="M1295 1088L1296 1089L1295 1088z"/>
<path style="fill:#f0fff2; stroke:none;" d="M1297 1088C1297.81 1090.89 1298.68 1092.09 1301 1094L1302 1089C1300.2 1088.44 1298.88 1088.18 1297 1088z"/>
<path style="fill:#83c8a9; stroke:none;" d="M634 1089L635 1090L634 1089z"/>
<path style="fill:#72b798; stroke:none;" d="M963 1089L964 1090L963 1089z"/>
<path style="fill:#a6c5b3; stroke:none;" d="M1296 1089L1297 1090L1296 1089z"/>
<path style="fill:#82cca9; stroke:none;" d="M186 1090L187 1091L186 1090z"/>
<path style="fill:#258458; stroke:none;" d="M633 1091C634.628 1092.74 635.697 1093.38 638 1094L635 1090L633 1091z"/>
<path style="fill:#7ac4a3; stroke:none;" d="M635 1090L636 1091L635 1090z"/>
<path style="fill:#8ec4aa; stroke:none;" d="M636 1091L637 1092L636 1091z"/>
<path style="fill:#119154; stroke:none;" d="M872 1091L872 1136C873.661 1132.04 873 1127.26 873 1123L873 1102C873 1098.3 873.441 1094.43 872 1091z"/>
<path style="fill:#6aa287; stroke:none;" d="M962 1091L963 1092L962 1091z"/>
<path style="fill:#a2d0b4; stroke:none;" d="M187 1092L188 1093L187 1092z"/>
<path style="fill:#60b689; stroke:none;" d="M637 1092L638 1093L637 1092z"/>
<path style="fill:#bbf3d6; stroke:none;" d="M960 1092L960 1094C961.635 1093.45 961.455 1093.64 962 1092L960 1092z"/>
<path style="fill:#58ae81; stroke:none;" d="M188 1093L189 1094L188 1093z"/>
<path style="fill:#61b78a; stroke:none;" d="M638 1093L639 1094L638 1093z"/>
<path style="fill:#65b389; stroke:none;" d="M812 1093L812 1096C812.696 1094.45 812.696 1094.55 812 1093z"/>
<path style="fill:#268859; stroke:none;" d="M873 1093L873 1123C874.328 1119.83 874 1116.42 874 1113C874 1106.85 875.395 1098.71 873 1093z"/>
<path style="fill:#5f9f7c; stroke:none;" d="M1299 1093L1300 1094L1299 1093z"/>
<path style="fill:#52c486; stroke:none;" d="M639 1094L640 1095L639 1094z"/>
<path style="fill:#a9f0d2; stroke:none;" d="M640 1094L641 1095L640 1094z"/>
<path style="fill:#cafce1; stroke:none;" d="M959 1094C958.014 1095.48 958 1095.2 958 1097C959.289 1095.56 959.401 1095.77 959 1094z"/>
<path style="fill:#5ccb96; stroke:none;" d="M960 1094L961 1095L960 1094z"/>
<path style="fill:#3a855a; stroke:none;" d="M1300.33 1094.67C1300.28 1094.72 1300.22 1095.78 1300.67 1095.33C1300.72 1095.28 1300.78 1094.22 1300.33 1094.67z"/>
<path style="fill:#64ba8d; stroke:none;" d="M189 1095L190 1096L189 1095z"/>
<path style="fill:#579e80; stroke:none;" d="M640 1095L641 1096L640 1095z"/>
<path style="fill:#8bb398; stroke:none;" d="M1301 1095L1302 1096L1301 1095z"/>
<path style="fill:#9df1cd; stroke:none;" d="M642 1096L643 1097L642 1096z"/>
<path style="fill:#9ce7be; stroke:none;" d="M812 1096L812 1099C812.696 1097.45 812.696 1097.55 812 1096z"/>
<path style="fill:#ddfff6; stroke:none;" d="M957 1096C955.957 1098.1 955.397 1099.7 955 1102C956.777 1099.95 957.64 1098.66 957 1096z"/>
<path style="fill:#37835c; stroke:none;" d="M959.333 1096.67C959.277 1096.72 959.222 1097.78 959.667 1097.33C959.722 1097.28 959.778 1096.22 959.333 1096.67z"/>
<path style="fill:#80d3a5; stroke:none;" d="M1302 1096L1303 1097L1302 1096z"/>
<path style="fill:#67dbaa; stroke:none;" d="M190 1097L191 1098L190 1097z"/>
<path style="fill:#1d8e56; stroke:none;" d="M191 1097C192.304 1100.11 193.602 1102.63 196 1105C196.238 1102.91 196.184 1101.11 196 1099L191 1097z"/>
<path style="fill:#a3d9bf; stroke:none;" d="M643 1097L644 1098L643 1097z"/>
<path style="fill:#86b89d; stroke:none;" d="M958 1097L959 1098L958 1097z"/>
<path style="fill:#51a476; stroke:none;" d="M1302 1097L1303 1098L1302 1097z"/>
<path style="fill:#97d0af; stroke:none;" d="M1303 1097L1304 1098L1303 1097z"/>
<path style="fill:#509b7d; stroke:none;" d="M191 1098L192 1099L191 1098z"/>
<path style="fill:#8fd7af; stroke:none;" d="M644 1098L645 1099L644 1098z"/>
<path style="fill:#14914f; stroke:none;" d="M1296 1098L1296 1104L1300 1104C1299.15 1101.19 1298.45 1099.61 1296 1098z"/>
<path style="fill:#9ce7c9; stroke:none;" d="M191 1099L192 1100L191 1099z"/>
<path style="fill:#9bc6ab; stroke:none;" d="M645 1099L646 1100L645 1099z"/>
<path style="fill:#cbffe7; stroke:none;" d="M812 1099L812 1104C812.83 1101.97 812.83 1101.03 812 1099z"/>
<path style="fill:#5ca37b; stroke:none;" d="M1304 1099L1305 1100L1304 1099z"/>
<path style="fill:#64c598; stroke:none;" d="M192 1100L193 1101L192 1100z"/>
<path style="fill:#72cb9d; stroke:none;" d="M646 1100L647 1101L646 1100z"/>
<path style="fill:#81d4aa; stroke:none;" d="M956 1100L957 1101L956 1100z"/>
<path style="fill:#5cb585; stroke:none;" d="M1305 1100L1306 1101L1305 1100z"/>
<path style="fill:#c7fff6; stroke:none;" d="M192 1101C192.406 1103.09 192.217 1103.02 194 1104C193.309 1102.61 193.016 1102.14 192 1101z"/>
<path style="fill:#74b794; stroke:none;" d="M647 1101L648 1102L647 1101z"/>
<path style="fill:#2a8d5e; stroke:none;" d="M956 1101L956 1104C957.016 1102.86 957.309 1102.39 958 1101L956 1101z"/>
<path style="fill:#7bb897; stroke:none;" d="M1306 1101L1307 1102L1306 1101z"/>
<path style="fill:#78dbac; stroke:none;" d="M193 1102L194 1103L193 1102z"/>
<path style="fill:#70c79c; stroke:none;" d="M648 1102L649 1103L648 1102z"/>
<path style="fill:#afe4c8; stroke:none;" d="M649 1102L650 1103L649 1102z"/>
<path style="fill:#518d71; stroke:none;" d="M955.333 1102.67C955.277 1102.72 955.222 1103.78 955.667 1103.33C955.722 1103.28 955.778 1102.22 955.333 1102.67z"/>
<path style="fill:#82cca7; stroke:none;" d="M1307 1102L1308 1103L1307 1102z"/>
<path style="fill:#77ac90; stroke:none;" d="M649 1103L650 1104L649 1103z"/>
<path style="fill:#99b3a6; stroke:none;" d="M954 1103L955 1104L954 1103z"/>
<path style="fill:#b2d9c4; stroke:none;" d="M1308 1103L1309 1104L1308 1103z"/>
<path style="fill:#41ad7c; stroke:none;" d="M650 1104L651 1105L650 1104z"/>
<path style="fill:#a0f2ce; stroke:none;" d="M651 1104L652 1105L651 1104z"/>
<path style="fill:#71ad89; stroke:none;" d="M813 1104L813 1109C813.83 1106.97 813.83 1106.03 813 1104z"/>
<path style="fill:#cbffe6; stroke:none;" d="M874 1104L874 1120C875.812 1115.68 875.812 1108.32 874 1104z"/>
<path style="fill:#95e8bc; stroke:none;" d="M953 1104L954 1105L953 1104z"/>
<path style="fill:#1e915a; stroke:none;" d="M1308 1104C1306.98 1109.34 1311.09 1110.24 1314 1114C1314.86 1109.8 1311.08 1106.49 1308 1104z"/>
<path style="fill:#93e6bc; stroke:none;" d="M1309 1104L1310 1105L1309 1104z"/>
<path style="fill:#d5ffef; stroke:none;" d="M1310 1104L1311 1107C1311.68 1105.75 1311.75 1105.45 1312 1104L1310 1104z"/>
<path style="fill:#69c8a0; stroke:none;" d="M195 1105L196 1106L195 1105z"/>
<path style="fill:#4d9f7b; stroke:none;" d="M651 1105L652 1106L651 1105z"/>
<path style="fill:#a3d9bf; stroke:none;" d="M652 1105L653 1106L652 1105z"/>
<path style="fill:#69bc90; stroke:none;" d="M953 1105L954 1106L953 1105z"/>
<path style="fill:#96e7be; stroke:none;" d="M1310 1105L1311 1106L1310 1105z"/>
<path style="fill:#248f5b; stroke:none;" d="M196 1106L198 1110C198.558 1107.69 197.996 1107.24 196 1106z"/>
<path style="fill:#8cd8b2; stroke:none;" d="M653 1106L654 1107L653 1106z"/>
<path style="fill:#78c69e; stroke:none;" d="M952 1106L953 1107L952 1106z"/>
<path style="fill:#1c8759; stroke:none;" d="M953 1106C950.263 1109.98 947.309 1113.75 945 1118C949.116 1115.9 953.709 1110.89 953 1106z"/>
<path style="fill:#89deb5; stroke:none;" d="M196 1107L197 1108L196 1107z"/>
<path style="fill:#82caa5; stroke:none;" d="M654 1107L655 1108L654 1107z"/>
<path style="fill:#64b68e; stroke:none;" d="M1311 1107L1312 1108L1311 1107z"/>
<path style="fill:#6fa887; stroke:none;" d="M197 1108L198 1109L197 1108z"/>
<path style="fill:#5acb93; stroke:none;" d="M655 1108L656 1109L655 1108z"/>
<path style="fill:#5fbc8f; stroke:none;" d="M951 1108L952 1109L951 1108z"/>
<path style="fill:#7eb89f; stroke:none;" d="M1312 1108L1313 1109L1312 1108z"/>
<path style="fill:#eafff8; stroke:none;" d="M1313 1108L1316 1113C1316.78 1111.23 1316.91 1109.96 1317 1108L1313 1108z"/>
<path style="fill:#86b098; stroke:none;" d="M656 1109L657 1110L656 1109z"/>
<path style="fill:#bdeece; stroke:none;" d="M813 1109L813 1114C813.83 1111.97 813.83 1111.03 813 1109z"/>
<path style="fill:#8ed1b0; stroke:none;" d="M950 1109L951 1110L950 1109z"/>
<path style="fill:#9abfad; stroke:none;" d="M1313 1109L1314 1110L1313 1109z"/>
<path style="fill:#7aba98; stroke:none;" d="M198 1110L199 1111L198 1110z"/>
<path style="fill:#379968; stroke:none;" d="M199.333 1110.67C199.278 1110.72 199.222 1111.78 199.667 1111.33C199.722 1111.28 199.778 1110.22 199.333 1110.67z"/>
<path style="fill:#62b28b; stroke:none;" d="M657 1110L658 1111L657 1110z"/>
<path style="fill:#e6fff4; stroke:none;" d="M658 1110C661.032 1113.5 664.215 1117.07 669 1117C667.752 1116.32 667.451 1116.25 666 1116C665.141 1111.96 661.892 1110.59 658 1110z"/>
<path style="fill:#9ae6c0; stroke:none;" d="M949 1110L950 1111L949 1110z"/>
<path style="fill:#85dab0; stroke:none;" d="M1314 1110L1315 1111L1314 1110z"/>
<path style="fill:#d7fde6; stroke:none;" d="M198 1111C198.684 1112.58 198.805 1112.78 200 1114C199.316 1112.42 199.195 1112.22 198 1111z"/>
<path style="fill:#5ba781; stroke:none;" d="M949 1111L950 1112L949 1111z"/>
<path style="fill:#abe6c8; stroke:none;" d="M1315 1111L1316 1112L1315 1111z"/>
<path style="fill:#2b9662; stroke:none;" d="M200 1112L202 1116C202.369 1113.78 201.86 1113.27 200 1112z"/>
<path style="fill:#84dbae; stroke:none;" d="M660 1112L661 1113L660 1112z"/>
<path style="fill:#349768; stroke:none;" d="M1315 1112C1315.68 1113.58 1315.81 1113.78 1317 1115C1316.32 1113.42 1316.2 1113.22 1315 1112z"/>
<path style="fill:#70bc96; stroke:none;" d="M200 1113L201 1114L200 1113z"/>
<path style="fill:#8bccaa; stroke:none;" d="M661 1113L662 1114L661 1113z"/>
<path style="fill:#9fd6b9; stroke:none;" d="M947 1113L948 1114L947 1113z"/>
<path style="fill:#74cd9f; stroke:none;" d="M662 1114L663 1115L662 1114z"/>
<path style="fill:#d0fdde; stroke:none;" d="M813 1114L813 1120C813.951 1117.71 813.951 1116.29 813 1114z"/>
<path style="fill:#97d8b6; stroke:none;" d="M201 1115L202 1116L201 1115z"/>
<path style="fill:#65ad88; stroke:none;" d="M663 1115L664 1116L663 1115z"/>
<path style="fill:#6cad8f; stroke:none;" d="M946 1115L947 1116L946 1115z"/>
<path style="fill:#8fd0b0; stroke:none;" d="M1316 1115L1317 1116L1316 1115z"/>
<path style="fill:#e1fff3; stroke:none;" d="M201 1116C201.818 1120.53 203.415 1124.49 208 1126C206.032 1122.3 204.086 1118.85 201 1116z"/>
<path style="fill:#65a281; stroke:none;" d="M202 1116L203 1117L202 1116z"/>
<path style="fill:#4fb585; stroke:none;" d="M664 1116L665 1117L664 1116z"/>
<path style="fill:#7adfb5; stroke:none;" d="M945 1116L946 1117L945 1116z"/>
<path style="fill:#5aaa85; stroke:none;" d="M665 1117L666 1118L665 1117z"/>
<path style="fill:#9cb8a1; stroke:none;" d="M203 1118L204 1119L203 1118z"/>
<path style="fill:#529b70; stroke:none;" d="M204.333 1118.67C204.278 1118.72 204.222 1119.78 204.667 1119.33C204.722 1119.28 204.778 1118.22 204.333 1118.67z"/>
<path style="fill:#217d54; stroke:none;" d="M666 1118C666.684 1119.58 666.805 1119.78 668 1121C667.316 1119.42 667.195 1119.22 666 1118z"/>
<path style="fill:#99e0c2; stroke:none;" d="M667 1118L668 1119L667 1118z"/>
<path style="fill:#43ae84; stroke:none;" d="M944 1118L945 1119L944 1118z"/>
<path style="fill:#30925f; stroke:none;" d="M1313.33 1118.67C1313.28 1118.72 1313.22 1119.78 1313.67 1119.33C1313.72 1119.28 1313.78 1118.22 1313.33 1118.67z"/>
<path style="fill:#7abe97; stroke:none;" d="M1314 1118L1315 1119L1314 1118z"/>
<path style="fill:#96c6b0; stroke:none;" d="M668 1119L669 1120L668 1119z"/>
<path style="fill:#a9b5b1; stroke:none;" d="M943 1119L944 1120L943 1119z"/>
<path style="fill:#6bd4ab; stroke:none;" d="M669 1120L670 1121L669 1120z"/>
<path style="fill:#c3fffd; stroke:none;" d="M670 1120C670.545 1121.64 670.365 1121.45 672 1122L672 1120L670 1120z"/>
<path style="fill:#f2f8ec; stroke:none;" d="M813 1120L813 1136C814.812 1131.68 814.812 1124.32 813 1120z"/>
<path style="fill:#e3fff4; stroke:none;" d="M935 1130C938.063 1127 940.637 1123.58 943 1120C938.484 1122.03 935.008 1124.81 935 1130z"/>
<path style="fill:#22915c; stroke:none;" d="M943 1120C940.966 1122.59 939.276 1124.97 938 1128C940.85 1125.88 943.5 1123.74 943 1120z"/>
<path style="fill:#18935a; stroke:none;" d="M1306 1122C1307.8 1122 1307.52 1121.99 1309 1121L1308 1127L1312 1123C1309.99 1120.89 1308.16 1119.56 1306 1122z"/>
<path style="fill:#81d1ac; stroke:none;" d="M205 1121L206 1122L205 1121z"/>
<path style="fill:#53bb96; stroke:none;" d="M670 1121L671 1122L670 1121z"/>
<path style="fill:#99bdb1; stroke:none;" d="M1312 1121L1313 1122L1312 1121z"/>
<path style="fill:#cbf8e1; stroke:none;" d="M672 1122C673.45 1123.61 674.051 1124.06 676 1125C674.55 1123.39 673.949 1122.94 672 1122z"/>
<path style="fill:#9fecc8; stroke:none;" d="M206 1123L207 1124L206 1123z"/>
<path style="fill:#66b78e; stroke:none;" d="M873 1123L873 1129C873.951 1126.71 873.951 1125.29 873 1123z"/>
<path style="fill:#7bbc9e; stroke:none;" d="M940 1123L941 1124L940 1123z"/>
<path style="fill:#95f6c9; stroke:none;" d="M1311 1123L1312 1124L1311 1123z"/>
<path style="fill:#7abb9d; stroke:none;" d="M207 1124L208 1125L207 1124z"/>
<path style="fill:#228a59; stroke:none;" d="M208 1124L211 1130C211.596 1127 210.544 1125.55 208 1124z"/>
<path style="fill:#81deb1; stroke:none;" d="M939 1124L940 1125L939 1124z"/>
<path style="fill:#89b79a; stroke:none;" d="M1310 1124L1311 1125L1310 1124z"/>
<path style="fill:#99d4b4; stroke:none;" d="M675 1125L676 1126L675 1125z"/>
<path style="fill:#a6dcba; stroke:none;" d="M208 1126L209 1127L208 1126z"/>
<path style="fill:#6ab88e; stroke:none;" d="M209.333 1126.67C209.278 1126.72 209.222 1127.78 209.667 1127.33C209.722 1127.28 209.778 1126.22 209.333 1126.67z"/>
<path style="fill:#0a8244; stroke:none;" d="M675 1126C675.936 1127.95 676.392 1128.55 678 1130C677.064 1128.05 676.608 1127.45 675 1126z"/>
<path style="fill:#6fb68c; stroke:none;" d="M676 1126L677 1127L676 1126z"/>
<path style="fill:#a9dfc5; stroke:none;" d="M1309 1126L1310 1127L1309 1126z"/>
<path style="fill:#7da187; stroke:none;" d="M677 1127L678 1128L677 1127z"/>
<path style="fill:#3ca970; stroke:none;" d="M937.333 1127.67C937.278 1127.72 937.222 1128.78 937.667 1128.33C937.722 1128.28 937.778 1127.22 937.333 1127.67z"/>
<path style="fill:#82e3b6; stroke:none;" d="M1308 1127L1309 1128L1308 1127z"/>
<path style="fill:#ddfff0; stroke:none;" d="M209 1128C209.849 1130.81 210.548 1132.39 213 1134C211.995 1131.41 211.02 1129.9 209 1128z"/>
<path style="fill:#e2fff8; stroke:none;" d="M679 1128C681.759 1130.98 684.676 1133.67 688 1136C686.508 1131.08 684.153 1128.69 679 1128z"/>
<path style="fill:#7ecca2; stroke:none;" d="M936 1128L937 1129L936 1128z"/>
<path style="fill:#5ca985; stroke:none;" d="M1307 1128L1308 1129L1307 1128z"/>
<path style="fill:#9ddfbb; stroke:none;" d="M680 1129L681 1130L680 1129z"/>
<path style="fill:#8bd3ad; stroke:none;" d="M873.333 1129.67C873.278 1129.72 873.222 1130.78 873.667 1130.33C873.722 1130.28 873.778 1129.22 873.333 1129.67z"/>
<path style="fill:#81c4a3; stroke:none;" d="M211 1130L212 1131L211 1130z"/>
<path style="fill:#7fdbac; stroke:none;" d="M681 1130L682 1131L681 1130z"/>
<path style="fill:#a5debe; stroke:none;" d="M1306 1130L1307 1131L1306 1130z"/>
<path style="fill:#88c8a5; stroke:none;" d="M682 1131L683 1132L682 1131z"/>
<path style="fill:#a3e9c4; stroke:none;" d="M873 1131L873 1134C873.696 1132.45 873.696 1132.55 873 1131z"/>
<path style="fill:#66a683; stroke:none;" d="M934 1131L935 1132L934 1131z"/>
<path style="fill:#52b586; stroke:none;" d="M1305 1131L1306 1132L1305 1131z"/>
<path style="fill:#5db98a; stroke:none;" d="M683 1132L684 1133L683 1132z"/>
<path style="fill:#65c997; stroke:none;" d="M933 1132L934 1133L933 1132z"/>
<path style="fill:#7dc89f; stroke:none;" d="M213 1133L214 1134L213 1133z"/>
<path style="fill:#65a885; stroke:none;" d="M684 1133L685 1134L684 1133z"/>
<path style="fill:#8ed8b3; stroke:none;" d="M932 1133L933 1134L932 1133z"/>
<path style="fill:#9cdeb8; stroke:none;" d="M1304 1133L1305 1134L1304 1133z"/>
<path style="fill:#56a67f; stroke:none;" d="M685 1134L686 1135L685 1134z"/>
<path style="fill:#d1f0e0; stroke:none;" d="M930 1134L930 1136C931.635 1135.46 931.455 1135.64 932 1134L930 1134z"/>
<path style="fill:#7fb895; stroke:none;" d="M1303 1134L1304 1135L1303 1134z"/>
<path style="fill:#548d6d; stroke:none;" d="M686 1135L687 1136L686 1135z"/>
<path style="fill:#e9ffff; stroke:none;" d="M208 1136L208 1140L210 1140L210 1136L208 1136z"/>
<path style="fill:#88ceaa; stroke:none;" d="M688 1136L689 1137L688 1136z"/>
<path style="fill:#cefce0; stroke:none;" d="M813 1136L813 1152C814.812 1147.68 814.812 1140.32 813 1136z"/>
<path style="fill:#1a8d54; stroke:none;" d="M871 1136L871 1150C872.667 1146.03 872.667 1139.97 871 1136z"/>
<path style="fill:#3a7b5b; stroke:none;" d="M872 1136L872 1140C872.71 1138.24 872.71 1137.76 872 1136z"/>
<path style="fill:#a9ffe3; stroke:none;" d="M928 1136L928 1138C929.635 1137.45 929.455 1137.64 930 1136L928 1136z"/>
<path style="fill:#53bc91; stroke:none;" d="M930 1136L931 1137L930 1136z"/>
<path style="fill:#1e935c; stroke:none;" d="M1299 1136L1299 1140C1300.27 1138.66 1301.04 1137.6 1302 1136L1299 1136z"/>
<path style="fill:#dbfff1; stroke:none;" d="M1302 1136L1299 1142C1301.41 1140.41 1302.48 1138.9 1302 1136z"/>
<path style="fill:#60b78d; stroke:none;" d="M216 1137L217 1138L216 1137z"/>
<path style="fill:#2a8559; stroke:none;" d="M688 1137C689.23 1140.73 692.265 1142.97 696 1144C693.723 1140.91 691.318 1138.92 688 1137z"/>
<path style="fill:#80ac95; stroke:none;" d="M689 1137L690 1138L689 1137z"/>
<path style="fill:#61c7a0; stroke:none;" d="M929 1137L930 1138L929 1137z"/>
<path style="fill:#5ab288; stroke:none;" d="M690 1138L691 1139L690 1138z"/>
<path style="fill:#67d1a3; stroke:none;" d="M928 1138L929 1139L928 1138z"/>
<path style="fill:#6eb593; stroke:none;" d="M1300 1138L1301 1139L1300 1138z"/>
<path style="fill:#7bdbad; stroke:none;" d="M217 1139L218 1140L217 1139z"/>
<path style="fill:#abd5bf; stroke:none;" d="M927 1139L928 1140L927 1139z"/>
<path style="fill:#6bb68d; stroke:none;" d="M218 1140L219 1141L218 1140z"/>
<path style="fill:#1b9657; stroke:none;" d="M219 1140C222.471 1149.02 231.334 1161.12 239 1167L239 1162C229.879 1157.84 228.266 1144.94 219 1140z"/>
<path style="fill:#6ca98a; stroke:none;" d="M872.333 1140.67C872.278 1140.72 872.222 1141.78 872.667 1141.33C872.722 1141.28 872.778 1140.22 872.333 1140.67z"/>
<path style="fill:#d9fff8; stroke:none;" d="M922 1143C924.674 1142.6 925.237 1142.11 927 1140C924.697 1140.62 923.628 1141.26 922 1143z"/>
<path style="fill:#1d995a; stroke:none;" d="M1296 1140L1296 1144C1297.43 1142.61 1298.04 1141.77 1299 1140L1296 1140z"/>
<path style="fill:#9bdebb; stroke:none;" d="M1299 1140L1300 1141L1299 1140z"/>
<path style="fill:#afd9c3; stroke:none;" d="M694 1141L695 1142L694 1141z"/>
<path style="fill:#6bc496; stroke:none;" d="M1298 1141L1299 1142L1298 1141z"/>
<path style="fill:#9cd3b3; stroke:none;" d="M219 1142L220 1143L219 1142z"/>
<path style="fill:#89d6b2; stroke:none;" d="M695 1142L696 1143L695 1142z"/>
<path style="fill:#95caae; stroke:none;" d="M872.333 1142.67C872.278 1142.72 872.222 1143.78 872.667 1143.33C872.722 1143.28 872.778 1142.22 872.333 1142.67z"/>
<path style="fill:#64ba8b; stroke:none;" d="M220 1143L221 1144L220 1143z"/>
<path style="fill:#c3f9e2; stroke:none;" d="M696.667 1143.33C696.222 1143.78 697.278 1143.72 697.333 1143.67C697.778 1143.22 696.722 1143.28 696.667 1143.33z"/>
<path style="fill:#66b48d; stroke:none;" d="M924 1143L925 1144L924 1143z"/>
<path style="fill:#cdfbdf; stroke:none;" d="M1297 1143C1296.01 1144.48 1296 1144.2 1296 1146C1297.29 1144.56 1297.4 1144.77 1297 1143z"/>
<path style="fill:#62c899; stroke:none;" d="M697 1144L698 1145L697 1144z"/>
<path style="fill:#d4fff1; stroke:none;" d="M698 1144C699.103 1145.46 699.543 1145.9 701 1147C700.101 1144.88 700.124 1144.9 698 1144z"/>
<path style="fill:#deffee; stroke:none;" d="M872 1144L872 1152C873.161 1149.23 873.161 1146.77 872 1144z"/>
<path style="fill:#e4fff8; stroke:none;" d="M920 1144L921 1146C922.635 1145.46 922.455 1145.64 923 1144L920 1144z"/>
<path style="fill:#62b48e; stroke:none;" d="M698 1145L699 1146L698 1145z"/>
<path style="fill:#7fc2a1; stroke:none;" d="M922 1145L923 1146L922 1145z"/>
<path style="fill:#5bc796; stroke:none;" d="M1295 1145L1296 1146L1295 1145z"/>
<path style="fill:#91ccae; stroke:none;" d="M222 1146L223 1147L222 1146z"/>
<path style="fill:#1e9158; stroke:none;" d="M699 1146L699 1150L703 1151C701.896 1148.64 701.044 1147.62 699 1146z"/>
<path style="fill:#7ed3aa; stroke:none;" d="M921 1146L922 1147L921 1146z"/>
<path style="fill:#1f7248; stroke:none;" d="M1292 1149C1293.46 1147.9 1293.9 1147.46 1295 1146C1292.88 1146.9 1292.9 1146.88 1292 1149z"/>
<path style="fill:#6abf98; stroke:none;" d="M223 1147L224 1148L223 1147z"/>
<path style="fill:#99d8bb; stroke:none;" d="M920 1147L921 1148L920 1147z"/>
<path style="fill:#91eec1; stroke:none;" d="M702 1148L703 1149L702 1148z"/>
<path style="fill:#a0f2ce; stroke:none;" d="M919 1148L920 1149L919 1148z"/>
<path style="fill:#8dc2a6; stroke:none;" d="M1293 1148L1294 1149L1293 1148z"/>
<path style="fill:#baffed; stroke:none;" d="M224 1149C224.406 1151.09 224.217 1151.02 226 1152C225.309 1150.61 225.015 1150.14 224 1149z"/>
<path style="fill:#9ee1c0; stroke:none;" d="M703 1149L704 1150L703 1149z"/>
<path style="fill:#e1fff2; stroke:none;" d="M812 1149L812 1163C813.667 1159.03 813.667 1152.97 812 1149z"/>
<path style="fill:#ace4c9; stroke:none;" d="M918 1149L919 1150L918 1149z"/>
<path style="fill:#53a581; stroke:none;" d="M919 1149L920 1150L919 1149z"/>
<path style="fill:#62b58b; stroke:none;" d="M1292 1149L1293 1150L1292 1149z"/>
<path style="fill:#6ad29f; stroke:none;" d="M225 1150L226 1151L225 1150z"/>
<path style="fill:#a1cabc; stroke:none;" d="M704 1150L705 1151L704 1150z"/>
<path style="fill:#49b07b; stroke:none;" d="M871.333 1150.67C871.278 1150.72 871.222 1151.78 871.667 1151.33C871.722 1151.28 871.778 1150.22 871.333 1150.67z"/>
<path style="fill:#aedfc9; stroke:none;" d="M917 1150L918 1151L917 1150z"/>
<path style="fill:#59a986; stroke:none;" d="M918 1150L919 1151L918 1150z"/>
<path style="fill:#defff8; stroke:none;" d="M1292 1150C1290.74 1152.01 1289.81 1153.78 1289 1156C1292.11 1154.91 1293.1 1153.17 1292 1150z"/>
<path style="fill:#4eb985; stroke:none;" d="M226 1151L227 1152L226 1151z"/>
<path style="fill:#517a6c; stroke:none;" d="M704 1151L705 1152L704 1151z"/>
<path style="fill:#9ebeb3; stroke:none;" d="M705 1151L706 1152L705 1151z"/>
<path style="fill:#6e9f89; stroke:none;" d="M917 1151L918 1152L917 1151z"/>
<path style="fill:#ddfff6; stroke:none;" d="M226 1152C226.849 1154.81 227.548 1156.39 230 1158C228.995 1155.41 228.02 1153.9 226 1152z"/>
<path style="fill:#58d297; stroke:none;" d="M706 1152L707 1153L706 1152z"/>
<path style="fill:#8cd8b2; stroke:none;" d="M813.333 1152.67C813.278 1152.72 813.222 1153.78 813.667 1153.33C813.722 1153.28 813.778 1152.22 813.333 1152.67z"/>
<path style="fill:#348f66; stroke:none;" d="M870 1152L869 1162C871.123 1158.89 871.459 1155.48 870 1152z"/>
<path style="fill:#90cfb4; stroke:none;" d="M871.333 1152.67C871.278 1152.72 871.222 1153.78 871.667 1153.33C871.722 1153.28 871.778 1152.22 871.333 1152.67z"/>
<path style="fill:#f1ffff; stroke:none;" d="M896 1156C897.227 1159.28 899.901 1161.39 900 1165C903.07 1164.9 903.987 1164.32 906 1162L902 1162C902.769 1157.34 905.85 1155.75 908 1152C903.336 1152.14 900.014 1153.75 896 1156z"/>
<path style="fill:#80caa7; stroke:none;" d="M1290 1152L1291 1153L1290 1152z"/>
<path style="fill:#63b68c; stroke:none;" d="M707 1153L708 1154L707 1153z"/>
<path style="fill:#70b191; stroke:none;" d="M915 1153L916 1154L915 1153z"/>
<path style="fill:#5fbe92; stroke:none;" d="M1289 1153L1290 1154L1289 1153z"/>
<path style="fill:#79c39e; stroke:none;" d="M228 1154L229 1155L228 1154z"/>
<path style="fill:#5cb589; stroke:none;" d="M708 1154L709 1155L708 1154z"/>
<path style="fill:#69b790; stroke:none;" d="M813 1154L813 1157C813.696 1155.45 813.696 1155.55 813 1154z"/>
<path style="fill:#cafee7; stroke:none;" d="M871 1154L871 1159C871.83 1156.97 871.83 1156.03 871 1154z"/>
<path style="fill:#69ba91; stroke:none;" d="M914 1154L915 1155L914 1154z"/>
<path style="fill:#71b493; stroke:none;" d="M709 1155L710 1156L709 1155z"/>
<path style="fill:#8cc7a7; stroke:none;" d="M913 1155L914 1156L913 1155z"/>
<path style="fill:#98eec1; stroke:none;" d="M1288 1155L1289 1156L1288 1155z"/>
<path style="fill:#69d19c; stroke:none;" d="M912 1156L913 1157L912 1156z"/>
<path style="fill:#78c59b; stroke:none;" d="M1287 1156L1288 1157L1287 1156z"/>
<path style="fill:#e4ffed; stroke:none;" d="M1288 1156C1287.04 1157.52 1286.61 1158.34 1286 1160C1287.86 1158.73 1288.37 1158.22 1288 1156z"/>
<path style="fill:#90deb6; stroke:none;" d="M230 1157L231 1158L230 1157z"/>
<path style="fill:#5dad8a; stroke:none;" d="M711 1157L712 1158L711 1157z"/>
<path style="fill:#2e855b; stroke:none;" d="M813 1157L813 1168C814.431 1164.59 814.431 1160.41 813 1157z"/>
<path style="fill:#8fc6af; stroke:none;" d="M911 1157L912 1158L911 1157z"/>
<path style="fill:#52c58a; stroke:none;" d="M1286 1157L1287 1158L1286 1157z"/>
<path style="fill:#e0fffa; stroke:none;" d="M230 1158C230.98 1161.2 231.781 1162.9 235 1164C233.617 1161.36 232.348 1159.83 230 1158z"/>
<path style="fill:#70b08e; stroke:none;" d="M231 1158L232 1159L231 1158z"/>
<path style="fill:#5cbd90; stroke:none;" d="M712 1158L713 1159L712 1158z"/>
<path style="fill:#9de4c4; stroke:none;" d="M713 1158L714 1159L713 1158z"/>
<path style="fill:#7ad2a8; stroke:none;" d="M910 1158L911 1159L910 1158z"/>
<path style="fill:#148c4c; stroke:none;" d="M1281 1158C1278.96 1162.39 1276.17 1164.93 1276 1170C1279.94 1166.79 1283.02 1162.69 1285 1158L1281 1158z"/>
<path style="fill:#5bb486; stroke:none;" d="M1285 1158L1286 1159L1285 1158z"/>
<path style="fill:#6ab191; stroke:none;" d="M713 1159L714 1160L713 1159z"/>
<path style="fill:#87c4a3; stroke:none;" d="M909 1159L910 1160L909 1159z"/>
<path style="fill:#95eec0; stroke:none;" d="M1285 1159L1286 1160L1285 1159z"/>
<path style="fill:#56c291; stroke:none;" d="M714 1160L715 1161L714 1160z"/>
<path style="fill:#8acdac; stroke:none;" d="M870.333 1160.67C870.278 1160.72 870.222 1161.78 870.667 1161.33C870.722 1161.28 870.778 1160.22 870.333 1160.67z"/>
<path style="fill:#79d1a1; stroke:none;" d="M908 1160L909 1161L908 1160z"/>
<path style="fill:#9ed5b5; stroke:none;" d="M1284 1160L1285 1161L1284 1160z"/>
<path style="fill:#e9fdf4; stroke:none;" d="M1285 1160C1283.12 1162.27 1281.4 1164.41 1280 1167C1283.3 1165.76 1284.95 1163.83 1287 1161L1285 1160z"/>
<path style="fill:#66b391; stroke:none;" d="M715 1161L716 1162L715 1161z"/>
<path style="fill:#8fc5a3; stroke:none;" d="M907 1161L908 1162L907 1161z"/>
<path style="fill:#62b98e; stroke:none;" d="M1283 1161L1284 1162L1283 1161z"/>
<path style="fill:#7ac4a1; stroke:none;" d="M234 1162L235 1163L234 1162z"/>
<path style="fill:#19924f; stroke:none;" d="M715 1162L715 1166L712 1165C713.912 1167.92 715.696 1167.69 719 1167L719 1164L715 1162z"/>
<path style="fill:#5eb78b; stroke:none;" d="M716 1162L717 1163L716 1162z"/>
<path style="fill:#1a8454; stroke:none;" d="M868 1162C867.995 1169.49 866.001 1176.49 866 1184C869.021 1177.71 869.454 1168.92 870 1162L868 1162z"/>
<path style="fill:#74cd9d; stroke:none;" d="M906 1162L907 1163L906 1162z"/>
<path style="fill:#5aa989; stroke:none;" d="M1282 1162L1283 1163L1282 1162z"/>
<path style="fill:#74af91; stroke:none;" d="M717 1163L718 1164L717 1163z"/>
<path style="fill:#b0e2c9; stroke:none;" d="M812.333 1163.67C812.278 1163.72 812.222 1164.78 812.667 1164.33C812.722 1164.28 812.778 1163.22 812.333 1163.67z"/>
<path style="fill:#83c79e; stroke:none;" d="M905 1163L906 1164L905 1163z"/>
<path style="fill:#a0efcf; stroke:none;" d="M1282 1163L1283 1164L1282 1163z"/>
<path style="fill:#68ca99; stroke:none;" d="M904 1164L905 1165L904 1164z"/>
<path style="fill:#9ce7c6; stroke:none;" d="M236 1165L237 1166L236 1165z"/>
<path style="fill:#69c095; stroke:none;" d="M719 1165L720 1166L719 1165z"/>
<path style="fill:#93c9af; stroke:none;" d="M812 1165L812 1168C812.696 1166.45 812.696 1166.55 812 1165z"/>
<path style="fill:#89c9a6; stroke:none;" d="M903 1165L904 1166L903 1165z"/>
<path style="fill:#80ccb0; stroke:none;" d="M1280 1165L1281 1166L1280 1165z"/>
<path style="fill:#f1fff4; stroke:none;" d="M236 1166C236.341 1170.48 237.734 1172.5 242 1174C242.53 1176.8 243.246 1177.18 246 1178C243.373 1173.45 240.09 1169.3 236 1166z"/>
<path style="fill:#61b993; stroke:none;" d="M237 1166L238 1167L237 1166z"/>
<path style="fill:#a9c9be; stroke:none;" d="M720 1166L721 1167L720 1166z"/>
<path style="fill:#60b389; stroke:none;" d="M869.333 1166.67C869.278 1166.72 869.222 1167.78 869.667 1167.33C869.722 1167.28 869.778 1166.22 869.333 1166.67z"/>
<path style="fill:#94c4aa; stroke:none;" d="M902 1166L903 1167L902 1166z"/>
<path style="fill:#4fc082; stroke:none;" d="M1279 1166L1280 1167L1279 1166z"/>
<path style="fill:#47bb8c; stroke:none;" d="M238 1167L239 1168L238 1167z"/>
<path style="fill:#56766b; stroke:none;" d="M720 1167L721 1168L720 1167z"/>
<path style="fill:#bed5cd; stroke:none;" d="M721 1167L722 1168L721 1167z"/>
<path style="fill:#a8b4aa; stroke:none;" d="M901 1167L902 1168L901 1167z"/>
<path style="fill:#9bffce; stroke:none;" d="M1279 1167L1280 1168L1279 1167z"/>
<path style="fill:#6b897f; stroke:none;" d="M239 1168L240 1169L239 1168z"/>
<path style="fill:#a4fecb; stroke:none;" d="M722 1168L723 1169L722 1168z"/>
<path style="fill:#d2ffea; stroke:none;" d="M811 1168L811 1173C811.83 1170.97 811.83 1170.03 811 1168z"/>
<path style="fill:#a6edcd; stroke:none;" d="M869.333 1168.67C869.278 1168.72 869.222 1169.78 869.667 1169.33C869.722 1169.28 869.778 1168.22 869.333 1168.67z"/>
<path style="fill:#72d1a5; stroke:none;" d="M900 1168L901 1169L900 1168z"/>
<path style="fill:#bfddd3; stroke:none;" d="M239 1169L240 1170L239 1169z"/>
<path style="fill:#83c0a1; stroke:none;" d="M899 1169L900 1170L899 1169z"/>
<path style="fill:#93d9b7; stroke:none;" d="M240 1170L241 1171L240 1170z"/>
<path style="fill:#6bbe92; stroke:none;" d="M723 1170L724 1171L723 1170z"/>
<path style="fill:#b3eccc; stroke:none;" d="M897 1170L898 1171L897 1170z"/>
<path style="fill:#6cba92; stroke:none;" d="M898 1170L899 1171L898 1170z"/>
<path style="fill:#238b5a; stroke:none;" d="M898 1172L901 1172L900 1170C898.365 1170.55 898.545 1170.36 898 1172z"/>
<path style="fill:#8ed1ae; stroke:none;" d="M1276 1170L1277 1171L1276 1170z"/>
<path style="fill:#54b788; stroke:none;" d="M241 1171L242 1172L241 1171z"/>
<path style="fill:#9fd1b4; stroke:none;" d="M724 1171L725 1172L724 1171z"/>
<path style="fill:#70a989; stroke:none;" d="M897 1171L898 1172L897 1171z"/>
<path style="fill:#56b686; stroke:none;" d="M1275 1171L1276 1172L1275 1171z"/>
<path style="fill:#67a886; stroke:none;" d="M242 1172L243 1173L242 1172z"/>
<path style="fill:#e4fff6; stroke:none;" d="M883 1181C888.349 1181.08 891.946 1176.17 896 1173C891.224 1170.41 886.415 1178.14 883 1181z"/>
<path style="fill:#329662; stroke:none;" d="M1273 1172L1273 1174C1274.64 1173.45 1274.45 1173.64 1275 1172L1273 1172z"/>
<path style="fill:#5aa881; stroke:none;" d="M725 1173L726 1174L725 1173z"/>
<path style="fill:#88d0ab; stroke:none;" d="M811 1173L811 1176C811.696 1174.45 811.696 1174.55 811 1173z"/>
<path style="fill:#91d4b1; stroke:none;" d="M868 1173L868 1176C868.696 1174.45 868.696 1174.55 868 1173z"/>
<path style="fill:#7fab94; stroke:none;" d="M895 1173L896 1174L895 1173z"/>
<path style="fill:#7fd6ab; stroke:none;" d="M726 1174L727 1175L726 1174z"/>
<path style="fill:#bff4d8; stroke:none;" d="M727.333 1174.67C727.278 1174.72 727.222 1175.78 727.667 1175.33C727.722 1175.28 727.778 1174.22 727.333 1174.67z"/>
<path style="fill:#a5f3cc; stroke:none;" d="M893 1174L894 1175L893 1174z"/>
<path style="fill:#58ab81; stroke:none;" d="M894 1174L895 1175L894 1174z"/>
<path style="fill:#93caad; stroke:none;" d="M1273 1174L1274 1175L1273 1174z"/>
<path style="fill:#88e1b5; stroke:none;" d="M244 1175L245 1176L244 1175z"/>
<path style="fill:#59a780; stroke:none;" d="M893 1175L894 1176L893 1175z"/>
<path style="fill:#72cb9f; stroke:none;" d="M1272 1175L1273 1176L1272 1175z"/>
<path style="fill:#8bc1a7; stroke:none;" d="M245 1176L246 1177L245 1176z"/>
<path style="fill:#21865a; stroke:none;" d="M246 1176L248 1180C248.369 1177.78 247.86 1177.27 246 1176z"/>
<path style="fill:#ddffec; stroke:none;" d="M728 1176L729 1179C729.685 1177.75 729.749 1177.45 730 1176L728 1176z"/>
<path style="fill:#45956e; stroke:none;" d="M811.333 1176.67C811.278 1176.72 811.222 1177.78 811.667 1177.33C811.722 1177.28 811.778 1176.22 811.333 1176.67z"/>
<path style="fill:#8ee3b9; stroke:none;" d="M891 1176L892 1177L891 1176z"/>
<path style="fill:#6faf8d; stroke:none;" d="M1271 1176L1272 1177L1271 1176z"/>
<path style="fill:#69bb95; stroke:none;" d="M246 1177L247 1178L246 1177z"/>
<path style="fill:#86cea8; stroke:none;" d="M728 1177L729 1178L728 1177z"/>
<path style="fill:#9cdcb9; stroke:none;" d="M890 1177L891 1178L890 1177z"/>
<path style="fill:#199056; stroke:none;" d="M728 1178L727 1184L731 1184C731.404 1187.91 732.101 1190.66 736 1192C733.938 1187.05 731.688 1181.94 728 1178z"/>
<path style="fill:#28895c; stroke:none;" d="M811 1178L811 1184C811.951 1181.71 811.951 1180.29 811 1178z"/>
<path style="fill:#75cc9f; stroke:none;" d="M889 1178L890 1179L889 1178z"/>
<path style="fill:#acedcf; stroke:none;" d="M247 1179L248 1180L247 1179z"/>
<path style="fill:#6bb68f; stroke:none;" d="M729 1179L730 1180L729 1179z"/>
<path style="fill:#a4d3b1; stroke:none;" d="M867.333 1179.67C867.278 1179.72 867.222 1180.78 867.667 1180.33C867.722 1180.28 867.778 1179.22 867.333 1179.67z"/>
<path style="fill:#87cda9; stroke:none;" d="M888 1179L889 1180L888 1179z"/>
<path style="fill:#98f5c8; stroke:none;" d="M1269 1179L1270 1180L1269 1179z"/>
<path style="fill:#93c9b1; stroke:none;" d="M248 1180L249 1181L248 1180z"/>
<path style="fill:#9ae5be; stroke:none;" d="M730 1180L731 1181L730 1180z"/>
<path style="fill:#5bbd8e; stroke:none;" d="M887 1180L888 1181L887 1180z"/>
<path style="fill:#91caaa; stroke:none;" d="M1268 1180L1269 1181L1268 1180z"/>
<path style="fill:#7bcba6; stroke:none;" d="M249 1181L250 1182L249 1181z"/>
<path style="fill:#80c5a6; stroke:none;" d="M810 1181L810 1184C810.696 1182.45 810.696 1182.55 810 1181z"/>
<path style="fill:#64b48f; stroke:none;" d="M886 1181L887 1182L886 1181z"/>
<path style="fill:#66bb91; stroke:none;" d="M1267 1181L1268 1182L1267 1181z"/>
<path style="fill:#7bb795; stroke:none;" d="M250 1182L251 1183L250 1182z"/>
<path style="fill:#7fe0b5; stroke:none;" d="M731 1182L732 1183L731 1182z"/>
<path style="fill:#c0ffe8; stroke:none;" d="M732.333 1182.67C732.278 1182.72 732.222 1183.78 732.667 1183.33C732.722 1183.28 732.778 1182.22 732.333 1182.67z"/>
<path style="fill:#a4e9cc; stroke:none;" d="M884 1182L885 1183L884 1182z"/>
<path style="fill:#5aa57e; stroke:none;" d="M1266 1182L1267 1183L1266 1182z"/>
<path style="fill:#51a778; stroke:none;" d="M251 1183L252 1184L251 1183z"/>
<path style="fill:#acd7c4; stroke:none;" d="M883 1183L884 1184L883 1183z"/>
<path style="fill:#e7fff6; stroke:none;" d="M251 1184L252 1190L256 1190C254.488 1187.74 252.968 1185.87 251 1184z"/>
<path style="fill:#559b79; stroke:none;" d="M252 1184L253 1185L252 1184z"/>
<path style="fill:#6dbf97; stroke:none;" d="M732 1184L733 1185L732 1184z"/>
<path style="fill:#168c5c; stroke:none;" d="M810 1184C809.764 1189.42 808.113 1194.56 808 1200C811.855 1196.22 812.053 1188.89 810 1184z"/>
<path style="fill:#8dd0a7; stroke:none;" d="M866.333 1184.67C866.278 1184.72 866.222 1185.78 866.667 1185.33C866.722 1185.28 866.778 1184.22 866.333 1184.67z"/>
<path style="fill:#eefffa; stroke:none;" d="M870 1184L871 1190L867 1192L867 1186C865.242 1189.91 864.574 1194.73 864 1199C869.165 1195.58 875.247 1191.65 878 1186L874 1188L874 1184L870 1184z"/>
<path style="fill:#abffe2; stroke:none;" d="M880 1184L880 1186C881.635 1185.46 881.455 1185.64 882 1184L880 1184z"/>
<path style="fill:#71dca8; stroke:none;" d="M882 1184L883 1185L882 1184z"/>
<path style="fill:#6a8a7f; stroke:none;" d="M1264 1184L1265 1185L1264 1184z"/>
<path style="fill:#a2e8c6; stroke:none;" d="M252 1185L253 1186L252 1185z"/>
<path style="fill:#66ce9d; stroke:none;" d="M881 1185L882 1186L881 1185z"/>
<path style="fill:#b6d6cb; stroke:none;" d="M1264 1185L1265 1186L1264 1185z"/>
<path style="fill:#92d8b6; stroke:none;" d="M253 1186L254 1187L253 1186z"/>
<path style="fill:#7bbc9c; stroke:none;" d="M733 1186L734 1187L733 1186z"/>
<path style="fill:#a1debd; stroke:none;" d="M809.333 1186.67C809.278 1186.72 809.222 1187.78 809.667 1187.33C809.722 1187.28 809.778 1186.22 809.333 1186.67z"/>
<path style="fill:#bdffe6; stroke:none;" d="M878 1186L878 1188C879.635 1187.46 879.455 1187.64 880 1186L878 1186z"/>
<path style="fill:#55c08a; stroke:none;" d="M880 1186L881 1187L880 1186z"/>
<path style="fill:#95d7b3; stroke:none;" d="M1263 1186L1264 1187L1263 1186z"/>
<path style="fill:#7fd7af; stroke:none;" d="M254 1187L255 1188L254 1187z"/>
<path style="fill:#77d8a5; stroke:none;" d="M1262 1187L1263 1188L1262 1187z"/>
<path style="fill:#78b498; stroke:none;" d="M255 1188L256 1189L255 1188z"/>
<path style="fill:#6cc79c; stroke:none;" d="M734 1188L735 1189L734 1188z"/>
<path style="fill:#5da580; stroke:none;" d="M809.333 1188.67C809.278 1188.72 809.222 1189.78 809.667 1189.33C809.722 1189.28 809.778 1188.22 809.333 1188.67z"/>
<path style="fill:#99f0c3; stroke:none;" d="M877 1188L878 1189L877 1188z"/>
<path style="fill:#63a283; stroke:none;" d="M1261 1188L1262 1189L1261 1188z"/>
<path style="fill:#3bb687; stroke:none;" d="M865 1189L866 1190L865 1189z"/>
<path style="fill:#97c8ab; stroke:none;" d="M876 1189L877 1190L876 1189z"/>
<path style="fill:#4daf80; stroke:none;" d="M1260 1189L1261 1190L1260 1189z"/>
<path style="fill:#9adcb8; stroke:none;" d="M257.333 1190.67C257.278 1190.72 257.222 1191.78 257.667 1191.33C257.722 1191.28 257.778 1190.22 257.333 1190.67z"/>
<path style="fill:#66d2a1; stroke:none;" d="M735 1190L736 1191L735 1190z"/>
<path style="fill:#edfcf5; stroke:none;" d="M736 1190C737.522 1193.41 739.325 1196.27 740 1200C741.437 1196.47 740.265 1193.47 739 1190L736 1190z"/>
<path style="fill:#c8ffee; stroke:none;" d="M808 1190C807.352 1192.71 807.058 1195.22 807 1198C808.278 1195.34 808.871 1192.83 808 1190z"/>
<path style="fill:#2a936b; stroke:none;" d="M864.333 1190.67C864.278 1190.72 864.222 1191.78 864.667 1191.33C864.722 1191.28 864.778 1190.22 864.333 1190.67z"/>
<path style="fill:#98dec6; stroke:none;" d="M865.333 1190.67C865.278 1190.72 865.222 1191.78 865.667 1191.33C865.722 1191.28 865.778 1190.22 865.333 1190.67z"/>
<path style="fill:#66b18a; stroke:none;" d="M875 1190L876 1191L875 1190z"/>
<path style="fill:#36815a; stroke:none;" d="M876 1190L874 1191C875.482 1191.79 876.796 1191.87 876 1190z"/>
<path style="fill:#b6e6d0; stroke:none;" d="M873 1191L874 1192L873 1191z"/>
<path style="fill:#93d2b3; stroke:none;" d="M736 1192L737 1193L736 1192z"/>
<path style="fill:#d4ffed; stroke:none;" d="M737 1192L739 1200C740.098 1196.83 739.037 1194.56 737 1192z"/>
<path style="fill:#548774; stroke:none;" d="M864 1192L864 1195C864.696 1193.45 864.696 1193.55 864 1192z"/>
<path style="fill:#81dcb3; stroke:none;" d="M872 1192L873 1193L872 1192z"/>
<path style="fill:#107b47; stroke:none;" d="M873 1192L873 1194L875 1194L875 1192L873 1192z"/>
<path style="fill:#4f9f7c; stroke:none;" d="M1257 1192L1258 1193L1257 1192z"/>
<path style="fill:#8dddb6; stroke:none;" d="M259 1193L260 1194L259 1193z"/>
<path style="fill:#62a182; stroke:none;" d="M736 1193L737 1194L736 1193z"/>
<path style="fill:#8ad7b5; stroke:none;" d="M808 1193L809 1194L808 1193z"/>
<path style="fill:#309c6a; stroke:none;" d="M869 1196C870.949 1195.06 871.55 1194.61 873 1193C871.052 1193.94 870.45 1194.39 869 1196z"/>
<path style="fill:#89d9b6; stroke:none;" d="M1257 1193L1258 1194L1257 1193z"/>
<path style="fill:#83c0a1; stroke:none;" d="M260 1194L261 1195L260 1194z"/>
<path style="fill:#1c8e53; stroke:none;" d="M261 1194L264 1199C264.801 1196.09 263.749 1194.96 261 1194z"/>
<path style="fill:#177b49; stroke:none;" d="M736.333 1194.67C736.278 1194.72 736.222 1195.78 736.667 1195.33C736.722 1195.28 736.778 1194.22 736.333 1194.67z"/>
<path style="fill:#94ceb5; stroke:none;" d="M1256 1194L1257 1195L1256 1194z"/>
<path style="fill:#e2fffb; stroke:none;" d="M1257 1194C1255.14 1195.95 1253.53 1197.79 1252 1200C1255.16 1199.12 1257.54 1197.51 1257 1194z"/>
<path style="fill:#77c59d; stroke:none;" d="M261 1195L262 1196L261 1195z"/>
<path style="fill:#7dbe9c; stroke:none;" d="M737 1195L738 1196L737 1195z"/>
<path style="fill:#a8c7b7; stroke:none;" d="M864 1195L865 1196L864 1195z"/>
<path style="fill:#99dfbd; stroke:none;" d="M868 1195L869 1196L868 1195z"/>
<path style="fill:#67c69e; stroke:none;" d="M1255 1195L1256 1196L1255 1195z"/>
<path style="fill:#73a687; stroke:none;" d="M262 1196L263 1197L262 1196z"/>
<path style="fill:#28a466; stroke:none;" d="M736 1196L736 1198C737.635 1197.46 737.455 1197.64 738 1196L736 1196z"/>
<path style="fill:#6bcd9c; stroke:none;" d="M867 1196L868 1197L867 1196z"/>
<path style="fill:#1f9558; stroke:none;" d="M868 1196C866.39 1197.33 865.299 1198.38 864 1200C866.396 1199.44 868.613 1198.68 868 1196z"/>
<path style="fill:#69b090; stroke:none;" d="M1254 1196L1255 1197L1254 1196z"/>
<path style="fill:#5bad7f; stroke:none;" d="M263 1197L264 1198L263 1197z"/>
<path style="fill:#92dfb5; stroke:none;" d="M738 1197L739 1198L738 1197z"/>
<path style="fill:#d9fff1; stroke:none;" d="M262 1200L265 1200L264 1198C262.365 1198.55 262.545 1198.36 262 1200z"/>
<path style="fill:#2d8658; stroke:none;" d="M738.333 1198.67C738.278 1198.72 738.222 1199.78 738.667 1199.33C738.722 1199.28 738.778 1198.22 738.333 1198.67z"/>
<path style="fill:#e0ffeb; stroke:none;" d="M806 1198C804.868 1202.03 804.087 1205.81 804 1210C805.876 1206.5 807.366 1201.82 806 1198z"/>
<path style="fill:#5bb792; stroke:none;" d="M807.333 1198.67C807.278 1198.72 807.222 1199.78 807.667 1199.33C807.722 1199.28 807.778 1198.22 807.333 1198.67z"/>
<path style="fill:#afd8ca; stroke:none;" d="M265 1200L266 1201L265 1200z"/>
<path style="fill:#82efb8; stroke:none;" d="M739 1200L740 1201L739 1200z"/>
<path style="fill:#c9ffec; stroke:none;" d="M740 1200L740 1204C740.71 1202.24 740.71 1201.76 740 1200z"/>
<path style="fill:#1f8a56; stroke:none;" d="M807 1200C806.499 1202.69 806.117 1205.27 806 1208C807.433 1205.3 808.038 1202.9 807 1200z"/>
<path style="fill:#559f84; stroke:none;" d="M1250 1200L1251 1201L1250 1200z"/>
<path style="fill:#e7fffd; stroke:none;" d="M1251 1200L1247 1206C1250.24 1205.08 1252.2 1203.46 1251 1200z"/>
<path style="fill:#ecfff7; stroke:none;" d="M264 1201C265.556 1204.91 268.218 1206.99 271 1210C271.895 1205.54 267.672 1202.72 264 1201z"/>
<path style="fill:#95d3ba; stroke:none;" d="M266 1201L267 1202L266 1201z"/>
<path style="fill:#46b37c; stroke:none;" d="M739 1201L740 1202L739 1201z"/>
<path style="fill:#8ed8bd; stroke:none;" d="M1250 1201L1251 1202L1250 1201z"/>
<path style="fill:#87ccad; stroke:none;" d="M267 1202L268 1203L267 1202z"/>
<path style="fill:#95e6bd; stroke:none;" d="M806 1202L807 1203L806 1202z"/>
<path style="fill:#88d1b6; stroke:none;" d="M1249 1202L1250 1203L1249 1202z"/>
<path style="fill:#64c194; stroke:none;" d="M268 1203L269 1204L268 1203z"/>
<path style="fill:#74d2ad; stroke:none;" d="M1248 1203L1249 1204L1248 1203z"/>
<path style="fill:#72ba94; stroke:none;" d="M269 1204L270 1205L269 1204z"/>
<path style="fill:#5fb88c; stroke:none;" d="M740 1204L741 1205L740 1204z"/>
<path style="fill:#87be97; stroke:none;" d="M1247 1204L1248 1205L1247 1204z"/>
<path style="fill:#64b688; stroke:none;" d="M270 1205L271 1206L270 1205z"/>
<path style="fill:#75bc90; stroke:none;" d="M1246 1205L1247 1206L1246 1205z"/>
<path style="fill:#75ac8d; stroke:none;" d="M271 1206L272 1207L271 1206z"/>
<path style="fill:#9ae0be; stroke:none;" d="M741.333 1206.67C741.278 1206.72 741.222 1207.78 741.667 1207.33C741.722 1207.28 741.778 1206.22 741.333 1206.67z"/>
<path style="fill:#85c9a4; stroke:none;" d="M805.333 1206.67C805.278 1206.72 805.222 1207.78 805.667 1207.33C805.722 1207.28 805.778 1206.22 805.333 1206.67z"/>
<path style="fill:#79bb97; stroke:none;" d="M1245 1206L1246 1207L1245 1206z"/>
<path style="fill:#54af83; stroke:none;" d="M1244 1207L1245 1208L1244 1207z"/>
<path style="fill:#d6fff2; stroke:none;" d="M272 1208C273.049 1210.89 275.175 1213.09 276 1216C277.452 1215.75 277.752 1215.68 279 1215C276.96 1212.12 274.88 1210.04 272 1208z"/>
<path style="fill:#2c8358; stroke:none;" d="M273 1208C274.33 1209.75 275.247 1210.67 277 1212L278 1211C276.283 1209.16 275.434 1208.65 273 1208z"/>
<path style="fill:#129454; stroke:none;" d="M276 1208C277.993 1213.28 280.312 1216.69 285 1220C283.656 1215.03 280.673 1210.29 276 1208z"/>
<path style="fill:#248b5c; stroke:none;" d="M741 1208C741.021 1210.85 741.19 1213.27 742 1216C742.668 1213.21 742.172 1210.61 741 1208z"/>
<path style="fill:#308359; stroke:none;" d="M805 1208C804.028 1212.7 802.417 1217.21 802 1222C804.94 1217.99 807.012 1212.8 805 1208z"/>
<path style="fill:#63957c; stroke:none;" d="M1243 1208L1244 1209L1243 1208z"/>
<path style="fill:#53a87f; stroke:none;" d="M1242 1209L1243 1210L1242 1209z"/>
<path style="fill:#a1e2c4; stroke:none;" d="M742.333 1210.67C742.278 1210.72 742.222 1211.78 742.667 1211.33C742.722 1211.28 742.778 1210.22 742.333 1210.67z"/>
<path style="fill:#9edebc; stroke:none;" d="M804 1210L805 1211L804 1210z"/>
<path style="fill:#57a482; stroke:none;" d="M1241 1210L1242 1211L1241 1210z"/>
<path style="fill:#65b08f; stroke:none;" d="M742.333 1212.67C742.278 1212.72 742.222 1213.78 742.667 1213.33C742.722 1213.28 742.778 1212.22 742.333 1212.67z"/>
<path style="fill:#a9dbc2; stroke:none;" d="M1240 1212L1241 1213L1240 1212z"/>
<path style="fill:#9be5c0; stroke:none;" d="M1239 1213L1240 1214L1239 1213z"/>
<path style="fill:#92bca4; stroke:none;" d="M803 1214L804 1215L803 1214z"/>
<path style="fill:#9cdcb9; stroke:none;" d="M1238 1214L1239 1215L1238 1214z"/>
<path style="fill:#6fdaa2; stroke:none;" d="M279 1215L280 1216L279 1215z"/>
<path style="fill:#88f8c0; stroke:none;" d="M1237 1215L1238 1216L1237 1215z"/>
<path style="fill:#91bda6; stroke:none;" d="M280 1216L281 1217L280 1216z"/>
<path style="fill:#a3ffd1; stroke:none;" d="M743 1216L743 1219C743.696 1217.45 743.696 1217.55 743 1216z"/>
<path style="fill:#e6fff1; stroke:none;" d="M744 1216L744 1232C745.812 1227.68 745.812 1220.32 744 1216z"/>
<path style="fill:#e0fff1; stroke:none;" d="M801 1216L800 1222C801.603 1220.08 802.247 1218.41 803 1216L801 1216z"/>
<path style="fill:#95caae; stroke:none;" d="M1236 1216L1237 1217L1236 1216z"/>
<path style="fill:#93d4b2; stroke:none;" d="M281 1217L282 1218L281 1217z"/>
<path style="fill:#a2ddc1; stroke:none;" d="M802 1217L803 1218L802 1217z"/>
<path style="fill:#76cba4; stroke:none;" d="M1235 1217L1236 1218L1235 1217z"/>
<path style="fill:#d7fff0; stroke:none;" d="M280 1219C282.073 1221.62 283.778 1223.08 287 1224C285.468 1221.79 283.862 1219.95 282 1218L280 1219z"/>
<path style="fill:#7bc39e; stroke:none;" d="M282 1218L283 1219L282 1218z"/>
<path style="fill:#80c7a5; stroke:none;" d="M1234 1218L1235 1219L1234 1218z"/>
<path style="fill:#5fbc8d; stroke:none;" d="M283 1219L284 1220L283 1219z"/>
<path style="fill:#60bd8e; stroke:none;" d="M743 1219L743 1232C744.591 1228.21 744.591 1222.79 743 1219z"/>
<path style="fill:#7ad5ac; stroke:none;" d="M1233 1219L1234 1220L1233 1219z"/>
<path style="fill:#72be98; stroke:none;" d="M284 1220L285 1221L284 1220z"/>
<path style="fill:#1e8e54; stroke:none;" d="M285 1220C286.223 1221.2 286.42 1221.32 288 1222C287.02 1220.22 287.092 1220.41 285 1220z"/>
<path style="fill:#1b9059; stroke:none;" d="M803 1220C801.747 1222 800.758 1223.77 800 1226C802.267 1224.13 803.623 1222.96 803 1220z"/>
<path style="fill:#98c8ae; stroke:none;" d="M1232 1220L1233 1221L1232 1220z"/>
<path style="fill:#5ab385; stroke:none;" d="M285 1221L286 1222L285 1221z"/>
<path style="fill:#75caa1; stroke:none;" d="M801 1221L802 1222L801 1221z"/>
<path style="fill:#75d39e; stroke:none;" d="M1231 1221L1232 1222L1231 1221z"/>
<path style="fill:#427f60; stroke:none;" d="M286 1222C286.545 1223.64 286.365 1223.45 288 1224L288 1222L286 1222z"/>
<path style="fill:#adffd9; stroke:none;" d="M800.333 1222.67C800.278 1222.72 800.222 1223.78 800.667 1223.33C800.722 1223.28 800.778 1222.22 800.333 1222.67z"/>
<path style="fill:#86c29e; stroke:none;" d="M1230 1222L1231 1223L1230 1222z"/>
<path style="fill:#6fc095; stroke:none;" d="M1229 1223L1230 1224L1229 1223z"/>
<path style="fill:#21935f; stroke:none;" d="M288 1224C290.365 1227.08 292.862 1229.72 296 1232C294.644 1227.49 292.826 1224.71 288 1224z"/>
<path style="fill:#e6fffe; stroke:none;" d="M797 1224C795.837 1228.87 794.603 1234.05 794 1239C797.304 1234.91 799.113 1229.15 800 1224L797 1224z"/>
<path style="fill:#68ab8a; stroke:none;" d="M1228 1224L1229 1225L1228 1224z"/>
<path style="fill:#55ba8e; stroke:none;" d="M1227 1225L1228 1226L1227 1225z"/>
<path style="fill:#69a589; stroke:none;" d="M290 1226L291 1227L290 1226z"/>
<path style="fill:#63b591; stroke:none;" d="M1226 1226L1227 1227L1226 1226z"/>
<path style="fill:#6eaf99; stroke:none;" d="M799 1227L800 1228L799 1227z"/>
<path style="fill:#49b385; stroke:none;" d="M1225 1227L1226 1228L1225 1227z"/>
<path style="fill:#d8fff8; stroke:none;" d="M291 1228C291.839 1230.63 292.374 1231.16 295 1232C293.67 1230.25 292.753 1229.33 291 1228z"/>
<path style="fill:#268b69; stroke:none;" d="M799 1228L798 1232C799.656 1230.6 799.751 1230.05 799 1228z"/>
<path style="fill:#72aa8d; stroke:none;" d="M1224 1228L1225 1229L1224 1228z"/>
<path style="fill:#a6dbc9; stroke:none;" d="M798 1229L799 1230L798 1229z"/>
<path style="fill:#62b98e; stroke:none;" d="M1223 1229L1224 1230L1223 1229z"/>
<path style="fill:#5ea28b; stroke:none;" d="M798 1230L799 1231L798 1230z"/>
<path style="fill:#6ebc92; stroke:none;" d="M1222 1230L1223 1231L1222 1230z"/>
<path style="fill:#d0ffff; stroke:none;" d="M1230 1230L1230 1232L1232 1232L1232 1230L1230 1230z"/>
<path style="fill:#4ec488; stroke:none;" d="M1221 1231L1222 1232L1221 1231z"/>
<path style="fill:#ddfff3; stroke:none;" d="M295 1232C295.776 1234.13 296.13 1234.77 298 1236C298.613 1239.12 299.888 1239.51 303 1240C300.709 1236.76 298.245 1234.29 295 1232z"/>
<path style="fill:#369263; stroke:none;" d="M296 1232C298.316 1235.18 300.002 1237.06 304 1237C301.462 1234.9 299.149 1232.97 296 1232z"/>
<path style="fill:#368d63; stroke:none;" d="M742 1232L742 1240C743.161 1237.23 743.161 1234.77 742 1232z"/>
<path style="fill:#b5edd2; stroke:none;" d="M743 1232L743 1238C743.951 1235.71 743.951 1234.29 743 1232z"/>
<path style="fill:#82cfa3; stroke:none;" d="M797 1232L798 1233L797 1232z"/>
<path style="fill:#77ac90; stroke:none;" d="M1220 1232L1221 1233L1220 1232z"/>
<path style="fill:#7acfa8; stroke:none;" d="M1219 1233L1220 1234L1219 1233z"/>
<path style="fill:#83caa8; stroke:none;" d="M1218 1234L1219 1235L1218 1234z"/>
<path style="fill:#72ae8c; stroke:none;" d="M796 1235L797 1236L796 1235z"/>
<path style="fill:#77d2a9; stroke:none;" d="M1217 1235L1218 1236L1217 1235z"/>
<path style="fill:#d0e6d9; stroke:none;" d="M1216.67 1236.33C1216.22 1236.78 1217.28 1236.72 1217.33 1236.67C1217.78 1236.22 1216.72 1236.28 1216.67 1236.33z"/>
<path style="fill:#84c1a0; stroke:none;" d="M795 1237L796 1238L795 1237z"/>
<path style="fill:#67d1a0; stroke:none;" d="M1215 1237L1216 1238L1215 1237z"/>
<path style="fill:#81b398; stroke:none;" d="M302 1238L303 1239L302 1238z"/>
<path style="fill:#13945b; stroke:none;" d="M1210 1238C1207.88 1239.92 1206.39 1241.51 1205 1244C1208.63 1243.42 1211.26 1241.95 1210 1238z"/>
<path style="fill:#218354; stroke:none;" d="M1211 1241C1212.46 1239.9 1212.9 1239.46 1214 1238C1211.64 1238.48 1211.48 1238.64 1211 1241z"/>
<path style="fill:#80cba4; stroke:none;" d="M1214 1238L1215 1239L1214 1238z"/>
<path style="fill:#86c5a6; stroke:none;" d="M303 1239L304 1240L303 1239z"/>
<path style="fill:#a1e2c2; stroke:none;" d="M794 1239L795 1240L794 1239z"/>
<path style="fill:#84d0a9; stroke:none;" d="M1213 1239L1214 1240L1213 1239z"/>
<path style="fill:#72d3a0; stroke:none;" d="M304 1240L305 1241L304 1240z"/>
<path style="fill:#17824c; stroke:none;" d="M305 1240C306.103 1241.46 306.543 1241.9 308 1243C307.518 1240.64 307.364 1240.48 305 1240z"/>
<path style="fill:#238355; stroke:none;" d="M741 1240C740.499 1242.69 740.117 1245.27 740 1248C741.433 1245.3 742.038 1242.9 741 1240z"/>
<path style="fill:#b1eecf; stroke:none;" d="M742.333 1240.67C742.278 1240.72 742.222 1241.78 742.667 1241.33C742.722 1241.28 742.778 1240.22 742.333 1240.67z"/>
<path style="fill:#d3ffea; stroke:none;" d="M304 1241C305.261 1244.81 308.368 1246.57 312 1248C309.725 1244.84 307.454 1242.8 304 1241z"/>
<path style="fill:#6cd49f; stroke:none;" d="M305 1241L306 1242L305 1241z"/>
<path style="fill:#b0e1c4; stroke:none;" d="M793 1241L794 1242L793 1241z"/>
<path style="fill:#3c9567; stroke:none;" d="M308.333 1242.67C308.278 1242.72 308.222 1243.78 308.667 1243.33C308.722 1243.28 308.778 1242.22 308.333 1242.67z"/>
<path style="fill:#ebfff0; stroke:none;" d="M790 1242L790 1248C791.573 1246.05 792.291 1244.41 793 1242L790 1242z"/>
<path style="fill:#42996c; stroke:none;" d="M793.333 1242.67C793.278 1242.72 793.222 1243.78 793.667 1243.33C793.722 1243.28 793.778 1242.22 793.333 1242.67z"/>
<path style="fill:#a3eccf; stroke:none;" d="M1209 1243L1210 1244L1209 1243z"/>
<path style="fill:#5fa984; stroke:none;" d="M741 1244L742 1245L741 1244z"/>
<path style="fill:#6acc9d; stroke:none;" d="M792 1244L793 1245L792 1244z"/>
<path style="fill:#1a8d4c; stroke:none;" d="M1203 1244L1203 1248L1206 1247L1206 1245L1205 1244L1203 1244z"/>
<path style="fill:#368b64; stroke:none;" d="M1205 1244L1206 1246C1207.64 1245.45 1207.45 1245.64 1208 1244L1205 1244z"/>
<path style="fill:#dffffb; stroke:none;" d="M1208 1244L1205 1247C1207.51 1247.57 1208.22 1246.85 1210 1245L1208 1244z"/>
<path style="fill:#8dd7b2; stroke:none;" d="M741 1245L742 1246L741 1245z"/>
<path style="fill:#75cca2; stroke:none;" d="M791 1246L792 1247L791 1246z"/>
<path style="fill:#abe2c5; stroke:none;" d="M1206 1246L1207 1247L1206 1246z"/>
<path style="fill:#5ebf89; stroke:none;" d="M1204 1247L1205 1248L1204 1247z"/>
<path style="fill:#67a07f; stroke:none;" d="M313 1248L314 1249L313 1248z"/>
<path style="fill:#139056; stroke:none;" d="M314 1248C315.795 1250.51 317.493 1252.2 320 1254C319.347 1250.14 317.744 1248.92 314 1248z"/>
<path style="fill:#0f954e; stroke:none;" d="M322 1249C325.367 1253.62 330.048 1259.53 336 1260C334.814 1257.28 333.569 1254.93 333 1252L336 1252L336 1248L332 1248L332 1252C328.315 1250.97 325.925 1248.61 322 1249z"/>
<path style="fill:#b1dcc1; stroke:none;" d="M740.333 1248.67C740.278 1248.72 740.222 1249.78 740.667 1249.33C740.722 1249.28 740.778 1248.22 740.333 1248.67z"/>
<path style="fill:#70c99d; stroke:none;" d="M790 1248L791 1249L790 1248z"/>
<path style="fill:#6bb698; stroke:none;" d="M1203 1248L1204 1249L1203 1248z"/>
<path style="fill:#e1fff7; stroke:none;" d="M1201 1252L1205 1252C1205 1249.91 1204.86 1250.28 1207 1250C1204.13 1248.63 1203.02 1249.68 1201 1252z"/>
<path style="fill:#63b489; stroke:none;" d="M314 1249L315 1250L314 1249z"/>
<path style="fill:#5bbf99; stroke:none;" d="M1202 1249L1203 1250L1202 1249z"/>
<path style="fill:#e3fff4; stroke:none;" d="M314 1250C314.192 1253.95 316.301 1255.1 320 1256C318.205 1253.49 316.508 1251.8 314 1250z"/>
<path style="fill:#6db792; stroke:none;" d="M315 1250L316 1251L315 1250z"/>
<path style="fill:#e9fffa; stroke:none;" d="M740 1250L737 1260C740.501 1257.68 741.586 1253.9 740 1250z"/>
<path style="fill:#78c49e; stroke:none;" d="M789 1250L790 1251L789 1250z"/>
<path style="fill:#88c3ad; stroke:none;" d="M1201 1250L1202 1251L1201 1250z"/>
<path style="fill:#5dbd8d; stroke:none;" d="M316 1251L317 1252L316 1251z"/>
<path style="fill:#63b68a; stroke:none;" d="M739 1251L740 1252L739 1251z"/>
<path style="fill:#7cd1b2; stroke:none;" d="M1200 1251L1201 1252L1200 1251z"/>
<path style="fill:#84c3a8; stroke:none;" d="M317 1252L318 1253L317 1252z"/>
<path style="fill:#b7fdd9; stroke:none;" d="M739.333 1252.67C739.278 1252.72 739.222 1253.78 739.667 1253.33C739.722 1253.28 739.778 1252.22 739.333 1252.67z"/>
<path style="fill:#6fd6a7; stroke:none;" d="M788 1252L789 1253L788 1252z"/>
<path style="fill:#97ceb1; stroke:none;" d="M1199 1252L1200 1253L1199 1252z"/>
<path style="fill:#89cbaf; stroke:none;" d="M318 1253L319 1254L318 1253z"/>
<path style="fill:#8fe4ba; stroke:none;" d="M1198 1253L1199 1254L1198 1253z"/>
<path style="fill:#a9cac1; stroke:none;" d="M319 1254L320 1255L319 1254z"/>
<path style="fill:#2d8a5e; stroke:none;" d="M737 1254L736 1259C737.318 1257.07 737.651 1256.23 737 1254z"/>
<path style="fill:#6dbb93; stroke:none;" d="M738 1254L739 1255L738 1254z"/>
<path style="fill:#62d2a0; stroke:none;" d="M787 1254L788 1255L787 1254z"/>
<path style="fill:#4d9d78; stroke:none;" d="M1196 1254L1197 1255L1196 1254z"/>
<path style="fill:#ace2ca; stroke:none;" d="M1197 1254L1198 1255L1197 1254z"/>
<path style="fill:#93e1b1; stroke:none;" d="M320 1255L321 1256L320 1255z"/>
<path style="fill:#47b284; stroke:none;" d="M1195 1255L1196 1256L1195 1255z"/>
<path style="fill:#b5d9bf; stroke:none;" d="M321 1256L322 1257L321 1256z"/>
<path style="fill:#449d6f; stroke:none;" d="M786.333 1256.67C786.278 1256.72 786.222 1257.78 786.667 1257.33C786.722 1257.28 786.778 1256.22 786.333 1256.67z"/>
<path style="fill:#66a185; stroke:none;" d="M1194 1256L1195 1257L1194 1256z"/>
<path style="fill:#8ec9ad; stroke:none;" d="M737 1257L738 1258L737 1257z"/>
<path style="fill:#bbd1bc; stroke:none;" d="M785 1257L786 1258L785 1257z"/>
<path style="fill:#67bc95; stroke:none;" d="M1193 1257L1194 1258L1193 1257z"/>
<path style="fill:#63b18a; stroke:none;" d="M324 1258L325 1259L324 1258z"/>
<path style="fill:#179555; stroke:none;" d="M732 1258L731 1264L733 1264C733.464 1261.83 734.105 1260.04 735 1258L732 1258z"/>
<path style="fill:#128449; stroke:none;" d="M735 1258C733.133 1261.19 731.957 1264.43 731 1268C733.633 1265.31 735.925 1261.81 735 1258z"/>
<path style="fill:#87c2a6; stroke:none;" d="M1192 1258L1193 1259L1192 1258z"/>
<path style="fill:#5dc495; stroke:none;" d="M325 1259L326 1260L325 1259z"/>
<path style="fill:#79af98; stroke:none;" d="M736 1259L737 1260L736 1259z"/>
<path style="fill:#96d3b1; stroke:none;" d="M784 1259L785 1260L784 1259z"/>
<path style="fill:#79d4ab; stroke:none;" d="M1191 1259L1192 1260L1191 1259z"/>
<path style="fill:#8dc3a9; stroke:none;" d="M326 1260L327 1261L326 1260z"/>
<path style="fill:#199050; stroke:none;" d="M785 1261C786.717 1262.84 787.566 1263.35 790 1264L790 1260C788.122 1260.18 786.8 1260.44 785 1261z"/>
<path style="fill:#25955b; stroke:none;" d="M1186 1260C1187.73 1261.05 1187.93 1261.05 1190 1261C1188.49 1260.32 1187.69 1260.17 1186 1260z"/>
<path style="fill:#a3d8bc; stroke:none;" d="M1190 1260L1191 1261L1190 1260z"/>
<path style="fill:#8dd9b3; stroke:none;" d="M327 1261L328 1262L327 1261z"/>
<path style="fill:#52c489; stroke:none;" d="M735 1261L736 1262L735 1261z"/>
<path style="fill:#94baad; stroke:none;" d="M783 1261L784 1262L783 1261z"/>
<path style="fill:#99e3be; stroke:none;" d="M1189 1261L1190 1262L1189 1261z"/>
<path style="fill:#effff7; stroke:none;" d="M327 1262C328.567 1265.58 330.083 1267.34 334 1268C331.95 1265.34 329.909 1263.69 327 1262z"/>
<path style="fill:#91ccac; stroke:none;" d="M328 1262L329 1263L328 1262z"/>
<path style="fill:#56af81; stroke:none;" d="M329 1262L330 1263L329 1262z"/>
<path style="fill:#4abc81; stroke:none;" d="M734.333 1262.67C734.278 1262.72 734.222 1263.78 734.667 1263.33C734.722 1263.28 734.778 1262.22 734.333 1262.67z"/>
<path style="fill:#bcffea; stroke:none;" d="M735.333 1262.67C735.278 1262.72 735.222 1263.78 735.667 1263.33C735.722 1263.28 735.778 1262.22 735.333 1262.67z"/>
<path style="fill:#84aa9d; stroke:none;" d="M782.333 1262.67C782.278 1262.72 782.222 1263.78 782.667 1263.33C782.722 1263.28 782.778 1262.22 782.333 1262.67z"/>
<path style="fill:#3b6a5a; stroke:none;" d="M783.333 1262.67C783.278 1262.72 783.222 1263.78 783.667 1263.33C783.722 1263.28 783.778 1262.22 783.333 1262.67z"/>
<path style="fill:#539a72; stroke:none;" d="M1187 1262L1188 1263L1187 1262z"/>
<path style="fill:#92ebbd; stroke:none;" d="M329 1263L330 1264L329 1263z"/>
<path style="fill:#56bf87; stroke:none;" d="M1186 1263L1187 1264L1186 1263z"/>
<path style="fill:#67a689; stroke:none;" d="M331 1264L332 1265L331 1264z"/>
<path style="fill:#1a8e5d; stroke:none;" d="M332 1264C333.33 1265.75 334.247 1266.67 336 1268C335.03 1265.51 334.525 1264.85 332 1264z"/>
<path style="fill:#f0fff8; stroke:none;" d="M734 1264L732 1270C733.582 1272.06 733.826 1273.39 734 1276C736.11 1272.03 736.332 1267.9 734 1264z"/>
<path style="fill:#c4efd4; stroke:none;" d="M780 1264L780 1266C781.635 1265.46 781.455 1265.64 782 1264L780 1264z"/>
<path style="fill:#436c5e; stroke:none;" d="M1184 1264L1185 1265L1184 1264z"/>
<path style="fill:#8caca1; stroke:none;" d="M1185 1264L1186 1265L1185 1264z"/>
<path style="fill:#6bc69d; stroke:none;" d="M332 1265L333 1266L332 1265z"/>
<path style="fill:#70c099; stroke:none;" d="M733 1265L734 1266L733 1265z"/>
<path style="fill:#80c3a2; stroke:none;" d="M333 1266L334 1267L333 1266z"/>
<path style="fill:#64bb90; stroke:none;" d="M780 1266L781 1267L780 1266z"/>
<path style="fill:#278759; stroke:none;" d="M1180 1266L1181 1268C1182.64 1267.45 1182.45 1267.64 1183 1266L1180 1266z"/>
<path style="fill:#acdac0; stroke:none;" d="M1183 1266L1184 1267L1183 1266z"/>
<path style="fill:#8fdbb7; stroke:none;" d="M334 1267L335 1268L334 1267z"/>
<path style="fill:#7fdcaf; stroke:none;" d="M732 1267L733 1268L732 1267z"/>
<path style="fill:#9ed9b9; stroke:none;" d="M779 1267L780 1268L779 1267z"/>
<path style="fill:#5ea07c; stroke:none;" d="M731 1268L732 1269L731 1268z"/>
<path style="fill:#d3fde5; stroke:none;" d="M777 1268L777 1270C778.635 1269.46 778.455 1269.64 779 1268L777 1268z"/>
<path style="fill:#549c77; stroke:none;" d="M1180 1268L1181 1269L1180 1268z"/>
<path style="fill:#eafff7; stroke:none;" d="M1181 1268L1178 1272C1180.48 1271.32 1181.67 1270.61 1181 1268z"/>
<path style="fill:#a4e4c1; stroke:none;" d="M336 1269L337 1270L336 1269z"/>
<path style="fill:#5cb589; stroke:none;" d="M337 1269L338 1270L337 1269z"/>
<path style="fill:#a1e3bf; stroke:none;" d="M731 1269L732 1270L731 1269z"/>
<path style="fill:#56ae84; stroke:none;" d="M778 1269L779 1270L778 1269z"/>
<path style="fill:#53bf8d; stroke:none;" d="M1179 1269L1180 1270L1179 1269z"/>
<path style="fill:#7eb69b; stroke:none;" d="M338 1270L339 1271L338 1270z"/>
<path style="fill:#8fb79c; stroke:none;" d="M730 1270L731 1271L730 1270z"/>
<path style="fill:#88e1b5; stroke:none;" d="M777 1270L778 1271L777 1270z"/>
<path style="fill:#1f8553; stroke:none;" d="M1175 1272C1176.58 1271.32 1176.78 1271.2 1178 1270C1175.91 1270.41 1175.98 1270.22 1175 1272z"/>
<path style="fill:#8ec5a8; stroke:none;" d="M1178 1270L1179 1271L1178 1270z"/>
<path style="fill:#74c6a2; stroke:none;" d="M339 1271L340 1272L339 1271z"/>
<path style="fill:#95e1bb; stroke:none;" d="M1177 1271L1178 1272L1177 1271z"/>
<path style="fill:#adcfbf; stroke:none;" d="M340 1272L341 1273L340 1272z"/>
<path style="fill:#c4ffe3; stroke:none;" d="M729.333 1272.67C729.278 1272.72 729.222 1273.78 729.667 1273.33C729.722 1273.28 729.778 1272.22 729.333 1272.67z"/>
<path style="fill:#cbfff2; stroke:none;" d="M774 1272L774 1274C775.635 1273.45 775.455 1273.64 776 1272L774 1272z"/>
<path style="fill:#4a8567; stroke:none;" d="M1174 1272L1174 1274C1175.64 1273.46 1175.45 1273.64 1176 1272L1174 1272z"/>
<path style="fill:#aae6ca; stroke:none;" d="M341 1273L342 1274L341 1273z"/>
<path style="fill:#55ab84; stroke:none;" d="M342 1273L343 1274L342 1273z"/>
<path style="fill:#66cc9d; stroke:none;" d="M728 1273L729 1274L728 1273z"/>
<path style="fill:#6bb992; stroke:none;" d="M775 1273L776 1274L775 1273z"/>
<path style="fill:#71ae8d; stroke:none;" d="M343 1274L344 1275L343 1274z"/>
<path style="fill:#89e4b8; stroke:none;" d="M774 1274L775 1275L774 1274z"/>
<path style="fill:#8b9c8c; stroke:none;" d="M1173 1274L1174 1275L1173 1274z"/>
<path style="fill:#6bc89b; stroke:none;" d="M344 1275L345 1276L344 1275z"/>
<path style="fill:#9eeec9; stroke:none;" d="M727 1275L728 1276L727 1275z"/>
<path style="fill:#beead1; stroke:none;" d="M773 1275L774 1276L773 1275z"/>
<path style="fill:#97caab; stroke:none;" d="M1172 1275L1173 1276L1172 1275z"/>
<path style="fill:#90c9a9; stroke:none;" d="M345 1276L346 1277L345 1276z"/>
<path style="fill:#96c6ac; stroke:none;" d="M726 1276L727 1277L726 1276z"/>
<path style="fill:#dffff8; stroke:none;" d="M770 1276L771 1278C772.635 1277.45 772.455 1277.64 773 1276L770 1276z"/>
<path style="fill:#3e9b6f; stroke:none;" d="M1169 1276L1169 1278C1170.64 1277.45 1170.45 1277.64 1171 1276L1169 1276z"/>
<path style="fill:#b0dcc5; stroke:none;" d="M1171 1276L1172 1277L1171 1276z"/>
<path style="fill:#97e5bb; stroke:none;" d="M346 1277L347 1278L346 1277z"/>
<path style="fill:#4cb07e; stroke:none;" d="M347 1277L348 1278L347 1277z"/>
<path style="fill:#68b98e; stroke:none;" d="M725 1277L726 1278L725 1277z"/>
<path style="fill:#68b28f; stroke:none;" d="M772 1277L773 1278L772 1277z"/>
<path style="fill:#eefffd; stroke:none;" d="M1168 1280L1176 1280C1173.02 1277.26 1170.89 1277.14 1168 1280z"/>
<path style="fill:#62b086; stroke:none;" d="M348 1278L349 1279L348 1278z"/>
<path style="fill:#6ecb9f; stroke:none;" d="M771 1278L772 1279L771 1278z"/>
<path style="fill:#8aa18d; stroke:none;" d="M1168 1278L1169 1279L1168 1278z"/>
<path style="fill:#52c38d; stroke:none;" d="M349 1279L350 1280L349 1279z"/>
<path style="fill:#9ed0b7; stroke:none;" d="M770 1279L771 1280L770 1279z"/>
<path style="fill:#58d794; stroke:none;" d="M1167 1279L1168 1280L1167 1279z"/>
<path style="fill:#aac4bb; stroke:none;" d="M350 1280L351 1281L350 1280z"/>
<path style="fill:#5e7c72; stroke:none;" d="M351 1280L352 1281L351 1280z"/>
<path style="fill:#d0ffeb; stroke:none;" d="M723 1280L724 1284C724.553 1281.99 724.303 1281.67 723 1280z"/>
<path style="fill:#4bbb81; stroke:none;" d="M769.333 1280.67C769.278 1280.72 769.222 1281.78 769.667 1281.33C769.722 1281.28 769.778 1280.22 769.333 1280.67z"/>
<path style="fill:#689578; stroke:none;" d="M1165 1280L1166 1281L1165 1280z"/>
<path style="fill:#c2cbba; stroke:none;" d="M1166 1280L1167 1281L1166 1280z"/>
<path style="fill:#24945c; stroke:none;" d="M352 1281C353.393 1282.43 354.233 1283.04 356 1284C354.771 1282.13 354.132 1281.78 352 1281z"/>
<path style="fill:#78d3a8; stroke:none;" d="M722 1281L723 1282L722 1281z"/>
<path style="fill:#88efb8; stroke:none;" d="M768 1281L769 1282L768 1281z"/>
<path style="fill:#4e9c74; stroke:none;" d="M1164 1281L1165 1282L1164 1281z"/>
<path style="fill:#f1fff2; stroke:none;" d="M1163 1284C1164.94 1284.89 1165.52 1285.4 1167 1287C1167.8 1285.01 1167.83 1283.15 1168 1281C1165.57 1281.65 1164.72 1282.16 1163 1284z"/>
<path style="fill:#0e884f; stroke:none;" d="M721 1282C722.402 1284.27 723.515 1284.99 726 1286C724.376 1283.96 723.357 1283.1 721 1282z"/>
<path style="fill:#f2fef0; stroke:none;" d="M760 1290C763.245 1287.71 765.709 1285.24 768 1282C763.477 1282.76 761.44 1285.81 760 1290z"/>
<path style="fill:#138f53; stroke:none;" d="M1159 1285L1163 1283C1160.59 1281.65 1159.57 1282.35 1159 1285z"/>
<path style="fill:#89c1a4; stroke:none;" d="M1163 1282L1164 1283L1163 1282z"/>
<path style="fill:#78d1a5; stroke:none;" d="M354 1283L355 1284L354 1283z"/>
<path style="fill:#82af98; stroke:none;" d="M767 1283L768 1284L767 1283z"/>
<path style="fill:#97e5bd; stroke:none;" d="M1162 1283L1163 1284L1162 1283z"/>
<path style="fill:#9adbb9; stroke:none;" d="M355 1284L356 1285L355 1284z"/>
<path style="fill:#5bbd8e; stroke:none;" d="M766 1284L767 1285L766 1284z"/>
<path style="fill:#559674; stroke:none;" d="M1160 1284L1161 1285L1160 1284z"/>
<path style="fill:#cfeedc; stroke:none;" d="M1161 1284L1160 1286L1162 1286C1162 1284.15 1162.3 1284.65 1161 1284z"/>
<path style="fill:#3fbd80; stroke:none;" d="M357 1285L358 1286L357 1285z"/>
<path style="fill:#70ba95; stroke:none;" d="M765 1285L766 1286L765 1285z"/>
<path style="fill:#62c294; stroke:none;" d="M1159 1285L1160 1286L1159 1285z"/>
<path style="fill:#86c7a5; stroke:none;" d="M358 1286L359 1287L358 1286z"/>
<path style="fill:#73cd9a; stroke:none;" d="M764 1286L765 1287L764 1286z"/>
<path style="fill:#89cca9; stroke:none;" d="M1158 1286L1159 1287L1158 1286z"/>
<path style="fill:#93efc0; stroke:none;" d="M358.667 1287.33C358.222 1287.78 359.278 1287.72 359.333 1287.67C359.778 1287.22 358.722 1287.28 358.667 1287.33z"/>
<path style="fill:#97d5b0; stroke:none;" d="M763 1287L764 1288L763 1287z"/>
<path style="fill:#96f1c5; stroke:none;" d="M1157 1287L1158 1288L1157 1287z"/>
<path style="fill:#4e9f76; stroke:none;" d="M361 1288L362 1289L361 1288z"/>
<path style="fill:#94ebc1; stroke:none;" d="M729 1288L730 1289L729 1288z"/>
<path style="fill:#7bd9a5; stroke:none;" d="M762 1288L763 1289L762 1288z"/>
<path style="fill:#78b895; stroke:none;" d="M1155 1288L1156 1289L1155 1288z"/>
<path style="fill:#58c38d; stroke:none;" d="M362 1289L363 1290L362 1289z"/>
<path style="fill:#9ad7b8; stroke:none;" d="M730 1289L731 1290L730 1289z"/>
<path style="fill:#84cba1; stroke:none;" d="M761 1289L762 1290L761 1289z"/>
<path style="fill:#76cf9f; stroke:none;" d="M1154 1289L1155 1290L1154 1289z"/>
<path style="fill:#a6d7b9; stroke:none;" d="M363 1290L364 1291L363 1290z"/>
<path style="fill:#0f9b54; stroke:none;" d="M366 1293L375 1298C374.807 1292.07 371.066 1287.68 366 1293z"/>
<path style="fill:#14854f; stroke:none;" d="M730 1290C731.397 1293.2 732.649 1294.91 736 1296C734.205 1293.49 732.507 1291.8 730 1290z"/>
<path style="fill:#88dab2; stroke:none;" d="M731 1290L732 1291L731 1290z"/>
<path style="fill:#72ce9d; stroke:none;" d="M760 1290L761 1291L760 1290z"/>
<path style="fill:#54a680; stroke:none;" d="M1152 1290L1153 1291L1152 1290z"/>
<path style="fill:#a0d5b9; stroke:none;" d="M1153 1290L1154 1291L1153 1290z"/>
<path style="fill:#95cdb0; stroke:none;" d="M732 1291L733 1292L732 1291z"/>
<path style="fill:#7cba95; stroke:none;" d="M759 1291L760 1292L759 1291z"/>
<path style="fill:#82d0a9; stroke:none;" d="M733 1292L734 1293L733 1292z"/>
<path style="fill:#33915f; stroke:none;" d="M758.333 1292.67C758.278 1292.72 758.222 1293.78 758.667 1293.33C758.722 1293.28 758.778 1292.22 758.333 1292.67z"/>
<path style="fill:#1d7e51; stroke:none;" d="M1146 1295L1150 1293C1147.69 1292.44 1147.24 1293 1146 1295z"/>
<path style="fill:#8fccab; stroke:none;" d="M1150 1292L1151 1293L1150 1292z"/>
<path style="fill:#7ed1a7; stroke:none;" d="M367 1293L368 1294L367 1293z"/>
<path style="fill:#89c9a7; stroke:none;" d="M734 1293L735 1294L734 1293z"/>
<path style="fill:#67af89; stroke:none;" d="M757 1293L758 1294L757 1293z"/>
<path style="fill:#53a37c; stroke:none;" d="M1148 1293L1149 1294L1148 1293z"/>
<path style="fill:#c1fff3; stroke:none;" d="M368 1294L368 1296L370 1296C369.455 1294.36 369.635 1294.55 368 1294z"/>
<path style="fill:#4eb685; stroke:none;" d="M369 1294L370 1295L369 1294z"/>
<path style="fill:#8ad4af; stroke:none;" d="M735 1294L736 1295L735 1294z"/>
<path style="fill:#72ad91; stroke:none;" d="M1147 1294L1148 1295L1147 1294z"/>
<path style="fill:#62cd99; stroke:none;" d="M370 1295L371 1296L370 1295z"/>
<path style="fill:#e4e4e4; stroke:none;" d="M736.667 1295.33C736.222 1295.78 737.278 1295.72 737.333 1295.67C737.778 1295.22 736.722 1295.28 736.667 1295.33z"/>
<path style="fill:#82d7ae; stroke:none;" d="M1146 1295L1147 1296L1146 1295z"/>
<path style="fill:#a6d0b8; stroke:none;" d="M371 1296L372 1297L371 1296z"/>
<path style="fill:#40895b; stroke:none;" d="M736 1296L736 1298L738 1298C737.455 1296.36 737.635 1296.55 736 1296z"/>
<path style="fill:#92caa5; stroke:none;" d="M737 1296L738 1297L737 1296z"/>
<path style="fill:#74ebab; stroke:none;" d="M753 1296L754 1297L753 1296z"/>
<path style="fill:#208b53; stroke:none;" d="M1137 1300C1140.21 1299.36 1141.84 1298.46 1144 1296C1140.78 1296.34 1138.64 1297.15 1137 1300z"/>
<path style="fill:#6b977e; stroke:none;" d="M1144 1296L1145 1297L1144 1296z"/>
<path style="fill:#e5fff3; stroke:none;" d="M1145 1296C1141.8 1298.54 1138.58 1300.83 1136 1304C1139.51 1303.16 1142.72 1301.5 1146 1300C1145.81 1298.47 1145.63 1297.37 1145 1296z"/>
<path style="fill:#58be8e; stroke:none;" d="M373 1297L374 1298L373 1297z"/>
<path style="fill:#8ac1a1; stroke:none;" d="M738 1297L739 1298L738 1297z"/>
<path style="fill:#36ad6d; stroke:none;" d="M752.667 1297.33C752.222 1297.78 753.278 1297.72 753.333 1297.67C753.778 1297.22 752.722 1297.28 752.667 1297.33z"/>
<path style="fill:#7dba98; stroke:none;" d="M1143 1297L1144 1298L1143 1297z"/>
<path style="fill:#ddffec; stroke:none;" d="M371 1299C372.506 1299.68 373.315 1299.83 375 1300C373.276 1298.94 373.079 1298.96 371 1299z"/>
<path style="fill:#87cda8; stroke:none;" d="M374 1298L375 1299L374 1298z"/>
<path style="fill:#77cca2; stroke:none;" d="M739 1298L740 1299L739 1298z"/>
<path style="fill:#e3fffd; stroke:none;" d="M747 1298C748.81 1299.24 748.777 1299.18 751 1299C749.494 1298.32 748.685 1298.17 747 1298z"/>
<path style="fill:#53b88c; stroke:none;" d="M751 1298L752 1299L751 1298z"/>
<path style="fill:#9ff6cb; stroke:none;" d="M375 1299L376 1300L375 1299z"/>
<path style="fill:#8cc2a8; stroke:none;" d="M740 1299L741 1300L740 1299z"/>
<path style="fill:#5cbd8a; stroke:none;" d="M1140 1299L1141 1300L1140 1299z"/>
<path style="fill:#e5fff8; stroke:none;" d="M375 1301C377.644 1304.18 379.825 1305.71 384 1306C381.891 1303.69 379.476 1301.92 377 1300L375 1301z"/>
<path style="fill:#85ba9c; stroke:none;" d="M377 1300L378 1301L377 1300z"/>
<path style="fill:#128b56; stroke:none;" d="M378 1300C379.904 1301.88 381.534 1302.95 384 1304C382.392 1301.43 380.944 1300.71 378 1300z"/>
<path style="fill:#88c3a3; stroke:none;" d="M741 1300L742 1301L741 1300z"/>
<path style="fill:#7de4b7; stroke:none;" d="M748 1300L749 1301L748 1300z"/>
<path style="fill:#3c8361; stroke:none;" d="M1136 1302C1137.58 1301.32 1137.78 1301.2 1139 1300C1136.91 1300.41 1136.98 1300.22 1136 1302z"/>
<path style="fill:#98d3b5; stroke:none;" d="M1139 1300L1140 1301L1139 1300z"/>
<path style="fill:#92dfb5; stroke:none;" d="M378 1301L379 1302L378 1301z"/>
<path style="fill:#41a775; stroke:none;" d="M379 1301L380 1302L379 1301z"/>
<path style="fill:#9cb7a6; stroke:none;" d="M742 1301L743 1302L742 1301z"/>
<path style="fill:#66ad8f; stroke:none;" d="M747 1301L748 1302L747 1301z"/>
<path style="fill:#65b791; stroke:none;" d="M380 1302L381 1303L380 1302z"/>
<path style="fill:#7decb9; stroke:none;" d="M743 1302C744.248 1302.68 744.549 1302.75 746 1303C744.752 1302.32 744.452 1302.25 743 1302z"/>
<path style="fill:#9bc1b4; stroke:none;" d="M1136 1302L1137 1303L1136 1302z"/>
<path style="fill:#6cd7a9; stroke:none;" d="M381 1303L382 1304L381 1303z"/>
<path style="fill:#45ae75; stroke:none;" d="M1134 1303L1135 1304L1134 1303z"/>
<path style="fill:#95e0b5; stroke:none;" d="M1135 1303L1136 1304L1135 1303z"/>
<path style="fill:#6fa38c; stroke:none;" d="M383 1304L384 1305L383 1304z"/>
<path style="fill:#91ba9c; stroke:none;" d="M1133 1304L1134 1305L1133 1304z"/>
<path style="fill:#52c584; stroke:none;" d="M384 1305L385 1306L384 1305z"/>
<path style="fill:#129755; stroke:none;" d="M1123 1312L1127 1309C1125.49 1308.32 1124.69 1308.17 1123 1308C1123.55 1306.36 1123.36 1306.55 1125 1306C1119.93 1304.61 1120.09 1309.23 1123 1312z"/>
<path style="fill:#94dfb6; stroke:none;" d="M1132 1305L1133 1306L1132 1305z"/>
<path style="fill:#c9fff0; stroke:none;" d="M384 1306C386.11 1309.54 388.927 1311.41 393 1312C390.378 1309.27 387.491 1307.45 384 1306z"/>
<path style="fill:#22935b; stroke:none;" d="M386 1306C388.59 1308.5 390.405 1309.34 394 1309C391.46 1306.81 389.335 1306.22 386 1306z"/>
<path style="fill:#79b090; stroke:none;" d="M1130 1306L1131 1307L1130 1306z"/>
<path style="fill:#65be8e; stroke:none;" d="M387 1307L388 1308L387 1307z"/>
<path style="fill:#83d5ad; stroke:none;" d="M1129 1307L1130 1308L1129 1307z"/>
<path style="fill:#a7d9be; stroke:none;" d="M388 1308L389 1309L388 1308z"/>
<path style="fill:#6aab8b; stroke:none;" d="M1127 1308L1128 1309L1127 1308z"/>
<path style="fill:#7ed9ad; stroke:none;" d="M1126 1309L1127 1310L1126 1309z"/>
<path style="fill:#619f7a; stroke:none;" d="M1124 1310L1125 1311L1124 1310z"/>
<path style="fill:#bed5c1; stroke:none;" d="M1125 1310L1126 1311L1125 1310z"/>
<path style="fill:#5bcc94; stroke:none;" d="M393 1311L394 1312L393 1311z"/>
<path style="fill:#76d6a6; stroke:none;" d="M1123 1311L1124 1312L1123 1311z"/>
<path style="fill:#d9fff3; stroke:none;" d="M392 1313C394.262 1314.96 396.03 1315.61 399 1316C396.687 1313.87 395.13 1313.06 392 1313z"/>
<path style="fill:#abddc4; stroke:none;" d="M394 1312L395 1313L394 1312z"/>
<path style="fill:#1e8e5c; stroke:none;" d="M395 1312C398.699 1315.29 402.949 1318.88 408 1319C406.059 1317.6 404.27 1316.78 402 1316L404 1315C401.131 1313.3 398.308 1312.41 395 1312z"/>
<path style="fill:#477062; stroke:none;" d="M1120 1312L1121 1313L1120 1312z"/>
<path style="fill:#88a89d; stroke:none;" d="M1121 1312L1122 1313L1121 1312z"/>
<path style="fill:#62c398; stroke:none;" d="M396 1313L397 1314L396 1313z"/>
<path style="fill:#9ee1c0; stroke:none;" d="M397 1314L398 1315L397 1314z"/>
<path style="fill:#5ea583; stroke:none;" d="M398 1314L399 1315L398 1314z"/>
<path style="fill:#1b8c56; stroke:none;" d="M1113 1314L1113 1318L1118 1315C1116.23 1314.22 1114.96 1314.09 1113 1314z"/>
<path style="fill:#66a280; stroke:none;" d="M1118 1314L1119 1315L1118 1314z"/>
<path style="fill:#65bd95; stroke:none;" d="M399 1315L400 1316L399 1315z"/>
<path style="fill:#8fd5b0; stroke:none;" d="M1117 1315L1118 1316L1117 1315z"/>
<path style="fill:#aee1c2; stroke:none;" d="M400 1316L401 1317L400 1316z"/>
<path style="fill:#6fac8d; stroke:none;" d="M1115 1316L1116 1317L1115 1316z"/>
<path style="fill:#69ba91; stroke:none;" d="M402 1317L403 1318L402 1317z"/>
<path style="fill:#89e4b8; stroke:none;" d="M1114 1317L1115 1318L1114 1317z"/>
<path style="fill:#178048; stroke:none;" d="M1108 1319C1109.66 1319.41 1110.24 1319.31 1112 1319C1110.19 1318.43 1109.93 1318.53 1108 1319z"/>
<path style="fill:#83b194; stroke:none;" d="M1112 1318L1113 1319L1112 1318z"/>
<path style="fill:#78d4ab; stroke:none;" d="M405 1319L406 1320L405 1319z"/>
<path style="fill:#55ab7c; stroke:none;" d="M1110 1319L1111 1320L1110 1319z"/>
<path style="fill:#a5e5c2; stroke:none;" d="M1111 1319L1112 1320L1111 1319z"/>
<path style="fill:#7abb9d; stroke:none;" d="M407 1320L408 1321L407 1320z"/>
<path style="fill:#1e9458; stroke:none;" d="M408 1320C411.351 1323.65 415.58 1325.83 420 1328C420.986 1326.52 421 1326.8 421 1325C419.311 1323.75 418.246 1322.69 417 1321C413.915 1321.1 411.063 1320.35 408 1320z"/>
<path style="fill:#a3ceb2; stroke:none;" d="M1109 1320L1110 1321L1109 1320z"/>
<path style="fill:#a6f8d2; stroke:none;" d="M407.667 1321.33C407.222 1321.78 408.278 1321.72 408.333 1321.67C408.778 1321.22 407.722 1321.28 407.667 1321.33z"/>
<path style="fill:#5dbf8c; stroke:none;" d="M1107 1321L1108 1322L1107 1321z"/>
<path style="fill:#cffff0; stroke:none;" d="M408 1322C409.543 1323.27 410.048 1323.49 412 1324C410.542 1322.92 409.752 1322.56 408 1322z"/>
<path style="fill:#7dbe9e; stroke:none;" d="M410 1322L411 1323L410 1322z"/>
<path style="fill:#689e84; stroke:none;" d="M1105 1322L1106 1323L1105 1322z"/>
<path style="fill:#a4dac0; stroke:none;" d="M1106 1322L1107 1323L1106 1322z"/>
<path style="fill:#75bd98; stroke:none;" d="M1104 1323L1105 1324L1104 1323z"/>
<path style="fill:#d9fff4; stroke:none;" d="M412 1324C412.98 1325.78 412.908 1325.59 415 1326C413.861 1324.98 413.388 1324.69 412 1324z"/>
<path style="fill:#96d2b9; stroke:none;" d="M413 1324L414 1325L413 1324z"/>
<path style="fill:#217c50; stroke:none;" d="M1099 1326C1100.58 1325.32 1100.78 1325.2 1102 1324C1099.91 1324.41 1099.98 1324.22 1099 1326z"/>
<path style="fill:#64ac86; stroke:none;" d="M1102 1324L1103 1325L1102 1324z"/>
<path style="fill:#8dd8b1; stroke:none;" d="M1101 1325L1102 1326L1101 1325z"/>
<path style="fill:#49b67d; stroke:none;" d="M416.667 1326.33C416.222 1326.78 417.278 1326.72 417.333 1326.67C417.778 1326.22 416.722 1326.28 416.667 1326.33z"/>
<path style="fill:#7bbc9a; stroke:none;" d="M1099 1326L1100 1327L1099 1326z"/>
<path style="fill:#9cffd0; stroke:none;" d="M416.667 1327.33C416.222 1327.78 417.278 1327.72 417.333 1327.67C417.778 1327.22 416.722 1327.28 416.667 1327.33z"/>
<path style="fill:#5dca91; stroke:none;" d="M418 1327L419 1328L418 1327z"/>
<path style="fill:#43b47e; stroke:none;" d="M1097 1327L1098 1328L1097 1327z"/>
<path style="fill:#96f2c3; stroke:none;" d="M1098 1327L1099 1328L1098 1327z"/>
<path style="fill:#84b69d; stroke:none;" d="M420 1328L421 1329L420 1328z"/>
<path style="fill:#4b7b61; stroke:none;" d="M1094.67 1328.33C1094.22 1328.78 1095.28 1328.72 1095.33 1328.67C1095.78 1328.22 1094.72 1328.28 1094.67 1328.33z"/>
<path style="fill:#e0fff0; stroke:none;" d="M1088 1333C1092.33 1334.41 1095.46 1331.28 1098 1328L1088 1333z"/>
<path style="fill:#94dfbe; stroke:none;" d="M421 1329L422 1330L421 1329z"/>
<path style="fill:#4bb182; stroke:none;" d="M422 1329L423 1330L422 1329z"/>
<path style="fill:#6ab08b; stroke:none;" d="M1094 1329L1095 1330L1094 1329z"/>
<path style="fill:#e0fff6; stroke:none;" d="M419 1331C421.052 1331.87 422.747 1331.95 425 1332C422.787 1330.61 421.609 1330.63 419 1331z"/>
<path style="fill:#87c4a5; stroke:none;" d="M423 1330L424 1331L423 1330z"/>
<path style="fill:#18854e; stroke:none;" d="M424 1330C426.01 1331.47 426.522 1331.46 429 1331C427.231 1330.22 425.964 1330.09 424 1330z"/>
<path style="fill:#59a17b; stroke:none;" d="M1092 1330L1093 1331L1092 1330z"/>
<path style="fill:#89dfb2; stroke:none;" d="M1091 1331L1092 1332L1091 1331z"/>
<path style="fill:#5d9d7b; stroke:none;" d="M427 1332L428 1333L427 1332z"/>
<path style="fill:#1c8b56; stroke:none;" d="M428 1332C431.084 1335.34 434.832 1337.26 439 1339C436.728 1335 432.521 1332.62 428 1332z"/>
<path style="fill:#96bba9; stroke:none;" d="M1089 1332L1090 1333L1089 1332z"/>
<path style="fill:#82d5ab; stroke:none;" d="M428 1333L429 1334L428 1333z"/>
<path style="fill:#40c286; stroke:none;" d="M1087 1333L1088 1334L1087 1333z"/>
<path style="fill:#83bea0; stroke:none;" d="M430 1334L431 1335L430 1334z"/>
<path style="fill:#69a282; stroke:none;" d="M1085 1334L1086 1335L1085 1334z"/>
<path style="fill:#a8d9bc; stroke:none;" d="M1086 1334L1087 1335L1086 1334z"/>
<path style="fill:#85d3ac; stroke:none;" d="M1084 1335L1085 1336L1084 1335z"/>
<path style="fill:#a3d8ba; stroke:none;" d="M433 1336L434 1337L433 1336z"/>
<path style="fill:#88d0ab; stroke:none;" d="M1082 1336L1083 1337L1082 1336z"/>
<path style="fill:#80cca5; stroke:none;" d="M435 1337L436 1338L435 1337z"/>
<path style="fill:#8feec2; stroke:none;" d="M1080.67 1337.33C1080.22 1337.78 1081.28 1337.72 1081.33 1337.67C1081.78 1337.22 1080.72 1337.28 1080.67 1337.33z"/>
<path style="fill:#ecfbf4; stroke:none;" d="M436 1338C436.557 1340.79 438.765 1340.31 436 1341L443 1344C441.321 1340.77 439.313 1339.47 436 1338z"/>
<path style="fill:#6bae8d; stroke:none;" d="M437 1338L438 1339L437 1338z"/>
<path style="fill:#38a473; stroke:none;" d="M439.333 1338.67C439.278 1338.72 439.222 1339.78 439.667 1339.33C439.722 1339.28 439.778 1338.22 439.333 1338.67z"/>
<path style="fill:#67a985; stroke:none;" d="M1078 1338L1079 1339L1078 1338z"/>
<path style="fill:#91e4ba; stroke:none;" d="M1077 1339L1078 1340L1077 1339z"/>
<path style="fill:#5ea180; stroke:none;" d="M441 1340L442 1341L441 1340z"/>
<path style="fill:#2d8d5f; stroke:none;" d="M442 1340L443 1342C444.635 1341.45 444.455 1341.64 445 1340L442 1340z"/>
<path style="fill:#11884e; stroke:none;" d="M444 1341C445.81 1342.24 445.777 1342.18 448 1342C446.19 1340.76 446.223 1340.82 444 1341z"/>
<path style="fill:#92d1b2; stroke:none;" d="M1075 1340L1076 1341L1075 1340z"/>
<path style="fill:#d1fff1; stroke:none;" d="M1076 1340L1074 1341C1075.48 1341.79 1076.8 1341.87 1076 1340z"/>
<path style="fill:#97eac0; stroke:none;" d="M442 1341L443 1342L442 1341z"/>
<path style="fill:#79d5ac; stroke:none;" d="M1073 1341L1074 1342L1073 1341z"/>
<path style="fill:#ceffe9; stroke:none;" d="M443 1342C443.98 1343.78 443.908 1343.59 446 1344C444.861 1342.98 444.388 1342.69 443 1342z"/>
<path style="fill:#8cdfb5; stroke:none;" d="M444 1342L445 1343L444 1342z"/>
<path style="fill:#56ba84; stroke:none;" d="M1070.67 1342.33C1070.22 1342.78 1071.28 1342.72 1071.33 1342.67C1071.78 1342.22 1070.72 1342.28 1070.67 1342.33z"/>
<path style="fill:#63daa2; stroke:none;" d="M446 1343L447 1344L446 1343z"/>
<path style="fill:#51c388; stroke:none;" d="M1069 1343L1070 1344L1069 1343z"/>
<path style="fill:#bcffea; stroke:none;" d="M1070.67 1343.33C1070.22 1343.78 1071.28 1343.72 1071.33 1343.67C1071.78 1343.22 1070.72 1343.28 1070.67 1343.33z"/>
<path style="fill:#3f9b74; stroke:none;" d="M448.667 1344.33C448.222 1344.78 449.278 1344.72 449.333 1344.67C449.778 1344.22 448.722 1344.28 448.667 1344.33z"/>
<path style="fill:#179148; stroke:none;" d="M1051 1344C1052.75 1345.25 1053.86 1345.61 1056 1346C1054.46 1344.2 1053.4 1344.17 1051 1344z"/>
<path style="fill:#659f89; stroke:none;" d="M1067 1344L1068 1345L1067 1344z"/>
<path style="fill:#b6d0c5; stroke:none;" d="M1068 1344L1069 1345L1068 1344z"/>
<path style="fill:#96f2cb; stroke:none;" d="M449 1345L450 1346L449 1345z"/>
<path style="fill:#49bf8d; stroke:none;" d="M1065 1345L1066 1346L1065 1345z"/>
<path style="fill:#e9fffc; stroke:none;" d="M1063 1347C1065.83 1348.69 1068.9 1349.88 1072 1351L1072 1346C1068.47 1345.43 1066.26 1345.5 1063 1347z"/>
<path style="fill:#6aa285; stroke:none;" d="M452 1346L453 1347L452 1346z"/>
<path style="fill:#15884d; stroke:none;" d="M1059 1346C1058.45 1347.64 1058.64 1347.45 1057 1348C1059.31 1348.56 1059.76 1348 1061 1346L1059 1346z"/>
<path style="fill:#3c895f; stroke:none;" d="M1061 1348L1064 1347C1062.22 1346.04 1061.98 1346.23 1061 1348z"/>
<path style="fill:#5bb88b; stroke:none;" d="M454 1347L455 1348L454 1347z"/>
<path style="fill:#91deb4; stroke:none;" d="M1062 1347L1063 1348L1062 1347z"/>
<path style="fill:#b7efd4; stroke:none;" d="M454.667 1348.33C454.222 1348.78 455.278 1348.72 455.333 1348.67C455.778 1348.22 454.722 1348.28 454.667 1348.33z"/>
<path style="fill:#379267; stroke:none;" d="M456 1348C457.139 1349.02 457.612 1349.31 459 1350C458.02 1348.22 458.092 1348.41 456 1348z"/>
<path style="fill:#a1d1b7; stroke:none;" d="M1060 1348L1061 1349L1060 1348z"/>
<path style="fill:#dbfff1; stroke:none;" d="M1061 1348L1059 1349C1060.48 1349.79 1061.8 1349.87 1061 1348z"/>
<path style="fill:#80dbb0; stroke:none;" d="M457 1349L458 1350L457 1349z"/>
<path style="fill:#3eb07d; stroke:none;" d="M1057 1349L1058 1350L1057 1349z"/>
<path style="fill:#83e9ba; stroke:none;" d="M1058 1349L1059 1350L1058 1349z"/>
<path style="fill:#9fcfb7; stroke:none;" d="M459 1350L460 1351L459 1350z"/>
<path style="fill:#327554; stroke:none;" d="M460 1350C461.458 1351.08 462.248 1351.44 464 1352C462.686 1350.38 462.123 1350.31 460 1350z"/>
<path style="fill:#217a4c; stroke:none;" d="M1052 1350C1053.51 1350.68 1054.31 1350.83 1056 1351C1054.49 1350.32 1053.69 1350.17 1052 1350z"/>
<path style="fill:#95d8b7; stroke:none;" d="M461 1351L462 1352L461 1351z"/>
<path style="fill:#89e0b3; stroke:none;" d="M1054 1351L1055 1352L1054 1351z"/>
<path style="fill:#2a8e5c; stroke:none;" d="M464 1352C465.139 1353.02 465.612 1353.31 467 1354C466.02 1352.22 466.092 1352.41 464 1352z"/>
<path style="fill:#198553; stroke:none;" d="M1048 1352L1049 1354C1050.64 1353.45 1050.45 1353.64 1051 1352L1048 1352z"/>
<path style="fill:#5c9d7d; stroke:none;" d="M1051 1352L1052 1353L1051 1352z"/>
<path style="fill:#a8d2ba; stroke:none;" d="M1052 1352L1053 1353L1052 1352z"/>
<path style="fill:#7fe3b1; stroke:none;" d="M465 1353L466 1354L465 1353z"/>
<path style="fill:#85dcb1; stroke:none;" d="M1050 1353L1051 1354L1050 1353z"/>
<path style="fill:#abceba; stroke:none;" d="M467 1354L468 1355L467 1354z"/>
<path style="fill:#589373; stroke:none;" d="M468 1354L469 1355L468 1354z"/>
<path style="fill:#538c6c; stroke:none;" d="M1046.67 1354.33C1046.22 1354.78 1047.28 1354.72 1047.33 1354.67C1047.78 1354.22 1046.72 1354.28 1046.67 1354.33z"/>
<path style="fill:#91e1ba; stroke:none;" d="M469 1355L470 1356L469 1355z"/>
<path style="fill:#46ae7d; stroke:none;" d="M470 1355L471 1356L470 1355z"/>
<path style="fill:#8ad8ae; stroke:none;" d="M1046 1355L1047 1356L1046 1355z"/>
<path style="fill:#afdfc7; stroke:none;" d="M470.667 1356.33C470.222 1356.78 471.278 1356.72 471.333 1356.67C471.778 1356.22 470.722 1356.28 470.667 1356.33z"/>
<path style="fill:#40926a; stroke:none;" d="M472 1356C473.139 1357.02 473.612 1357.31 475 1358C474.02 1356.22 474.092 1356.41 472 1356z"/>
<path style="fill:#5aa27d; stroke:none;" d="M1043 1356L1044 1357L1043 1356z"/>
<path style="fill:#a3dabd; stroke:none;" d="M1044 1356L1045 1357L1044 1356z"/>
<path style="fill:#8cdeb6; stroke:none;" d="M473 1357L474 1358L473 1357z"/>
<path style="fill:#51b486; stroke:none;" d="M1041 1357L1042 1358L1041 1357z"/>
<path style="fill:#e4ffe9; stroke:none;" d="M474 1358C474.98 1359.78 474.908 1359.59 477 1360C475.861 1358.98 475.388 1358.69 474 1358z"/>
<path style="fill:#addec0; stroke:none;" d="M475 1358L476 1359L475 1358z"/>
<path style="fill:#5ea57d; stroke:none;" d="M476 1358L477 1359L476 1358z"/>
<path style="fill:#218853; stroke:none;" d="M477 1358C478.139 1359.02 478.612 1359.31 480 1360C479.02 1358.22 479.092 1358.41 477 1358z"/>
<path style="fill:#96f2c3; stroke:none;" d="M477 1359L478 1360L477 1359z"/>
<path style="fill:#4db47f; stroke:none;" d="M478 1359L479 1360L478 1359z"/>
<path style="fill:#4cc786; stroke:none;" d="M1037 1359L1038 1360L1037 1359z"/>
<path style="fill:#b4ffe8; stroke:none;" d="M1038.67 1359.33C1038.22 1359.78 1039.28 1359.72 1039.33 1359.67C1039.78 1359.22 1038.72 1359.28 1038.67 1359.33z"/>
<path style="fill:#1a8a58; stroke:none;" d="M480 1360C481.75 1361.25 482.857 1361.61 485 1362C483.46 1360.2 482.402 1360.17 480 1360z"/>
<path style="fill:#2a8a5c; stroke:none;" d="M1027 1363C1030.02 1363.03 1032.19 1362.13 1035 1361C1031.69 1359.65 1029.42 1360.46 1027 1363z"/>
<path style="fill:#8fb3a5; stroke:none;" d="M1035 1360L1036 1361L1035 1360z"/>
<path style="fill:#e5fff4; stroke:none;" d="M1025 1365C1030.04 1367.09 1034.39 1364.62 1038 1361C1033.39 1360.96 1029.34 1363.52 1025 1365z"/>
<path style="fill:#91f2c5; stroke:none;" d="M481 1361L482 1362L481 1361z"/>
<path style="fill:#85c4a7; stroke:none;" d="M1033 1361L1034 1362L1033 1361z"/>
<path style="fill:#85ba9c; stroke:none;" d="M484 1362L485 1363L484 1362z"/>
<path style="fill:#2c8358; stroke:none;" d="M485 1362C486.139 1363.02 486.612 1363.31 488 1364C487.02 1362.22 487.092 1362.41 485 1362z"/>
<path style="fill:#169756; stroke:none;" d="M1019 1364L1019 1366C1022.18 1365.79 1024.92 1364.69 1028 1364C1024.97 1361.98 1022.43 1363.31 1019 1364z"/>
<path style="fill:#87c8a8; stroke:none;" d="M1031 1362L1032 1363L1031 1362z"/>
<path style="fill:#7cd3a8; stroke:none;" d="M486 1363L487 1364L486 1363z"/>
<path style="fill:#8de8bd; stroke:none;" d="M1029 1363L1030 1364L1029 1363z"/>
<path style="fill:#ccffe7; stroke:none;" d="M486 1364C487.54 1365.8 488.597 1365.83 491 1366C489.25 1364.75 488.143 1364.39 486 1364z"/>
<path style="fill:#5ba781; stroke:none;" d="M489 1364L490 1365L489 1364z"/>
<path style="fill:#268e5d; stroke:none;" d="M490 1364C491.223 1365.2 491.42 1365.32 493 1366C492.02 1364.22 492.092 1364.41 490 1364z"/>
<path style="fill:#448366; stroke:none;" d="M1024.67 1364.33C1024.22 1364.78 1025.28 1364.72 1025.33 1364.67C1025.78 1364.22 1024.72 1364.28 1024.67 1364.33z"/>
<path style="fill:#71b093; stroke:none;" d="M1026 1364L1027 1365L1026 1364z"/>
<path style="fill:#83caaa; stroke:none;" d="M1024 1365L1025 1366L1024 1365z"/>
<path style="fill:#4e7e66; stroke:none;" d="M493 1366C494.248 1366.68 494.549 1366.75 496 1367C494.752 1366.32 494.452 1366.25 493 1366z"/>
<path style="fill:#169858; stroke:none;" d="M496 1366C498.579 1369.78 503.562 1371.26 508 1371C504.716 1368.15 500.301 1366.57 496 1366z"/>
<path style="fill:#258651; stroke:none;" d="M1012 1369C1015.41 1369.71 1018.67 1367.91 1022 1367C1018.22 1365.45 1015.4 1367.15 1012 1369z"/>
<path style="fill:#85d2a8; stroke:none;" d="M1022 1366L1023 1367L1022 1366z"/>
<path style="fill:#c4ffe6; stroke:none;" d="M1023 1366C1021.52 1366.99 1021.8 1367 1020 1367C1021.52 1367.86 1023.94 1368.29 1023 1366z"/>
<path style="fill:#95cdb2; stroke:none;" d="M495 1367L496 1368L495 1367z"/>
<path style="fill:#5eb787; stroke:none;" d="M1019 1367L1020 1368L1019 1367z"/>
<path style="fill:#d3ffee; stroke:none;" d="M496 1368C497.314 1369.62 497.877 1369.69 500 1370C498.542 1368.92 497.752 1368.56 496 1368z"/>
<path style="fill:#84bb9c; stroke:none;" d="M1017 1368L1018 1369L1017 1368z"/>
<path style="fill:#69c191; stroke:none;" d="M500 1369L501 1370L500 1369z"/>
<path style="fill:#52b37e; stroke:none;" d="M1014 1369L1015 1370L1014 1369z"/>
<path style="fill:#bcffd9; stroke:none;" d="M1015.67 1369.33C1015.22 1369.78 1016.28 1369.72 1016.33 1369.67C1016.78 1369.22 1015.72 1369.28 1015.67 1369.33z"/>
<path style="fill:#d2fff1; stroke:none;" d="M501 1370C501.98 1371.78 501.908 1371.59 504 1372C502.861 1370.98 502.388 1370.69 501 1370z"/>
<path style="fill:#94c0a5; stroke:none;" d="M502 1370L503 1371L502 1370z"/>
<path style="fill:#16884d; stroke:none;" d="M508 1372C510.711 1373.96 513.81 1374.98 517 1376C515.923 1372.77 514.557 1371.77 515 1370L508 1372z"/>
<path style="fill:#0f7142; stroke:none;" d="M1009.67 1370.33C1009.22 1370.78 1010.28 1370.72 1010.33 1370.67C1010.78 1370.22 1009.72 1370.28 1009.67 1370.33z"/>
<path style="fill:#74c09a; stroke:none;" d="M1012 1370L1013 1371L1012 1370z"/>
<path style="fill:#cfffed; stroke:none;" d="M1006 1373C1009.33 1373.93 1012.85 1372.69 1015 1370C1011.88 1370.74 1008.99 1371.85 1006 1373z"/>
<path style="fill:#8de2b8; stroke:none;" d="M504 1371L505 1372L504 1371z"/>
<path style="fill:#54be8d; stroke:none;" d="M505 1371L506 1372L505 1371z"/>
<path style="fill:#d6fff3; stroke:none;" d="M506 1372C506.98 1373.78 506.908 1373.59 509 1374C507.861 1372.98 507.389 1372.69 506 1372z"/>
<path style="fill:#7dc9a5; stroke:none;" d="M507 1372L508 1373L507 1372z"/>
<path style="fill:#158c54; stroke:none;" d="M999 1372L998 1376L1004 1374C1002.25 1372.75 1001.14 1372.39 999 1372z"/>
<path style="fill:#308059; stroke:none;" d="M1002 1372C1004.16 1373.25 1005.48 1373.22 1008 1373C1005.95 1372.13 1004.25 1372.05 1002 1372z"/>
<path style="fill:#85ecbd; stroke:none;" d="M509 1373L510 1374L509 1373z"/>
<path style="fill:#75c59e; stroke:none;" d="M1005 1373L1006 1374L1005 1373z"/>
<path style="fill:#cde6d3; stroke:none;" d="M510.667 1374.33C510.222 1374.78 511.278 1374.72 511.333 1374.67C511.778 1374.22 510.722 1374.28 510.667 1374.33z"/>
<path style="fill:#38a56c; stroke:none;" d="M512.667 1374.33C512.222 1374.78 513.278 1374.72 513.333 1374.67C513.778 1374.22 512.722 1374.28 512.667 1374.33z"/>
<path style="fill:#67b38d; stroke:none;" d="M1002 1374L1003 1375L1002 1374z"/>
<path style="fill:#a2ddc1; stroke:none;" d="M1003 1374L1004 1375L1003 1374z"/>
<path style="fill:#e7fff4; stroke:none;" d="M1002 1375C1004.32 1375.92 1005.58 1375.69 1008 1375C1005.76 1374.19 1004.35 1374.47 1002 1375z"/>
<path style="fill:#99ffcd; stroke:none;" d="M512.667 1375.33C512.222 1375.78 513.278 1375.72 513.333 1375.67C513.778 1375.22 512.722 1375.28 512.667 1375.33z"/>
<path style="fill:#69d69d; stroke:none;" d="M514 1375L515 1376L514 1375z"/>
<path style="fill:#a1f9cf; stroke:none;" d="M1000.67 1375.33C1000.22 1375.78 1001.28 1375.72 1001.33 1375.67C1001.78 1375.22 1000.72 1375.28 1000.67 1375.33z"/>
<path style="fill:#8eb5a2; stroke:none;" d="M517 1376L518 1377L517 1376z"/>
<path style="fill:#258955; stroke:none;" d="M518 1376C519.751 1377.25 520.857 1377.61 523 1378C521.46 1376.2 520.402 1376.17 518 1376z"/>
<path style="fill:#24845f; stroke:none;" d="M992 1378L997 1377C994.7 1376.08 993.623 1376.12 992 1378z"/>
<path style="fill:#7ba796; stroke:none;" d="M997 1376L998 1377L997 1376z"/>
<path style="fill:#a4e5c3; stroke:none;" d="M518.667 1377.33C518.222 1377.78 519.278 1377.72 519.333 1377.67C519.778 1377.22 518.722 1377.28 518.667 1377.33z"/>
<path style="fill:#7ccbac; stroke:none;" d="M995 1377L996 1378L995 1377z"/>
<path style="fill:#e4fffb; stroke:none;" d="M991 1379C993.88 1380.66 997.228 1380.6 998 1377L991 1379z"/>
<path style="fill:#d7ffef; stroke:none;" d="M518 1379C521.119 1380.54 524.57 1381.42 528 1382C524.951 1379.26 522.038 1378.21 518 1379z"/>
<path style="fill:#74b893; stroke:none;" d="M522 1378L523 1379L522 1378z"/>
<path style="fill:#2a915a; stroke:none;" d="M523 1378C524.751 1379.25 525.857 1379.61 528 1380C526.211 1378.58 525.294 1378.3 523 1378z"/>
<path style="fill:#3d8e63; stroke:none;" d="M988 1378C989.724 1379.06 989.921 1379.04 992 1379C990.494 1378.32 989.685 1378.17 988 1378z"/>
<path style="fill:#ace2cb; stroke:none;" d="M992.667 1378.33C992.222 1378.78 993.278 1378.72 993.333 1378.67C993.778 1378.22 992.722 1378.28 992.667 1378.33z"/>
<path style="fill:#8eeab9; stroke:none;" d="M524 1379L525 1380L524 1379z"/>
<path style="fill:#9ce4bc; stroke:none;" d="M990 1379L991 1380L990 1379z"/>
<path style="fill:#9ab7a5; stroke:none;" d="M527 1380L528 1381L527 1380z"/>
<path style="fill:#3a8a65; stroke:none;" d="M984 1380C985.249 1380.68 985.548 1380.75 987 1381C985.752 1380.32 985.451 1380.25 984 1380z"/>
<path style="fill:#a0ccb3; stroke:none;" d="M987 1380L988 1381L987 1380z"/>
<path style="fill:#86fbc6; stroke:none;" d="M528.667 1381.33C528.222 1381.78 529.278 1381.72 529.333 1381.67C529.778 1381.22 528.722 1381.28 528.667 1381.33z"/>
<path style="fill:#53c891; stroke:none;" d="M530 1381L531 1382L530 1381z"/>
<path style="fill:#9dedc8; stroke:none;" d="M984.667 1381.33C984.222 1381.78 985.278 1381.72 985.333 1381.67C985.778 1381.22 984.722 1381.28 984.667 1381.33z"/>
<path style="fill:#e7fffa; stroke:none;" d="M528 1382L528 1384L534 1384C531.958 1382.58 530.489 1382.25 528 1382z"/>
<path style="fill:#a8d8be; stroke:none;" d="M532 1382L533 1383L532 1382z"/>
<path style="fill:#238b58; stroke:none;" d="M533 1382C534.693 1383.06 536.04 1383.52 538 1384C536.46 1382.2 535.403 1382.17 533 1382z"/>
<path style="fill:#80b999; stroke:none;" d="M981 1382L982 1383L981 1382z"/>
<path style="fill:#90e7bd; stroke:none;" d="M534.667 1383.33C534.222 1383.78 535.278 1383.72 535.333 1383.67C535.778 1383.22 534.722 1383.28 534.667 1383.33z"/>
<path style="fill:#49c088; stroke:none;" d="M978 1383L979 1384L978 1383z"/>
<path style="fill:#bbffdf; stroke:none;" d="M979.667 1383.33C979.222 1383.78 980.278 1383.72 980.333 1383.67C980.778 1383.22 979.722 1383.28 979.667 1383.33z"/>
<path style="fill:#d5fff2; stroke:none;" d="M534 1384L534 1386L541 1386C538.689 1384.61 536.691 1384.23 534 1384z"/>
<path style="fill:#7ec6a0; stroke:none;" d="M538 1384L539 1385L538 1384z"/>
<path style="fill:#1d8550; stroke:none;" d="M539 1384C540.693 1385.06 542.04 1385.52 544 1386C542.46 1384.2 541.403 1384.17 539 1384z"/>
<path style="fill:#499c70; stroke:none;" d="M974.667 1384.33C974.222 1384.78 975.278 1384.72 975.333 1384.67C975.778 1384.22 974.722 1384.28 974.667 1384.33z"/>
<path style="fill:#cafff1; stroke:none;" d="M974 1385C975.943 1385.62 976.949 1385.44 979 1385C977.057 1384.38 976.051 1384.56 974 1385z"/>
<path style="fill:#6cd2a0; stroke:none;" d="M541 1385L542 1386L541 1385z"/>
<path style="fill:#73d3a3; stroke:none;" d="M973 1385L974 1386L973 1385z"/>
<path style="fill:#d0e0d3; stroke:none;" d="M542.667 1386.33C542.222 1386.78 543.278 1386.72 543.333 1386.67C543.778 1386.22 542.722 1386.28 542.667 1386.33z"/>
<path style="fill:#3ca175; stroke:none;" d="M544.667 1386.33C544.222 1386.78 545.278 1386.72 545.333 1386.67C545.778 1386.22 544.722 1386.28 544.667 1386.33z"/>
<path style="fill:#149457; stroke:none;" d="M546 1386C548.903 1388.39 552.355 1389.16 556 1390C553.806 1385.82 550.349 1386.01 546 1386z"/>
<path style="fill:#18955b; stroke:none;" d="M964 1388L970 1387C967.471 1385.96 965.824 1385.91 964 1388z"/>
<path style="fill:#9ad2b5; stroke:none;" d="M970 1386L971 1387L970 1386z"/>
<path style="fill:#a4ffdd; stroke:none;" d="M544 1387C545.248 1387.68 545.548 1387.75 547 1388C545.752 1387.32 545.452 1387.25 544 1387z"/>
<path style="fill:#74d7a8; stroke:none;" d="M967 1387L968 1388L967 1387z"/>
<path style="fill:#c9ffea; stroke:none;" d="M549 1388C550.314 1389.62 550.877 1389.69 553 1390C551.623 1389.09 550.56 1388.6 549 1388z"/>
<path style="fill:#73a88c; stroke:none;" d="M550 1388L551 1389L550 1388z"/>
<path style="fill:#208752; stroke:none;" d="M954 1391L963 1389C959.417 1387.5 956.313 1387.79 954 1391z"/>
<path style="fill:#56a47d; stroke:none;" d="M963 1388L964 1389L963 1388z"/>
<path style="fill:#92d8b6; stroke:none;" d="M964 1388L965 1389L964 1388z"/>
<path style="fill:#cbffef; stroke:none;" d="M961 1389C963.613 1390.1 966.164 1389.99 969 1390C966.337 1388.14 964.183 1388.63 961 1389z"/>
<path style="fill:#59b88c; stroke:none;" d="M553 1389L554 1390L553 1389z"/>
<path style="fill:#5abc8b; stroke:none;" d="M960 1389L961 1390L960 1389z"/>
<path style="fill:#e3fff8; stroke:none;" d="M549 1391C551.89 1392.21 554.874 1392 558 1392C554.912 1390.15 552.436 1390.06 549 1391z"/>
<path style="fill:#9fdcbb; stroke:none;" d="M555 1390L556 1391L555 1390z"/>
<path style="fill:#357d57; stroke:none;" d="M556 1390C557.506 1390.68 558.315 1390.83 560 1391C558.494 1390.32 557.685 1390.17 556 1390z"/>
<path style="fill:#6dad88; stroke:none;" d="M957 1390L958 1391L957 1390z"/>
<path style="fill:#ceffe9; stroke:none;" d="M955 1391C957.3 1391.92 958.377 1391.88 960 1390L955 1391z"/>
<path style="fill:#77c59b; stroke:none;" d="M558.667 1391.33C558.222 1391.78 559.278 1391.72 559.333 1391.67C559.778 1391.22 558.722 1391.28 558.667 1391.33z"/>
<path style="fill:#69cd9b; stroke:none;" d="M954 1391L955 1392L954 1391z"/>
<path style="fill:#82af9a; stroke:none;" d="M562 1392L563 1393L562 1392z"/>
<path style="fill:#2f845d; stroke:none;" d="M563 1392C565.013 1393.22 566.659 1393.65 569 1394C566.943 1392.42 565.612 1392.17 563 1392z"/>
<path style="fill:#337d5c; stroke:none;" d="M944 1392L944 1394L951 1393C948.668 1392.02 946.543 1392.02 944 1392z"/>
<path style="fill:#92b4a3; stroke:none;" d="M951 1392L952 1393L951 1392z"/>
<path style="fill:#7fc9a8; stroke:none;" d="M565 1393L566 1394L565 1393z"/>
<path style="fill:#88d2b1; stroke:none;" d="M948 1393L949 1394L948 1393z"/>
<path style="fill:#87caa9; stroke:none;" d="M568 1394L569 1395L568 1394z"/>
<path style="fill:#248a58; stroke:none;" d="M569 1394C571.283 1395.22 573.428 1395.65 576 1396C574.074 1393.79 571.875 1394.03 569 1394z"/>
<path style="fill:#20834d; stroke:none;" d="M933 1398C936.641 1396.84 940.255 1395.78 944 1395C939.788 1393.24 935.478 1394.07 933 1398z"/>
<path style="fill:#74b492; stroke:none;" d="M944 1394L945 1395L944 1394z"/>
<path style="fill:#daffeb; stroke:none;" d="M941 1395C943.613 1396.1 946.165 1395.99 949 1396L949 1394L941 1395z"/>
<path style="fill:#c5ffed; stroke:none;" d="M568 1395C569.506 1395.68 570.315 1395.83 572 1396C570.494 1395.32 569.685 1395.17 568 1395z"/>
<path style="fill:#59bf8d; stroke:none;" d="M572 1395L573 1396L572 1395z"/>
<path style="fill:#effff4; stroke:none;" d="M564 1397C567.697 1398.55 572.017 1398 576 1398C572.323 1395.23 568.339 1396.66 564 1397z"/>
<path style="fill:#90b69f; stroke:none;" d="M575 1396L576 1397L575 1396z"/>
<path style="fill:#228a57; stroke:none;" d="M576 1396C578.013 1397.22 579.658 1397.65 582 1398C580.266 1396 578.645 1396.08 576 1396z"/>
<path style="fill:#159d5d; stroke:none;" d="M928 1399C930.265 1398.26 931.547 1397.84 933 1396C930.457 1396.53 929.465 1396.88 928 1399z"/>
<path style="fill:#7cb193; stroke:none;" d="M937 1396L938 1397L937 1396z"/>
<path style="fill:#aeffe1; stroke:none;" d="M576 1397C577.248 1397.68 577.548 1397.75 579 1398C577.752 1397.32 577.451 1397.25 576 1397z"/>
<path style="fill:#b1ffd9; stroke:none;" d="M934 1397C935.248 1397.68 935.548 1397.75 937 1398C935.752 1397.32 935.452 1397.25 934 1397z"/>
<path style="fill:#60a183; stroke:none;" d="M582.667 1398.33C582.222 1398.78 583.278 1398.72 583.333 1398.67C583.778 1398.22 582.722 1398.28 582.667 1398.33z"/>
<path style="fill:#5d8d75; stroke:none;" d="M929 1398L930 1399L929 1398z"/>
<path style="fill:#93c5ac; stroke:none;" d="M930 1398L931 1399L930 1398z"/>
<path style="fill:#b5ffdf; stroke:none;" d="M583 1399C584.249 1399.68 584.548 1399.75 586 1400C584.752 1399.32 584.452 1399.25 583 1399z"/>
<path style="fill:#53bf8b; stroke:none;" d="M586 1399L587 1400L586 1399z"/>
<path style="fill:#44d695; stroke:none;" d="M926 1399L927 1400L926 1399z"/>
<path style="fill:#6dffc1; stroke:none;" d="M927 1399L928 1400L927 1399z"/>
<path style="fill:#dafff3; stroke:none;" d="M584 1400L584 1402L592 1402C589.397 1400.44 587.022 1400.14 584 1400z"/>
<path style="fill:#93d4b2; stroke:none;" d="M589 1400L590 1401L589 1400z"/>
<path style="fill:#198c51; stroke:none;" d="M590 1400C593.95 1402.12 598.684 1402.78 603 1404C599.925 1399.68 594.877 1400 590 1400z"/>
<path style="fill:#74b18f; stroke:none;" d="M922 1400L923 1401L922 1400z"/>
<path style="fill:#e5fff2; stroke:none;" d="M923 1400L923 1402L928 1402L928 1400L923 1400z"/>
<path style="fill:#6ee1a6; stroke:none;" d="M592.667 1401.33C592.222 1401.78 593.278 1401.72 593.333 1401.67C593.778 1401.22 592.722 1401.28 592.667 1401.33z"/>
<path style="fill:#78daa9; stroke:none;" d="M918.667 1401.33C918.222 1401.78 919.278 1401.72 919.333 1401.67C919.778 1401.22 918.722 1401.28 918.667 1401.33z"/>
<path style="fill:#c0ffe3; stroke:none;" d="M920 1401C921.249 1401.68 921.548 1401.75 923 1402C921.752 1401.32 921.452 1401.25 920 1401z"/>
<path style="fill:#dcfff3; stroke:none;" d="M592 1402L592 1404C599.396 1404.48 606.564 1407.53 614 1408C608.847 1403.78 598.585 1402.3 592 1402z"/>
<path style="fill:#87bfa2; stroke:none;" d="M597 1402L598 1403L597 1402z"/>
<path style="fill:#12935a; stroke:none;" d="M904 1402L904 1404L914 1403C910.837 1401.67 907.414 1402 904 1402z"/>
<path style="fill:#7ab694; stroke:none;" d="M914 1402L915 1403L914 1402z"/>
<path style="fill:#dbffed; stroke:none;" d="M912 1403C914.529 1404.04 916.176 1404.09 918 1402L912 1403z"/>
<path style="fill:#64cc99; stroke:none;" d="M600.667 1403.33C600.222 1403.78 601.278 1403.72 601.333 1403.67C601.778 1403.22 600.722 1403.28 600.667 1403.33z"/>
<path style="fill:#6ee3af; stroke:none;" d="M910.667 1403.33C910.222 1403.78 911.278 1403.72 911.333 1403.67C911.778 1403.22 910.722 1403.28 910.667 1403.33z"/>
<path style="fill:#88cfad; stroke:none;" d="M605 1404L606 1405L605 1404z"/>
<path style="fill:#22935f; stroke:none;" d="M607 1404C610.647 1406.23 614.768 1406.82 619 1407C615.269 1404.01 611.629 1404 607 1404z"/>
<path style="fill:#338964; stroke:none;" d="M902 1404C903.506 1404.68 904.315 1404.83 906 1405C904.494 1404.32 903.685 1404.17 902 1404z"/>
<path style="fill:#88bea6; stroke:none;" d="M906 1404L907 1405L906 1404z"/>
<path style="fill:#7cddb2; stroke:none;" d="M609 1405L610 1406L609 1405z"/>
<path style="fill:#47b584; stroke:none;" d="M901 1405L902 1406L901 1405z"/>
<path style="fill:#afffe0; stroke:none;" d="M902.667 1405.33C902.222 1405.78 903.278 1405.72 903.333 1405.67C903.778 1405.22 902.722 1405.28 902.667 1405.33z"/>
<path style="fill:#abd3bb; stroke:none;" d="M613 1406L614 1407L613 1406z"/>
<path style="fill:#87aa94; stroke:none;" d="M896.667 1406.33C896.222 1406.78 897.278 1406.72 897.333 1406.67C897.778 1406.22 896.722 1406.28 896.667 1406.33z"/>
<path style="fill:#caffe7; stroke:none;" d="M614 1407C615.506 1407.68 616.315 1407.83 618 1408C616.494 1407.32 615.685 1407.17 614 1407z"/>
<path style="fill:#3caf74; stroke:none;" d="M618.667 1407.33C618.222 1407.78 619.278 1407.72 619.333 1407.67C619.778 1407.22 618.722 1407.28 618.667 1407.33z"/>
<path style="fill:#7ff0b2; stroke:none;" d="M892.667 1407.33C892.222 1407.78 893.278 1407.72 893.333 1407.67C893.778 1407.22 892.722 1407.28 892.667 1407.33z"/>
<path style="fill:#aaffd7; stroke:none;" d="M894.667 1407.33C894.222 1407.78 895.278 1407.72 895.333 1407.67C895.778 1407.22 894.722 1407.28 894.667 1407.33z"/>
<path style="fill:#f0ffff; stroke:none;" d="M617 1408C619.637 1413.03 624.993 1412 630 1412C626.245 1409.31 621.585 1408.23 617 1408z"/>
<path style="fill:#95afa6; stroke:none;" d="M622.667 1408.33C622.222 1408.78 623.278 1408.72 623.333 1408.67C623.778 1408.22 622.722 1408.28 622.667 1408.33z"/>
<path style="fill:#1b8d5a; stroke:none;" d="M624 1408C626.757 1409.32 629.004 1409.5 632 1409C629.387 1407.9 626.835 1408.01 624 1408z"/>
<path style="fill:#337d5c; stroke:none;" d="M880 1410L887 1409C884.266 1407.86 882.003 1407.71 880 1410z"/>
<path style="fill:#749b88; stroke:none;" d="M887 1408L888 1409L887 1408z"/>
<path style="fill:#cdded4; stroke:none;" d="M888.667 1408.33C888.222 1408.78 889.278 1408.72 889.333 1408.67C889.778 1408.22 888.722 1408.28 888.667 1408.33z"/>
<path style="fill:#b4ffe5; stroke:none;" d="M624 1409C625.248 1409.68 625.548 1409.75 627 1410C625.752 1409.32 625.452 1409.25 624 1409z"/>
<path style="fill:#57ba8e; stroke:none;" d="M627.667 1409.33C627.222 1409.78 628.278 1409.72 628.333 1409.67C628.778 1409.22 627.722 1409.28 627.667 1409.33z"/>
<path style="fill:#1c9258; stroke:none;" d="M866 1410C869.642 1411.95 873.089 1410.56 877 1410C873.443 1408.55 869.754 1409.7 866 1410z"/>
<path style="fill:#61bc93; stroke:none;" d="M882 1409L883 1410L882 1409z"/>
<path style="fill:#d8ffee; stroke:none;" d="M628 1410C630.85 1411.95 633.59 1411.97 637 1412C634.138 1410.62 631.162 1410.25 628 1410z"/>
<path style="fill:#74af91; stroke:none;" d="M632.667 1410.33C632.222 1410.78 633.278 1410.72 633.333 1410.67C633.778 1410.22 632.722 1410.28 632.667 1410.33z"/>
<path style="fill:#3c8d64; stroke:none;" d="M634 1410C635.969 1411.06 637.798 1411.53 640 1412C638.266 1410 636.645 1410.08 634 1410z"/>
<path style="fill:#2d7851; stroke:none;" d="M873 1410C874.506 1410.68 875.315 1410.83 877 1411C875.494 1410.32 874.685 1410.17 873 1410z"/>
<path style="fill:#81b797; stroke:none;" d="M877 1410L878 1411L877 1410z"/>
<path style="fill:#d5ffeb; stroke:none;" d="M874 1411C876.89 1412.21 879.874 1412 883 1412L883 1410L874 1411z"/>
<path style="fill:#82d2ab; stroke:none;" d="M637 1411L638 1412L637 1411z"/>
<path style="fill:#51b583; stroke:none;" d="M870.667 1411.33C870.222 1411.78 871.278 1411.72 871.333 1411.67C871.778 1411.22 870.722 1411.28 870.667 1411.33z"/>
<path style="fill:#a1f4c8; stroke:none;" d="M872.667 1411.33C872.222 1411.78 873.278 1411.72 873.333 1411.67C873.778 1411.22 872.722 1411.28 872.667 1411.33z"/>
<path style="fill:#d5fff2; stroke:none;" d="M640 1412C640.98 1413.78 640.908 1413.59 643 1414C641.777 1412.8 641.58 1412.68 640 1412z"/>
<path style="fill:#89c8a9; stroke:none;" d="M642.667 1412.33C642.222 1412.78 643.278 1412.72 643.333 1412.67C643.778 1412.22 642.722 1412.28 642.667 1412.33z"/>
<path style="fill:#46906b; stroke:none;" d="M644.667 1412.33C644.222 1412.78 645.278 1412.72 645.333 1412.67C645.778 1412.22 644.722 1412.28 644.667 1412.33z"/>
<path style="fill:#168952; stroke:none;" d="M852 1413C856.348 1414.81 861.378 1413.1 866 1413C861.552 1411.13 856.649 1412.41 852 1413z"/>
<path style="fill:#afeccd; stroke:none;" d="M866.667 1412.33C866.222 1412.78 867.278 1412.72 867.333 1412.67C867.778 1412.22 866.722 1412.28 866.667 1412.33z"/>
<path style="fill:#e0ffef; stroke:none;" d="M861 1413C864.697 1414.55 869.017 1414 873 1414L873 1412L861 1413z"/>
<path style="fill:#aeffdc; stroke:none;" d="M643 1413C644.769 1413.78 646.036 1413.91 648 1414C646.231 1413.22 644.964 1413.09 643 1413z"/>
<path style="fill:#60c897; stroke:none;" d="M648.667 1413.33C648.222 1413.78 649.278 1413.72 649.333 1413.67C649.778 1413.22 648.722 1413.28 648.667 1413.33z"/>
<path style="fill:#78d3a7; stroke:none;" d="M859.667 1413.33C859.222 1413.78 860.278 1413.72 860.333 1413.67C860.778 1413.22 859.722 1413.28 859.667 1413.33z"/>
<path style="fill:#e1fff5; stroke:none;" d="M650 1414L650 1416L656 1416C653.958 1414.58 652.489 1414.25 650 1414z"/>
<path style="fill:#80b09a; stroke:none;" d="M654.667 1414.33C654.222 1414.78 655.278 1414.72 655.333 1414.67C655.778 1414.22 654.722 1414.28 654.667 1414.33z"/>
<path style="fill:#12944a; stroke:none;" d="M800 1414L800 1416L816 1416L816 1414L800 1414z"/>
<path style="fill:#317856; stroke:none;" d="M848 1414C849.769 1414.78 851.036 1414.91 853 1415C851.231 1414.22 849.964 1414.09 848 1414z"/>
<path style="fill:#a9dec4; stroke:none;" d="M853.667 1414.33C853.222 1414.78 854.278 1414.72 854.333 1414.67C854.778 1414.22 853.722 1414.28 853.667 1414.33z"/>
<path style="fill:#cdffef; stroke:none;" d="M848 1415C851.35 1416.4 854.521 1415.83 858 1415C854.84 1414.03 851.333 1414.93 848 1415z"/>
<path style="fill:#b3ffdf; stroke:none;" d="M656 1415C657.769 1415.78 659.036 1415.91 661 1416C659.231 1415.22 657.964 1415.09 656 1415z"/>
<path style="fill:#52c287; stroke:none;" d="M661 1415C662.249 1415.68 662.548 1415.75 664 1416C662.752 1415.32 662.451 1415.25 661 1415z"/>
<path style="fill:#5ed295; stroke:none;" d="M845 1415C846.249 1415.68 846.548 1415.75 848 1416C846.752 1415.32 846.451 1415.25 845 1415z"/>
<path style="fill:#e8fff4; stroke:none;" d="M661 1416L661 1418L672 1418C668.54 1416.34 664.815 1416.08 661 1416z"/>
<path style="fill:#9ec3b1; stroke:none;" d="M667.667 1416.33C667.222 1416.78 668.278 1416.72 668.333 1416.67C668.778 1416.22 667.722 1416.28 667.667 1416.33z"/>
<path style="fill:#67917d; stroke:none;" d="M669 1416C670.248 1416.68 670.548 1416.75 672 1417C670.752 1416.32 670.452 1416.25 669 1416z"/>
<path style="fill:#129257; stroke:none;" d="M672 1416C676.857 1418.1 682.758 1418 688 1418L688 1416L672 1416z"/>
<path style="fill:#169057; stroke:none;" d="M800 1418L800 1420C810.463 1419.97 821.71 1418.91 832 1417C826.195 1414.56 819.897 1416.72 814 1417.68C809.441 1418.41 804.606 1418 800 1418z"/>
<path style="fill:#44755f; stroke:none;" d="M832 1416C834.052 1416.87 835.747 1416.95 838 1417C835.948 1416.13 834.253 1416.05 832 1416z"/>
<path style="fill:#9fc1b0; stroke:none;" d="M838.667 1416.33C838.222 1416.78 839.278 1416.72 839.333 1416.67C839.778 1416.22 838.722 1416.28 838.667 1416.33z"/>
<path style="fill:#c7e2d3; stroke:none;" d="M840.667 1416.33C840.222 1416.78 841.278 1416.72 841.333 1416.67C841.778 1416.22 840.722 1416.28 840.667 1416.33z"/>
<path style="fill:#b4ffde; stroke:none;" d="M672 1417C673.249 1417.68 673.548 1417.75 675 1418C673.752 1417.32 673.452 1417.25 672 1417z"/>
<path style="fill:#7ad7a8; stroke:none;" d="M675.667 1417.33C675.222 1417.78 676.278 1417.72 676.333 1417.67C676.778 1417.22 675.722 1417.28 675.667 1417.33z"/>
<path style="fill:#3fa573; stroke:none;" d="M677.667 1417.33C677.222 1417.78 678.278 1417.72 678.333 1417.67C678.778 1417.22 677.722 1417.28 677.667 1417.33z"/>
<path style="fill:#6bcc96; stroke:none;" d="M829 1417C830.248 1417.68 830.549 1417.75 832 1418C830.752 1417.32 830.451 1417.25 829 1417z"/>
<path style="fill:#cefee8; stroke:none;" d="M832 1417C835.163 1418.33 838.586 1418 842 1418C838.837 1416.67 835.414 1417 832 1417z"/>
<path style="fill:#d2fff2; stroke:none;" d="M679 1418C682.482 1420.64 686.761 1420 691 1420C687.397 1418.47 682.917 1418.31 679 1418z"/>
<path style="fill:#a1d9be; stroke:none;" d="M683.667 1418.33C683.222 1418.78 684.278 1418.72 684.333 1418.67C684.778 1418.22 683.722 1418.28 683.667 1418.33z"/>
<path style="fill:#80bca0; stroke:none;" d="M685 1418C686.249 1418.68 686.548 1418.75 688 1419C686.752 1418.32 686.451 1418.25 685 1418z"/>
<path style="fill:#248a5a; stroke:none;" d="M688 1418C692.549 1419.91 699.038 1419.9 704 1420L704 1418L688 1418z"/>
<path style="fill:#71ac8c; stroke:none;" d="M819.667 1418.33C819.222 1418.78 820.278 1418.72 820.333 1418.67C820.778 1418.22 819.722 1418.28 819.667 1418.33z"/>
<path style="fill:#a2d4b7; stroke:none;" d="M821 1418C822.249 1418.68 822.548 1418.75 824 1419C822.752 1418.32 822.451 1418.25 821 1418z"/>
<path style="fill:#e1ffee; stroke:none;" d="M821 1419C823.46 1419.89 825.415 1419.47 828 1419C825.54 1418.11 823.585 1418.53 821 1419z"/>
<path style="fill:#8eebbe; stroke:none;" d="M691 1419C692.769 1419.78 694.036 1419.91 696 1420C694.231 1419.22 692.964 1419.09 691 1419z"/>
<path style="fill:#51b384; stroke:none;" d="M696.667 1419.33C696.222 1419.78 697.278 1419.72 697.333 1419.67C697.778 1419.22 696.722 1419.28 696.667 1419.33z"/>
<path style="fill:#3dba80; stroke:none;" d="M808 1419C809.249 1419.68 809.548 1419.75 811 1420C809.752 1419.32 809.451 1419.25 808 1419z"/>
<path style="fill:#72ecb3; stroke:none;" d="M811.667 1419.33C811.222 1419.78 812.278 1419.72 812.333 1419.67C812.778 1419.22 811.722 1419.28 811.667 1419.33z"/>
<path style="fill:#92ffd3; stroke:none;" d="M813 1419C814.249 1419.68 814.548 1419.75 816 1420C814.752 1419.32 814.452 1419.25 813 1419z"/>
<path style="fill:#c5ffe3; stroke:none;" d="M816 1419C817.769 1419.78 819.036 1419.91 821 1420C819.231 1419.22 817.964 1419.09 816 1419z"/>
<path style="fill:#daf4e7; stroke:none;" d="M699 1420C700.769 1420.78 702.036 1420.91 704 1421C702.231 1420.22 700.964 1420.09 699 1420z"/>
<path style="fill:#b0f3d2; stroke:none;" d="M704 1420C705.248 1420.68 705.548 1420.75 707 1421C705.752 1420.32 705.452 1420.25 704 1420z"/>
<path style="fill:#89cfad; stroke:none;" d="M707.667 1420.33C707.222 1420.78 708.278 1420.72 708.333 1420.67C708.778 1420.22 707.722 1420.28 707.667 1420.33z"/>
<path style="fill:#6ab491; stroke:none;" d="M709 1420C710.249 1420.68 710.548 1420.75 712 1421C710.752 1420.32 710.452 1420.25 709 1420z"/>
<path style="fill:#2b885c; stroke:none;" d="M712 1420C718.202 1422.6 727.298 1421 734 1421C727.798 1418.4 718.702 1420 712 1420z"/>
<path style="fill:#17925b; stroke:none;" d="M734 1420C738.275 1422.92 743.994 1422 749 1422C760.716 1422 772.328 1421 784 1421C779.28 1419.02 773.08 1420 768 1420L734 1420z"/>
<path style="fill:#34815d; stroke:none;" d="M784 1420C787.163 1421.33 790.586 1421 794 1421C790.837 1419.67 787.414 1420 784 1420z"/>
<path style="fill:#61a785; stroke:none;" d="M794 1420C795.248 1420.68 795.548 1420.75 797 1421C795.752 1420.32 795.452 1420.25 794 1420z"/>
<path style="fill:#8ad0ae; stroke:none;" d="M797 1420C798.248 1420.68 798.548 1420.75 800 1421C798.752 1420.32 798.452 1420.25 797 1420z"/>
<path style="fill:#e6f2e8; stroke:none;" d="M800 1420C802.613 1421.1 805.165 1420.99 808 1421C805.387 1419.9 802.836 1420.01 800 1420z"/>
<path style="fill:#c5ffed; stroke:none;" d="M704 1421C708.724 1422.98 714.91 1422 720 1422C715.276 1420.02 709.09 1421 704 1421z"/>
<path style="fill:#93e8bf; stroke:none;" d="M720 1421C722.613 1422.1 725.165 1421.99 728 1422C725.387 1420.9 722.835 1421.01 720 1421z"/>
<path style="fill:#55b689; stroke:none;" d="M728 1421C730.052 1421.87 731.747 1421.95 734 1422C731.948 1421.13 730.253 1421.05 728 1421z"/>
<path style="fill:#3da072; stroke:none;" d="M734.667 1421.33C734.222 1421.78 735.278 1421.72 735.333 1421.67C735.778 1421.22 734.722 1421.28 734.667 1421.33z"/>
<path style="fill:#57c996; stroke:none;" d="M772 1421C774.331 1421.98 776.457 1421.98 779 1422C776.668 1421.02 774.543 1421.02 772 1421z"/>
<path style="fill:#85f1c0; stroke:none;" d="M779 1421C780.769 1421.78 782.036 1421.91 784 1422C782.231 1421.22 780.964 1421.09 779 1421z"/>
<path style="fill:#b1ffdf; stroke:none;" d="M784 1421C786.331 1421.98 788.457 1421.98 791 1422C788.668 1421.02 786.543 1421.02 784 1421z"/>
<path style="fill:#cbffef; stroke:none;" d="M791 1421C793.89 1422.21 796.874 1422 800 1422C797.11 1420.79 794.126 1421 791 1421z"/>
<path style="fill:#ecfbf4; stroke:none;" d="M734 1422C737.699 1423.55 742.017 1423 746 1423L768 1423C764.301 1421.45 759.983 1422 756 1422L734 1422z"/>
</svg>
