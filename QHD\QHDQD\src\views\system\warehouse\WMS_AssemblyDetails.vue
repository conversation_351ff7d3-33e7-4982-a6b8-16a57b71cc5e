<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/warehouse/WMS_AssemblyDetails.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/warehouse/WMS_AssemblyDetails.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'id',
                footer: "Foots",
                cnName: '组装明细表',
                name: 'warehouse/WMS_AssemblyDetails',
                url: "/WMS_AssemblyDetails/",
                sortName: "id"
            });
            const editFormFields = ref({"ItemType":"","ItemName":"","ItemCode":"","ToolNumber":"","ContainerPoint":"","ItemModel":"","BindiToolHandle":""});
            const editFormOptions = ref([[{"title":"物料名称","field":"ItemName"},
                               {"title":"物料编号","field":"ItemCode"},
                               {"title":"刀号","field":"ToolNumber"},
                               {"title":"放置托盘位置","field":"ContainerPoint","type":"number"},
                               {"title":"物料规格型号","field":"ItemModel"},
                               {"title":"刀柄绑定二维码","field":"BindiToolHandle"},
                               {"dataKey":"物料类型","data":[],"title":"物料类别","field":"ItemType","type":"select"}]]);
            const searchFormFields = ref({});
            const searchFormOptions = ref([]);
            const columns = ref([{field:'id',title:'id',type:'int',width:110,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'AssemblyID',title:'组装主表id',type:'int',width:110,hidden:true,align:'left'},
                       {field:'ItemID',title:'物料id',type:'int',width:110,hidden:true,align:'left'},
                       {field:'ItemType',title:'物料类别',type:'int',bind:{ key:'物料类型',data:[]},width:110,align:'left',sort:true},
                       {field:'ItemName',title:'物料名称',type:'string',width:220,align:'left'},
                       {field:'ItemCode',title:'物料编号',type:'string',width:220,align:'left'},
                       {field:'ToolNumber',title:'刀号',type:'string',width:220,align:'left'},
                       {field:'ContainerPoint',title:'放置托盘位置',type:'int',width:80,align:'left'},
                       {field:'ItemModel',title:'物料规格型号',type:'string',width:220,align:'left'},
                       {field:'BindiToolHandle',title:'刀柄绑定二维码',type:'string',width:220,align:'left'},
                       {field:'Status',title:'是否有对刀仪数据',type:'int',bind:{ key:'enable',data:[]},width:80,hidden:true,align:'left'}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
