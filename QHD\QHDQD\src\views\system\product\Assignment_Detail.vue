<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/product/Assignment_Detail.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/product/Assignment_Detail.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'ID',
                footer: "Foots",
                cnName: '订单排产任务',
                name: 'product/Assignment_Detail',
                url: "/Assignment_Detail/",
                sortName: "OrderNumber"
            });
            const editFormFields = ref({"TaskCoding":"","OP_NAME":"","TaskType":"","Priority":"","START_DATE":"","DUE_DATE":"","PLAN_HOURS":""});
            const editFormOptions = ref([[{"title":"任务编号","required":true,"field":"TaskCoding"},
                               {"title":"任务名称","field":"OP_NAME"},
                               {"dataKey":"任务类型","data":[],"title":"任务类型","required":true,"field":"TaskType","type":"select"},
                               {"dataKey":"生产排单优先级","data":[],"title":"优先级","required":true,"field":"Priority","type":"select"},
                               {"title":"开始时间","required":true,"field":"START_DATE","type":"date"},
                               {"title":"结束时间","required":true,"field":"DUE_DATE","type":"date"},
                               {"dataKey":"任务状态","data":[],"title":"任务状态","field":"PLAN_HOURS","type":"select"}]]);
            const searchFormFields = ref({});
            const searchFormOptions = ref([]);
            const columns = ref([{field:'ID',title:'编号',type:'int',width:110,hidden:true,require:true,align:'left'},
                       {field:'TaskCoding',title:'任务编号',type:'string',width:120,require:true,align:'left',sort:true},
                       {field:'OP_NAME',title:'任务名称',type:'string',width:120,hidden:true,align:'left'},
                       {field:'TaskType',title:'任务类型',type:'string',bind:{ key:'任务类型',data:[]},width:110,require:true,align:'left'},
                       {field:'Priority',title:'优先级',type:'string',bind:{ key:'生产排单优先级',data:[]},width:110,require:true,align:'left'},
                       {field:'START_DATE',title:'开始时间',type:'string',width:120,require:true,align:'left'},
                       {field:'DUE_DATE',title:'结束时间',type:'string',width:120,require:true,align:'left'},
                       {field:'PLAN_HOURS',title:'任务状态',type:'string',bind:{ key:'任务状态',data:[]},width:110,align:'left'},
                       {field:'ASSID',title:'排产id',type:'int',width:120,hidden:true,align:'left'}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
