<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/warehouse/WMS_InOutRecordDetail_Fixture.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/warehouse/WMS_InOutRecordDetail_Fixture.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'DetailID',
                footer: "Foots",
                cnName: '夹具出入库明细New',
                name: 'warehouse/WMS_InOutRecordDetail_Fixture',
                url: "/WMS_InOutRecordDetail_Fixture/",
                sortName: "DetailID"
            });
            const editFormFields = ref({"ItemID":"","Qty":"","Remark":""});
            const editFormOptions = ref([[{"dataKey":"物料编码_刀具","data":[],"title":"关联物品","required":true,"field":"ItemID","type":"select"},
                               {"title":"数量","required":true,"field":"Qty","type":"decimal"},
                               {"title":"备注","field":"Remark","type":"textarea"}]]);
            const searchFormFields = ref({});
            const searchFormOptions = ref([]);
            const columns = ref([{field:'DetailID',title:'明细ID',type:'int',width:80,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'RecordCode',title:'出入库编号',type:'string',width:240,require:true,align:'left',sort:true},
                       {field:'RecordID',title:'关联出入库记录',type:'int',width:80,hidden:true,require:true,align:'left'},
                       {field:'ItemID',title:'关联物品',type:'int',bind:{ key:'物料编码_刀具',data:[]},width:80,require:true,align:'left'},
                       {field:'Qty',title:'数量',type:'int',width:80,require:true,align:'left'},
                       {field:'CreateID',title:'CreateID',type:'int',width:80,hidden:true,align:'left'},
                       {field:'Creator',title:'Creator',type:'string',width:130,hidden:true,align:'left'},
                       {field:'CreateDate',title:'CreateDate',type:'datetime',width:150,hidden:true,align:'left',sort:true},
                       {field:'ModifyID',title:'ModifyID',type:'int',width:80,hidden:true,align:'left'},
                       {field:'Modifier',title:'Modifier',type:'string',width:130,hidden:true,align:'left'},
                       {field:'ModifyDate',title:'ModifyDate',type:'datetime',width:150,hidden:true,align:'left',sort:true},
                       {field:'Remark',title:'备注',type:'string',width:110,align:'left'}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
