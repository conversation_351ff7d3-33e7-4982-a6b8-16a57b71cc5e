<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/warehouse/WMS_Assembly.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/warehouse/WMS_Assembly.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'AssemblyID',
                footer: "Foots",
                cnName: '组装主表',
                name: 'warehouse/WMS_Assembly',
                url: "/WMS_Assembly/",
                sortName: "AssemblyID"
            });
            const editFormFields = ref({"AssemblyNo":""});
            const editFormOptions = ref([[{"dataKey":"通知单模糊搜索","data":[],"title":"通知单编码","field":"AssemblyNo","type":"remoteSearch"}]]);
            const searchFormFields = ref({});
            const searchFormOptions = ref([]);
            const columns = ref([{field:'AssemblyID',title:'AssemblyID',type:'int',width:110,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'AssembleCode',title:'组装编码',type:'string',width:110,align:'left',sort:true},
                       {field:'AssemblyNo',title:'通知单编码',type:'string',bind:{ key:'通知单模糊搜索',data:[]},width:180,align:'left'},
                       {field:'Remarks',title:'备注',type:'string',width:110,hidden:true,align:'left'},
                       {field:'CreateID',title:'CreateID',type:'int',width:100,hidden:true,align:'left'},
                       {field:'Creator',title:'创建人',type:'string',width:100,hidden:true,align:'left'},
                       {field:'CreateDate',title:'创建日期',type:'datetime',width:150,hidden:true,align:'left',sort:true},
                       {field:'ModifyID',title:'ModifyID',type:'int',width:100,hidden:true,align:'left'},
                       {field:'Modifier',title:'修改人',type:'string',width:100,hidden:true,align:'left'},
                       {field:'ModifyDate',title:'修改日期',type:'datetime',width:150,hidden:true,align:'left',sort:true},
                       {field:'Status',title:'状态',type:'int',width:110,hidden:true,align:'left'}]);
            const detail = ref({
                cnName: "组装明细表",
                table: "WMS_AssemblyDetails",
                columns: [{field:'id',title:'id',type:'int',width:110,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'AssemblyID',title:'组装主表id',type:'int',width:110,hidden:true,align:'left'},
                       {field:'ItemID',title:'物料id',type:'int',width:110,hidden:true,align:'left'},
                       {field:'ItemType',title:'物料类别',type:'int',bind:{ key:'物料类型',data:[]},width:110,edit:{type:'select'},align:'left',sort:true},
                       {field:'ItemName',title:'物料名称',type:'string',width:220,edit:{type:''},align:'left'},
                       {field:'ItemCode',title:'物料编号',type:'string',width:220,edit:{type:''},align:'left'},
                       {field:'ToolNumber',title:'刀号',type:'string',width:100,edit:{type:''},align:'left'},
                       {field:'ContainerPoint',title:'放置托盘位置',type:'int',width:80,edit:{type:''},align:'left'},
                       {field:'ItemModel',title:'物料规格型号',type:'string',width:220,edit:{type:''},align:'left'},
                       {field:'BindiToolHandle',title:'刀柄绑定二维码',type:'string',width:220,edit:{type:''},align:'left'},
                       {field:'Status',title:'是否有对刀仪数据',type:'int',bind:{ key:'enable',data:[]},width:80,hidden:true,align:'left'}],
                      
                sortName: "id",
                key: "id"
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
