<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/instock/WMS_InStockDetails.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/instock/WMS_InStockDetails.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'ID',
                footer: "Foots",
                cnName: '入库明细表',
                name: 'instock/WMS_InStockDetails',
                url: "/WMS_InStockDetails/",
                sortName: "ID"
            });
            const editFormFields = ref({"StuId":"","qty":"","pice":"","remark":""});
            const editFormOptions = ref([[{"dataKey":"物料编号","data":[],"title":"物料","field":"StuId","type":"select"},
                               {"title":"数量","field":"qty","type":"decimal"},
                               {"title":"单价","field":"pice","type":"decimal"},
                               {"title":"备注","field":"remark","type":"textarea"}]]);
            const searchFormFields = ref({});
            const searchFormOptions = ref([]);
            const columns = ref([{field:'ID',title:'id',type:'int',width:110,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'SGUID',title:'SGUID',type:'string',width:220,hidden:true,align:'left'},
                       {field:'Tenant',title:'租户id',type:'string',width:220,hidden:true,align:'left'},
                       {field:'CreatedBy',title:'创建人',type:'int',width:110,hidden:true,align:'left'},
                       {field:'CreatedTime',title:'创建时间',type:'datetime',width:150,hidden:true,align:'left',sort:true},
                       {field:'UpdatedBy',title:'更新人',type:'int',width:110,hidden:true,align:'left'},
                       {field:'UpdatedTime',title:'更新时间',type:'datetime',width:150,hidden:true,align:'left',sort:true},
                       {field:'WIID',title:'出入库表主表',type:'int',width:110,hidden:true,align:'left'},
                       {field:'StuId',title:'物料',type:'int',bind:{ key:'物料编号',data:[]},width:110,align:'left',sort:true},
                       {field:'Static',title:'状态',type:'string',width:220,align:'left'},
                       {field:'qty',title:'数量',type:'decimal',width:110,align:'left'},
                       {field:'pice',title:'单价',type:'decimal',width:110,align:'left'},
                       {field:'remark',title:'备注',type:'string',width:220,align:'left'},
                       {field:'CreateID',title:'CreateID',type:'int',width:100,hidden:true,align:'left'},
                       {field:'Creator',title:'Creator',type:'string',width:100,hidden:true,align:'left'},
                       {field:'CreateDate',title:'CreateDate',type:'datetime',width:150,hidden:true,align:'left',sort:true},
                       {field:'ModifyID',title:'ModifyID',type:'int',width:100,hidden:true,align:'left'},
                       {field:'Modifier',title:'Modifier',type:'string',width:100,hidden:true,align:'left'},
                       {field:'ModifyDate',title:'ModifyDate',type:'datetime',width:150,hidden:true,align:'left',sort:true}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
