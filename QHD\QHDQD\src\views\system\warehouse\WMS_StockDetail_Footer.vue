<template>
	<div>
		<h3>
			<i class="ivu-icon ivu-icon-ios-information-circle-outline"></i>
			<div class="desc-text">
				<i class="el-icon-s-grid"></i>
				<span>库存明细</span>
			</div>
		</h3>
		<!-- url="/api/SellOrder/getDetailPage"-->
		<div style="padding: 10px; background: white; padding-top: 0">
			<vol-table ref="tableList" :loadKey="true" :columns="columns" :pagination-hide="true" :height="420"
				:defaultLoadPage="false" @loadBefore="loadBefore" url="api/WMS_Stock/getDetailPage"
				:row-index="true" :index="false" :ck="false"></vol-table>
		</div>
	</div>
</template>
<script>
import VolTable from "@/components/basic/VolTable.vue";
export default {
	components: {
		VolTable,
	},
	methods: {
		loadBefore(params, callback) {
			return callback(true);
		},
	},
    data() {
        return {
           tableData: [],
            //更多table配置见文档：http://doc.volcore.xyz/table
            //明细表格配置，从生成的vue文件中可以复制过来
            columns: [{field:'DetailID',title:'序号',type:'int',width:110,require:true,align:'left',sort:true},
                       {field:'ID',title:'库存序号',type:'int',width:110,hidden:true,align:'left'},
                       {field:'ItemType',title:'物料类型',type:'int',bind:{ key:'物料类型',data:[]},width:110,align:'left'},
                       {field:'ItemName',title:'物料名称',type:'string',width:110,align:'left'},
                       {field:'ItemCode',title:'物料编号',type:'string',width:110,align:'left'},
                       {field:'ItemState',title:'物料状态',type:'string',width:110,align:'left'},
                       {field:'lifetime',title:'可使用时间',type:'float',width:110,align:'left'},
                       {field:'OrderID',title:'订单编号',type:'string',width:120,hidden:true,align:'left'}],
        }
    }
}
</script>
<style scoped>
h3 {
    font-weight: 500;
    padding-left: 10px;
    background: white;
    margin-top: 8px;
    padding-bottom: 5px;
}
</style>
