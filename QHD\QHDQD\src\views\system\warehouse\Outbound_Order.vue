<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/warehouse/Outbound_Order.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/warehouse/Outbound_Order.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'ID',
                footer: "Foots",
                cnName: '出库单据',
                name: 'warehouse/Outbound_Order',
                url: "/Outbound_Order/",
                sortName: "ID"
            });
            const editFormFields = ref({"NoticeOrder":"","OutboundCode":"","OutboundTime":"","OutboundType":"","MaterialCode":"","MaterialName":"","SpecModel":"","OutboundQuantity":"","unit":"","BatchNumber":"","Receiver":""});
            const editFormOptions = ref([[{"title":"通知单号","field":"NoticeOrder"},
                               {"title":"出库单号","field":"OutboundCode"}],
                              [{"title":"出库日期","field":"OutboundTime","type":"datetime"},
                               {"title":"出库类型","field":"OutboundType"}],
                              [{"dataKey":"远程 物料 夹具","data":[],"title":"物料编码","field":"MaterialCode","type":"remoteSearch"},
                               {"title":"物料名称","field":"MaterialName"}],
                              [{"title":"规格型号","field":"SpecModel"},
                               {"title":"出库数量","field":"OutboundQuantity","type":"decimal"}],
                              [{"title":"单位","field":"unit"},
                               {"title":"批次号","field":"BatchNumber"}],
                              [{"title":"接收方","field":"Receiver"}]]);
            const searchFormFields = ref({"NoticeOrder":"","OutboundCode":"","OutboundTime":"","MaterialCode":"","MaterialName":""});
            const searchFormOptions = ref([[{"title":"通知单号","field":"NoticeOrder","type":"text"},{"title":"出库单号","field":"OutboundCode","type":"text"},{"title":"出库日期","field":"OutboundTime","type":"datetime"},{"dataKey":"远程 物料 夹具","data":[],"title":"物料编码","field":"MaterialCode","type":"text"},{"title":"物料名称","field":"MaterialName","type":"text"}]]);
            const columns = ref([{field:'ID',title:'编号',type:'int',width:110,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'NoticeOrder',title:'通知单号',type:'string',width:180,align:'left',sort:true},
                       {field:'OutboundCode',title:'出库单号',type:'string',link:true,width:180,align:'left'},
                       {field:'OutboundTime',title:'出库日期',type:'datetime',width:150,align:'left',sort:true},
                       {field:'OutboundType',title:'出库类型',type:'string',width:110,align:'left'},
                       {field:'MaterialCode',title:'物料编码',type:'string',bind:{ key:'远程 物料 夹具',data:[]},width:180,align:'left'},
                       {field:'MaterialName',title:'物料名称',type:'string',width:180,align:'left'},
                       {field:'SpecModel',title:'规格型号',type:'string',width:120,align:'left'},
                       {field:'OutboundQuantity',title:'出库数量',type:'int',width:110,align:'left'},
                       {field:'unit',title:'单位',type:'string',width:110,align:'left'},
                       {field:'BatchNumber',title:'批次号',type:'string',width:180,align:'left'},
                       {field:'Receiver',title:'接收方',type:'string',width:110,align:'left'},
                       {field:'Status',title:'状态',type:'int',bind:{ key:'出库状态',data:[]},width:80,align:'left'}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
