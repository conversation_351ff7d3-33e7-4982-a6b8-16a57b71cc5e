<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/agv/AGV_Task_Records.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/agv/AGV_Task_Records.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'id',
                footer: "Foots",
                cnName: 'AGV回传单',
                name: 'agv/AGV_Task_Records',
                url: "/AGV_Task_Records/",
                sortName: "id"
            });
            const editFormFields = ref({"task_id":"","station_name":"","arrival_time":"","status":"","task_type":"","task_status":""});
            const editFormOptions = ref([[{"title":"AGV回传编号","required":true,"field":"task_id"},
                               {"title":"站名","required":true,"field":"station_name"}],
                              [{"title":"到达时间","field":"arrival_time","type":"datetime"},
                               {"title":"状态","required":true,"field":"status"}],
                              [{"title":"任务类型","required":true,"field":"task_type"},
                               {"title":"任务状态","required":true,"field":"task_status"}]]);
            const searchFormFields = ref({});
            const searchFormOptions = ref([]);
            const columns = ref([{field:'id',title:'id',type:'int',width:110,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'task_id',title:'AGV回传编号',type:'string',width:110,require:true,align:'left',sort:true},
                       {field:'station_name',title:'站名',type:'string',width:120,require:true,align:'left'},
                       {field:'arrival_time',title:'到达时间',type:'datetime',width:110,align:'left',sort:true},
                       {field:'status',title:'状态',type:'string',width:110,require:true,align:'left'},
                       {field:'task_type',title:'任务类型',type:'string',width:110,require:true,align:'left'},
                       {field:'task_status',title:'任务状态',type:'string',width:110,require:true,align:'left'},
                       {field:'create_time',title:'创建时间',type:'datetime',width:110,align:'left',sort:true}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
