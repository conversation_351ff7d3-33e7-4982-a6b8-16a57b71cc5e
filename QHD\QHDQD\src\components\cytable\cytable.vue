<template>
  <div class="cy-table">
    <el-table
      class="cy-table-class"
      ref="tableRef"
      v-bind="$attrs"
      :border="config.border"
      :data="props.tableData"
      :height="config.height ? config.height : 'auto'"
      :show-summary="config.showSummary"
      style="width: 100%"
      :row-key="getRowKey"
      @selection-change="onSelectionChange"
    >
      <el-table-column
        type="expand"
        v-if="config.expand"
        :label="config.expandLabel"
        :width="config.expandWidth"
      >
        <template #default="scope">
          <slot name="expandSlot" v-bind="scope"></slot>
        </template>
      </el-table-column>
      <el-table-column
        type="selection"
        :reserve-selection="true"
        width="40"
        v-if="config.isSelection"
      />
      <el-table-column v-if="config.showIndex" type="index" label="序号" width="80" />
      <el-table-column
        v-for="(item, index) in props.column"
        :key="item.prop"
        v-bind="item"
        :width="item.width || 'auto'"
      >
        <el-table-column
          v-if="item.children"
          v-for="(subItem, index) in item.children"
          :key="subItem.prop"
          v-bind="subItem"
        >
          <template #default="scope" v-if="$slots[subItem.prop]">
            <slot :name="subItem.prop" :column="item" v-bind="scope"></slot>
          </template>
        </el-table-column>

        <template #default="scope" v-if="!item.children && $slots[item.prop]">
          <slot :name="item.prop" :columnItem="item" v-bind="scope"></slot>
        </template>

        <template #default="scope" v-if="!$slots[item.prop]">
          <el-input
            v-if="scope.row._rowStatus == 'edit' && item.type == 'input'"
            :name="item.prop"
            :type="item.inputType"
            @click="handleInputClick(scope, item)"
            v-model="scope.row[item.prop]"
          ></el-input>
          <el-switch
            v-if="scope.row._rowStatus == 'edit' && item.type == 'switch'"
            :name="item.prop"
            v-model="scope.row[item.prop]"
            class="ml-2"
            style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
          />
          <el-date-picker
            v-if="scope.row._rowStatus == 'edit' && item.type == 'date'"
            v-model="scope.row[item.prop]"
            type="date"
            :name="item.prop"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            placeholder="选择时间"
          />
          <el-select-v2
          clearable
            v-model="scope.row[item.prop]"
            v-if="
              (scope.row._rowStatus == 'edit' || scope.row._rowStatus == 'editView') &&
              item.type == 'select'
            "
            placeholder="请选择节点类型"
            :disabled="scope.row._rowStatus === 'editView'"
            :options="item.options"
          />
          <div v-if="scope.row._rowStatus === 'view'">{{ scope.row[item.prop] }}</div>
        </template>
      </el-table-column>
    </el-table>
    <div class="cy-table-footer" v-if="props.config.pageNum && props.config.total">
      <el-pagination
        v-model:current-page="props.config.pageNum"
        v-model:page-size="props.config.pageSize"
        :pager-count="5"
        :page-sizes="[10, 20, 30]"
        :total="props.config ? props.config.total : 0"
        layout="total,  prev, pager, next, jumper"
        background
        @size-change="onHandleSizeChange"
        @current-change="onHandleCurrentChange"
      >
      </el-pagination>
    </div>
  </div>
</template>
<script setup>
import { defineProps, defineEmits, reactive, watch, ref } from "vue";
let props = defineProps({
  tableData: {
    type: Array,
    default: () => [],
  },
  //指定key值
  idValue: {
    type: String,
    default: "id",
  },
  // 配置项
  config: {
    type: Object,
    default: () => {
      return {
        total: 0,
        pageNum: 1,
        pageSize: 20,
        isSelection: false,
        border: true,
        expandLabel: "明细",
        showSummary: false,
      };
    },
  },
  column: {
    type: Array,
    default: () => [],
  },
});

// 定义子组件向父组件传值/事件
const emit = defineEmits(["delRow", "pageChange", "sortHeader", "onSelectionChange"]);

const tableRef = ref();

const getRowKey = (row) => {
  return row[props.idValue] || row.code;
};

const state = reactive({
  page: {
    pageNum: 1,
    pageSize: 10,
  },
  selectlist: [],
  checkListAll: true,
  checkListIndeterminate: false,
});

// 表格多选改变时，用于导出
const onSelectionChange = (val) => {
  state.selectlist = val;
  emit("onSelectionChange", state.selectlist);
};

const clearSelection = () => {
  tableRef.value.clearSelection();
};

const handleInputClick = (row, column) => {
  if (column.handleInputClick) {
    column.handleInputClick(row, column);
  }
};

// 删除当前项
const onDelRow = (row) => {
  emit("delRow", row);
};
// 分页改变
const onHandleSizeChange = (val) => {
  state.page.pageSize = val;
  emit("pageChange", state.page);
};
// 分页改变
const onHandleCurrentChange = (val) => {
  state.page.pageNum = val;
  emit("pageChange", state.page);
};

watch(state, (newValue, oldValue) => {
  props.config.pageNum = newValue.page.pageNum;
  props.config.pageSize = newValue.page.pageSize;
  emit("update:config", props.config);
});
//只监听一次，获取分页配置
watch(props.config, (newValue, oldValue) => {
  // console.log('监听一下你',newValue)
  state.page.pageSize = newValue.pageSize;
},{ once: true });

defineExpose({
  clearSelection,
});
</script>
<style lang="less" scoped>
.cy-table {
  width: 100%;
  height: 100%;
  margin: 10px 0;
  /deep/.cy-table-class {
    .cell {
      text-align: center;
    }
  }
  .cy-table-footer {
    text-align: center;
    margin-top: 10px;
    padding-top: 10px;
    width: 100%;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
