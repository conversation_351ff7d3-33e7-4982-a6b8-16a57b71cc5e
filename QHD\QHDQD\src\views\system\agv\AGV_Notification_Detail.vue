<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/agv/AGV_Notification_Detail.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/agv/AGV_Notification_Detail.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'DetailID',
                footer: "Foots",
                cnName: 'AGV通知明细',
                name: 'agv/AGV_Notification_Detail',
                url: "/AGV_Notification_Detail/",
                sortName: "DetailID"
            });
            const editFormFields = ref({"MaterialCode":"","MaterialName":"","MaterialType":"","SpecModel":"","Unit":"","MTCposition":"","Quantity":"","TaskNumber":"","Remark":""});
            const editFormOptions = ref([[{"title":"规格型号","field":"SpecModel"},
                               {"title":"材质","field":"MTCposition"},
                               {"title":"备注信息","field":"Remark"},
                               {"title":"物料编码","required":true,"field":"MaterialCode"},
                               {"dataKey":"物料列表","data":[],"title":"物料名称","required":true,"field":"MaterialName","type":"select"},
                               {"title":"物料类型","required":true,"field":"MaterialType"},
                               {"title":"单位","required":true,"field":"Unit"},
                               {"title":"数量","required":true,"field":"Quantity","type":"number"},
                               {"title":"任务编号","field":"TaskNumber"}]]);
            const searchFormFields = ref({});
            const searchFormOptions = ref([]);
            const columns = ref([{field:'DetailID',title:'明细ID，自增主键，唯一标识每条明细记录',type:'int',width:110,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'MaterialCode',title:'物料编码',type:'string',width:120,require:true,align:'left',sort:true},
                       {field:'MaterialName',title:'物料名称',type:'string',bind:{ key:'物料列表',data:[]},width:120,require:true,align:'left'},
                       {field:'MaterialType',title:'物料类型',type:'string',width:110,require:true,align:'left'},
                       {field:'SpecModel',title:'规格型号',type:'string',width:180,align:'left'},
                       {field:'Unit',title:'单位',type:'string',width:110,require:true,align:'left'},
                       {field:'MTCposition',title:'材质',type:'string',width:110,align:'left'},
                       {field:'Quantity',title:'数量',type:'int',width:110,require:true,align:'left'},
                       {field:'TaskNumber',title:'任务编号',type:'string',width:110,hidden:true,align:'left'},
                       {field:'Remark',title:'备注信息',type:'string',width:220,align:'left'},
                       {field:'CreateTime',title:'创建时间',type:'datetime',width:150,hidden:true,align:'left',sort:true},
                       {field:'UpdateTime',title:'更新时间',type:'datetime',width:150,hidden:true,align:'left',sort:true},
                       {field:'MIID',title:'MIID',type:'int',width:110,hidden:true,align:'left'}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
