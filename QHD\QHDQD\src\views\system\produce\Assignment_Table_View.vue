<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/produce/Assignment_Table_View.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/produce/Assignment_Table_View.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'ASSID',
                footer: "Foots",
                cnName: '生产排产',
                name: 'produce/Assignment_Table_View',
                url: "/Assignment_Table_View/",
                sortName: "ASSID"
            });
            const editFormFields = ref({"OrderNumber":"","START_DATE":"","DUE_DATE":"","PLAN_QTY":"","ITEM_MODEL":"","ITEM_NORM":"","Priority":"","SerialNumber":""});
            const editFormOptions = ref([[{"title":"订单编号","field":"OrderNumber","colSize":3},
                               {"title":"计划开工日期","field":"START_DATE","colSize":3,"type":"datetime"},
                               {"title":"计划完工日期","field":"DUE_DATE","colSize":3,"type":"datetime"},
                               {"title":"数量","field":"PLAN_QTY","colSize":3,"type":"number"}],
                              [{"title":"物料编号","field":"ITEM_MODEL","colSize":3},
                               {"title":"规格","field":"ITEM_NORM","colSize":3},
                               {"dataKey":"生产排单优先级","data":[],"title":"优先级","field":"Priority","colSize":3,"type":"select"},
                               {"title":"排序","field":"SerialNumber","colSize":3,"type":"number"}]]);
            const searchFormFields = ref({"OrderNumber":"","START_DATE":"","DUE_DATE":"","ITEM_MODEL":"","ITEM_NORM":"","Priority":""});
            const searchFormOptions = ref([[{"title":"订单编号","field":"OrderNumber"},{"title":"物料编号","field":"ITEM_MODEL"},{"title":"规格","field":"ITEM_NORM"}],[{"title":"计划开工日期","field":"START_DATE","type":"date"},{"title":"计划完工日期","field":"DUE_DATE","type":"date"},{"dataKey":"生产排单优先级","data":[],"title":"优先级","field":"Priority","type":"select"}]]);
            const columns = ref([{field:'ASSID',title:'ASSID',type:'int',width:110,hidden:true,require:true,align:'left'},
                       {field:'OrderNumber',title:'订单编号',type:'string',link:true,width:120,align:'left',sort:true},
                       {field:'START_DATE',title:'计划开工日期',type:'string',width:120,align:'left'},
                       {field:'DUE_DATE',title:'计划完工日期',type:'string',width:120,align:'left'},
                       {field:'PLAN_QTY',title:'数量',type:'string',width:110,align:'left'},
                       {field:'ITEM_MODEL',title:'物料编号',type:'string',width:120,align:'left'},
                       {field:'ITEM_NORM',title:'规格',type:'string',width:120,align:'left'},
                       {field:'Priority',title:'优先级',type:'string',bind:{ key:'生产排单优先级',data:[]},width:110,align:'left'},
                       {field:'SerialNumber',title:'排序',type:'int',width:110,align:'left'},
                       {field:'ERPDue_DATE',title:'ERP计划完成日期',type:'string',width:120,readonly:true,align:'left'},
                       {field:'State',title:'状态',type:'string',width:120,align:'left'}]);
            const detail = ref({
                cnName: "订单排产任务",
                table: "Assignment_Detail",
                columns: [{field:'ID',title:'编号',type:'int',width:110,hidden:true,require:true,align:'left'},
                       {field:'TaskCoding',title:'任务编号',type:'string',width:120,edit:{type:''},require:true,align:'left',sort:true},
                       {field:'OP_NAME',title:'任务名称',type:'string',width:120,hidden:true,edit:{type:''},align:'left'},
                       {field:'TaskType',title:'任务类型',type:'string',bind:{ key:'任务类型',data:[]},width:110,edit:{type:'select'},require:true,align:'left'},
                       {field:'Priority',title:'优先级',type:'string',bind:{ key:'生产排单优先级',data:[]},width:110,edit:{type:'select'},require:true,align:'left'},
                       {field:'START_DATE',title:'开始时间',type:'string',width:120,edit:{type:'date'},require:true,align:'left'},
                       {field:'DUE_DATE',title:'结束时间',type:'string',width:120,edit:{type:'date'},require:true,align:'left'},
                       {field:'PLAN_HOURS',title:'任务状态',type:'string',bind:{ key:'任务状态',data:[]},width:110,edit:{type:'select'},align:'left'},
                       {field:'ASSID',title:'排产id',type:'int',width:120,hidden:true,align:'left'}],
                sortName: "OrderNumber",
                key: "ID"
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
