<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/warehouse/WMS_Stock.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/warehouse/WMS_Stock.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'ID',
                footer: "Foots",
                cnName: '库存管理',
                name: 'warehouse/WMS_Stock',
                url: "/WMS_Stock/",
                sortName: "ID"
            });
            const editFormFields = ref({});
            const editFormOptions = ref([]);
            const searchFormFields = ref({});
            const searchFormOptions = ref([]);
            const columns = ref([{field:'ID',title:'id',type:'int',width:110,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'WarehouseId',title:'存放仓库',type:'int',bind:{ key:'仓库库位',data:[]},width:110,align:'left',sort:true},
                       {field:'ItemType',title:'物料类型',type:'int',bind:{ key:'物料类型',data:[]},width:80,align:'left'},
                       {field:'ItemName',title:'物料名称',type:'string',width:220,align:'left'},
                       {field:'ItemCode',title:'物料编码',type:'string',width:220,align:'left'},
                       {field:'ItemModel',title:'规格型号',type:'string',width:220,align:'left'},
                       {field:'ToolInfoId',title:'物料名称',type:'int',bind:{ key:'物料名称',data:[]},width:110,hidden:true,align:'left'},
                       {field:'qty',title:'库存总量',type:'decimal',width:110,align:'left'},
                       {field:'BatchesId',title:'批次号',type:'string',width:220,hidden:true,require:true,align:'left'},
                       {field:'CreateID',title:'CreateID',type:'int',width:100,hidden:true,align:'left'},
                       {field:'Creator',title:'创建人',type:'string',width:100,hidden:true,align:'left'},
                       {field:'CreateDate',title:'创建时间',type:'datetime',width:150,align:'left',sort:true},
                       {field:'ModifyID',title:'ModifyID',type:'int',width:100,hidden:true,align:'left'},
                       {field:'Modifier',title:'修改人',type:'string',width:100,hidden:true,align:'left'},
                       {field:'ModifyDate',title:'修改时间',type:'datetime',width:150,hidden:true,align:'left',sort:true},
                       {field:'Static',title:'状态',type:'string',bind:{ key:'库存状态',data:[]},width:220,align:'left'}]);
            const detail = ref({
                cnName: "库存明细",
                table: "WMS_StockDetail",
                columns: [{field:'DetailID',title:'序号',type:'int',width:110,require:true,align:'left',sort:true},
                       {field:'ID',title:'库存序号',type:'int',width:110,hidden:true,align:'left'},
                       {field:'ItemType',title:'物料类型',type:'string',bind:{ key:'物料类型',data:[]},width:110,align:'left'},
                       {field:'ItemName',title:'物料名称',type:'string',width:110,align:'left'},
                       {field:'ItemCode',title:'物料编号',type:'string',width:110,align:'left'},
                       {field:'ItemState',title:'物料状态',type:'string',width:110,align:'left'},
                       {field:'lifetime',title:'可使用时间',type:'float',width:110,align:'left'},
                       {field:'OrderID',title:'订单编号',type:'string',width:120,hidden:true,align:'left'}],
                sortName: "DetailID",
                key: "DetailID"
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
