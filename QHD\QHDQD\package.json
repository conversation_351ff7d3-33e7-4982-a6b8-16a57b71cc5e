{"name": "vol", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "test:unit": "vue-cli-service test:unit", "test:e2e": "vue-cli-service test:e2e", "lint": "vue-cli-service lint"}, "dependencies": {"@microsoft/signalr": "^7.0.3", "@vue/babel-helper-vue-jsx-merge-props": "^1.4.0", "ali-oss": "^6.17.1", "axios": "^0.21.1", "core-js": "^3.8.3", "echarts": "^5.4.1", "element-plus": "^2.2.32", "less": "^4.1.3", "qrcode": "^1.5.4", "vue": "^3.4.21", "vue-draggable-next": "^2.1.1", "vue-router": "^4.1.6", "vuex": "^4.1.0", "wangeditor": "^4.7.15"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@babel/plugin-transform-react-jsx": "^7.23.4", "@vue/babel-plugin-jsx": "^1.2.1", "@vue/babel-preset-jsx": "^1.4.0", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-e2e-cypress": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-unit-jest": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/test-utils": "^2.0.0-0", "@vue/vue3-jest": "^27.0.0-alpha.1", "babel-jest": "^27.0.6", "cypress": "^9.7.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "jest": "^27.0.5", "less": "^4.0.0", "less-loader": "^8.0.0", "stylus": "^0.63.0", "stylus-loader": "^8.1.0", "vite-plugin-lang-jsx": "^1.5.3"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}, "overrides": [{"files": ["**/__tests__/*.{j,t}s?(x)", "**/tests/unit/**/*.spec.{j,t}s?(x)"], "env": {"jest": true}}]}, "eslintIgnore": ["*"], "jest": {"preset": "@vue/cli-plugin-unit-jest"}}