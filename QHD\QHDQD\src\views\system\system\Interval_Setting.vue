<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/system/Interval_Setting.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/system/Interval_Setting.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'ID',
                footer: "Foots",
                cnName: '区间设置',
                name: 'system/Interval_Setting',
                url: "/Interval_Setting/",
                sortName: "ID"
            });
            const editFormFields = ref({"IntervalName":"","IntervalVlue":"","IntervaType":""});
            const editFormOptions = ref([[{"title":"区间名","field":"IntervalName","type":"text"},
                               {"title":"区间值","field":"IntervalVlue","type":"text"},
                               {"dataKey":"Interval","data":[],"title":"区间类型","field":"IntervaType","type":"select"}]]);
            const searchFormFields = ref({"IntervalName":"","IntervalVlue":"","IntervaType":""});
            const searchFormOptions = ref([[{"title":"区间名","field":"IntervalName","type":"text"},{"title":"区间值","field":"IntervalVlue","type":"text"},{"dataKey":"Interval","data":[],"title":"区间类型","field":"IntervaType","type":"select"}]]);
            const columns = ref([{field:'ID',title:'编号',type:'int',width:110,require:true,align:'left',sort:true},
                       {field:'IntervalName',title:'区间名',type:'string',width:160,align:'left'},
                       {field:'IntervalVlue',title:'区间值',type:'string',width:160,align:'left'},
                       {field:'IntervaType',title:'区间类型',type:'string',bind:{ key:'Interval',data:[]},width:110,align:'left'}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
