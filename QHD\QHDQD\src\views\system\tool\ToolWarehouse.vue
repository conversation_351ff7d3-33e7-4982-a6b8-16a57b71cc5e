<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/tool/ToolWarehouse.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/tool/ToolWarehouse.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'ID',
                footer: "Foots",
                cnName: '线边库刀具管理',
                name: 'tool/ToolWarehouse',
                url: "/ToolWarehouse/",
                sortName: "ID"
            });
            const editFormFields = ref({"WarehouseNo":"","WarehouseNature":"","WarehouseType":"","ToolModelNo":"","ToolNo":"","ToolCategory":"","ToolSpecification":"","ToolHolderSpecification":"","ClampingLength":"","TotalToolLength":"","MachiningMaterial":"","FunctionUsage":"","OffsetNoH":"","OffsetNoD":"","MonitoringMethod":"","ReplacementMethod":"","SetValue":"","ActualValue":"","ToolSettingRequirement":"","ToolStatus":"","WarehouseStatus":""});
            const editFormOptions = ref([[{"title":"仓号","field":"WarehouseNo","type":"number"},
                               {"title":"仓号性质","field":"WarehouseNature"},
                               {"title":"仓号类型","field":"WarehouseType"}],
                              [{"title":"刀型编号","field":"ToolModelNo"},
                               {"title":"刀具编号","field":"ToolNo"},
                               {"title":"刀具类别","field":"ToolCategory"}],
                              [{"title":"刀具规格","field":"ToolSpecification"},
                               {"title":"刀柄规格","field":"ToolHolderSpecification"},
                               {"title":"装夹长度","field":"ClampingLength","type":"decimal"}],
                              [{"title":"刀具总长","field":"TotalToolLength","type":"decimal"},
                               {"title":"加工材质","field":"MachiningMaterial"},
                               {"title":"使用功能","field":"FunctionUsage"}],
                              [{"title":"刀补号H","field":"OffsetNoH"},
                               {"title":"刀补号D","field":"OffsetNoD"},
                               {"title":"监控方式","field":"MonitoringMethod"}],
                              [{"title":"更换方式","field":"ReplacementMethod"},
                               {"title":"设定值","field":"SetValue","type":"decimal"},
                               {"title":"实际值","field":"ActualValue","type":"decimal"}],
                              [{"title":"对刀要求","field":"ToolSettingRequirement"},
                               {"title":"刀具状态","field":"ToolStatus"},
                               {"title":"仓位状态","field":"WarehouseStatus"}]]);
            const searchFormFields = ref({"WarehouseNo":"","WarehouseNature":"","WarehouseType":"","ToolModelNo":"","ToolNo":"","ToolCategory":"","ToolSpecification":"","ToolHolderSpecification":"","ToolStatus":""});
            const searchFormOptions = ref([[{"title":"仓号","field":"WarehouseNo","type":"text"},{"title":"仓号性质","field":"WarehouseNature"},{"title":"仓号类型","field":"WarehouseType"}],[{"title":"刀型编号","field":"ToolModelNo"},{"title":"刀具编号","field":"ToolNo"},{"title":"刀具类别","field":"ToolCategory"}],[{"title":"刀具规格","field":"ToolSpecification"},{"title":"刀柄规格","field":"ToolHolderSpecification"},{"title":"刀具状态","field":"ToolStatus"}]]);
            const columns = ref([{field:'ID',title:'自增主键，唯一标识每条刀具记录',type:'int',width:110,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'WarehouseNo',title:'仓号',type:'int',width:110,align:'left',sort:true},
                       {field:'WarehouseNature',title:'仓号性质',type:'string',width:110,align:'left'},
                       {field:'WarehouseType',title:'仓号类型',type:'string',width:110,align:'left'},
                       {field:'ToolModelNo',title:'刀型编号',type:'string',width:110,align:'left'},
                       {field:'ToolNo',title:'刀具编号',type:'string',width:110,align:'left'},
                       {field:'ToolCategory',title:'刀具类别',type:'string',width:110,align:'left'},
                       {field:'ToolSpecification',title:'刀具规格',type:'string',width:110,align:'left'},
                       {field:'ToolHolderSpecification',title:'刀柄规格',type:'string',width:110,align:'left'},
                       {field:'ClampingLength',title:'装夹长度',type:'decimal',width:110,align:'left'},
                       {field:'TotalToolLength',title:'刀具总长',type:'decimal',width:110,align:'left'},
                       {field:'MachiningMaterial',title:'加工材质',type:'string',width:110,align:'left'},
                       {field:'FunctionUsage',title:'使用功能',type:'string',width:110,align:'left'},
                       {field:'OffsetNoH',title:'刀补号H',type:'string',width:110,align:'left'},
                       {field:'OffsetNoD',title:'刀补号D',type:'string',width:110,align:'left'},
                       {field:'MonitoringMethod',title:'监控方式',type:'string',width:110,align:'left'},
                       {field:'ReplacementMethod',title:'更换方式',type:'string',width:110,align:'left'},
                       {field:'SetValue',title:'设定值',type:'decimal',width:110,align:'left'},
                       {field:'ActualValue',title:'实际值',type:'decimal',width:110,align:'left'},
                       {field:'ToolSettingRequirement',title:'对刀要求',type:'string',width:110,align:'left'},
                       {field:'ToolStatus',title:'刀具状态',type:'string',width:110,align:'left'},
                       {field:'WarehouseStatus',title:'仓位状态',type:'string',width:110,align:'left'},
                       {field:'CreateTime',title:'创建时间',type:'datetime',width:150,align:'left',sort:true},
                       {field:'UpdateTime',title:'更新时间',type:'datetime',width:150,align:'left',sort:true}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
