<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/product/Pac_Routing_Op.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/product/Pac_Routing_Op.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'ID',
                footer: "Foots",
                cnName: '生产订单',
                name: 'product/Pac_Routing_Op',
                url: "/Pac_Routing_Op/",
                sortName: "ID"
            });
            const editFormFields = ref({});
            const editFormOptions = ref([]);
            const searchFormFields = ref({"company_code":"","order_no":"","op_name":"","item_code":"","item_name":"","item_model":"","item_norm":"","customer_code":""});
            const searchFormOptions = ref([[{"title":"公司名","field":"company_code","type":"like"},{"title":"任务号","field":"order_no","type":"like"},{"title":"工序名","field":"op_name","type":"like"},{"title":"物料编码","field":"item_code","type":"like"}],[{"title":"物料名称","field":"item_name","type":"like"},{"title":"规格型号","field":"item_model","type":"like"},{"title":"图号","field":"item_norm","type":"like"},{"title":"客户编码","field":"customer_code","type":"like"}]]);
            const columns = ref([{field:'ID',title:'主键',type:'int',width:80,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'company_code',title:'公司名',type:'string',width:220,require:true,align:'left',sort:true},
                       {field:'rs_id',title:'rs_id',type:'string',width:110,hidden:true,align:'left'},
                       {field:'order_no',title:'任务号',type:'string',width:150,require:true,align:'left'},
                       {field:'op_no',title:'工序号',type:'long',width:150,require:true,align:'left'},
                       {field:'op_name',title:'工序名',type:'string',width:150,align:'left'},
                       {field:'item_code',title:'物料编码',type:'string',width:150,align:'left'},
                       {field:'item_name',title:'物料名称',type:'string',width:180,align:'left'},
                       {field:'item_model',title:'规格型号',type:'string',width:150,align:'left'},
                       {field:'item_norm',title:'图号',type:'string',width:150,align:'left'},
                       {field:'plan_qty',title:'计划数量',type:'float',width:110,align:'left'},
                       {field:'work_no',title:'工作令号',type:'string',width:150,align:'left'},
                       {field:'plan_no',title:'计划号',type:'string',width:150,align:'left'},
                       {field:'customer_code',title:'客户编码',type:'string',width:150,align:'left'},
                       {field:'customer_name',title:'客户名称',type:'string',width:220,align:'left'},
                       {field:'unit_code',title:'计量单位',type:'string',width:110,align:'left'},
                       {field:'unit_name',title:'计量名称',type:'string',width:110,align:'left'},
                       {field:'dept_code',title:'责任部门编码',type:'string',width:150,align:'left'},
                       {field:'dept_name',title:'责任部门名称',type:'string',width:150,align:'left'},
                       {field:'wc_code',title:'工位编码',type:'string',width:150,align:'left'},
                       {field:'wc_name',title:'工位名称',type:'string',width:180,align:'left'},
                       {field:'start_date',title:'计划开始时间',type:'string',width:150,align:'left'},
                       {field:'due_date',title:'计划结束时间',type:'string',width:150,align:'left'},
                       {field:'urgency_flag',title:'urgency_flag',type:'string',width:110,hidden:true,align:'left'},
                       {field:'plan_hours',title:'工时（小时）',type:'float',width:110,align:'left'},
                       {field:'wc_abv',title:'wc_abv',type:'string',width:110,hidden:true,align:'left'},
                       {field:'job_kind',title:'工种编码',type:'string',width:150,align:'left'},
                       {field:'job_kind_name',title:'工种名称',type:'string',width:180,align:'left'},
                       {field:'earlist_date_s',title:'earlist_date_s',type:'string',width:110,hidden:true,align:'left'},
                       {field:'earlist_date_d',title:'earlist_date_d',type:'string',width:110,hidden:true,align:'left'},
                       {field:'latest_date_s',title:'latest_date_s',type:'string',width:110,hidden:true,align:'left'},
                       {field:'latest_date_d',title:'latest_date_d',type:'string',width:110,hidden:true,align:'left'},
                       {field:'priority',title:'priority',type:'long',width:110,hidden:true,align:'left'},
                       {field:'first_last',title:'首末序标记',type:'string',width:110,align:'left'},
                       {field:'cost_op',title:'cost_op',type:'string',width:110,hidden:true,align:'left'},
                       {field:'sfc_op',title:'sfc_op',type:'string',width:110,hidden:true,align:'left'},
                       {field:'cap_op',title:'cap_op',type:'string',width:110,hidden:true,align:'left'},
                       {field:'check_op',title:'check_op',type:'string',width:110,hidden:true,align:'left'},
                       {field:'qc_flag',title:'质检标记',type:'string',width:110,align:'left'},
                       {field:'op_flag',title:'op_flag',type:'string',width:110,hidden:true,align:'left'},
                       {field:'setup_time',title:'setup_time',type:'float',width:110,hidden:true,align:'left'},
                       {field:'setup_unit',title:'setup_unit',type:'string',width:110,hidden:true,align:'left'},
                       {field:'run_time',title:'run_time',type:'float',width:110,hidden:true,align:'left'},
                       {field:'run_unit',title:'run_unit',type:'string',width:110,hidden:true,align:'left'},
                       {field:'over_load',title:'over_load',type:'float',width:110,hidden:true,align:'left'},
                       {field:'post_time',title:'post_time',type:'float',width:110,hidden:true,align:'left'},
                       {field:'post_unit',title:'post_unit',type:'string',width:110,hidden:true,align:'left'},
                       {field:'provide_nums',title:'provide_nums',type:'long',width:110,hidden:true,align:'left'},
                       {field:'overlaped',title:'overlaped',type:'string',width:110,hidden:true,align:'left'},
                       {field:'move_qty',title:'move_qty',type:'float',width:110,hidden:true,align:'left'},
                       {field:'over_rate',title:'over_rate',type:'float',width:110,hidden:true,align:'left'},
                       {field:'move_days',title:'move_days',type:'long',width:110,hidden:true,align:'left'},
                       {field:'coop_flag',title:'外协标记',type:'string',width:110,align:'left'},
                       {field:'coop_code',title:'coop_code',type:'string',width:110,hidden:true,align:'left'},
                       {field:'coop_name',title:'coop_name',type:'string',width:110,hidden:true,align:'left'},
                       {field:'coop_op_cost',title:'coop_op_cost',type:'float',width:110,hidden:true,align:'left'},
                       {field:'coop_actual_cost',title:'coop_actual_cost',type:'float',width:110,hidden:true,align:'left'},
                       {field:'coop_hours',title:'coop_hours',type:'float',width:110,hidden:true,align:'left'},
                       {field:'tool_desc',title:'tool_desc',type:'string',width:110,hidden:true,align:'left'},
                       {field:'tool_flag',title:'tool_flag',type:'string',width:110,hidden:true,align:'left'},
                       {field:'customer_order',title:'工作令',type:'string',width:110,align:'left'},
                       {field:'dept_code_wc',title:'工序部门编码',type:'string',width:150,align:'left'},
                       {field:'dept_name_wc',title:'工序部门名称',type:'string',width:150,align:'left'},
                       {field:'note',title:'工艺流转码',type:'string',width:150,align:'left'},
                       {field:'start_flag',title:'start_flag',type:'string',width:110,hidden:true,align:'left'},
                       {field:'due_flag',title:'due_flag',type:'string',width:110,hidden:true,align:'left'},
                       {field:'actual_start_date',title:'实际开工时间',type:'string',width:110,align:'left'},
                       {field:'actual_end_date',title:'实际完工时间',type:'string',width:110,align:'left'},
                       {field:'accum_qty',title:'累计完成数量',type:'float',width:110,align:'left'},
                       {field:'scrap_qty',title:'废品数量',type:'float',width:110,align:'left'},
                       {field:'grade_qty_1',title:'grade_qty_1',type:'float',width:110,hidden:true,align:'left'},
                       {field:'grade_qty_2',title:'grade_qty_2',type:'float',width:110,hidden:true,align:'left'},
                       {field:'grade_qty_3',title:'grade_qty_3',type:'float',width:110,hidden:true,align:'left'},
                       {field:'grade_qty_4',title:'grade_qty_4',type:'float',width:110,hidden:true,align:'left'},
                       {field:'grade_qty_5',title:'grade_qty_5',type:'float',width:110,hidden:true,align:'left'},
                       {field:'actual_hours',title:'实际工时（小时）',type:'float',width:110,align:'left'},
                       {field:'scrap_hours',title:'scrap_hours',type:'float',width:110,hidden:true,align:'left'},
                       {field:'m_scrap_hours',title:'m_scrap_hours',type:'float',width:110,hidden:true,align:'left'},
                       {field:'revise_qty',title:'revise_qty',type:'float',width:110,hidden:true,align:'left'},
                       {field:'revise_hours',title:'revise_hours',type:'float',width:110,hidden:true,align:'left'},
                       {field:'op_status',title:'工序状态',type:'string',bind:{ key:'工序状态',data:[]},width:110,align:'left'},
                       {field:'assist_unit',title:'assist_unit',type:'string',width:110,hidden:true,align:'left'},
                       {field:'assist_name',title:'assist_name',type:'string',width:110,hidden:true,align:'left'},
                       {field:'assist_plan_qty',title:'assist_plan_qty',type:'float',width:110,hidden:true,align:'left'},
                       {field:'assist_actual_qty',title:'assist_actual_qty',type:'float',width:110,hidden:true,align:'left'},
                       {field:'idle_hours',title:'idle_hours',type:'float',width:110,hidden:true,align:'left'},
                       {field:'rs_char1',title:'考核字段',type:'string',width:120,hidden:true,align:'left'},
                       {field:'rs_char2',title:'下料小于10件免检备注、涂装说明',type:'string',width:120,hidden:true,align:'left'},
                       {field:'rs_int1',title:'rs_int1',type:'long',width:110,hidden:true,align:'left'},
                       {field:'rs_int2',title:'rs_int2',type:'long',width:110,hidden:true,align:'left'},
                       {field:'rs_float1',title:'rs_float1',type:'float',width:110,hidden:true,align:'left'},
                       {field:'rs_float2',title:'rs_float2',type:'float',width:110,hidden:true,align:'left'},
                       {field:'priority_t',title:'priority_t',type:'float',width:110,hidden:true,align:'left'},
                       {field:'other_hours',title:'other_hours',type:'float',width:110,hidden:true,align:'left'},
                       {field:'ration_hours',title:'ration_hours',type:'float',width:110,hidden:true,align:'left'},
                       {field:'coop_type',title:'外协类型071224add jfm 当外协标记=Y，值为M-加工类外协；否则空',type:'string',width:110,hidden:true,align:'left'},
                       {field:'agreement_no',title:'协议号071224add jfm 从采购协议中选择 ',type:'string',width:110,hidden:true,align:'left'},
                       {field:'a_seq_no',title:'协议行号071224add jfm 从采购协议中选择',type:'long',width:110,hidden:true,align:'left'},
                       {field:'coop_qty',title:'调为外协数量071224add jfm ',type:'float',width:110,hidden:true,align:'left'},
                       {field:'unique_flag',title:'单件标记',type:'string',width:110,align:'left'},
                       {field:'invoice_flag',title:'报工标记',type:'string',width:110,align:'left'},
                       {field:'invoice_actual_qty',title:'报工数量',type:'float',width:110,align:'left'},
                       {field:'invoice_actual_hours',title:'实际工时',type:'float',width:110,align:'left'},
                       {field:'invoice_scrap_qty',title:'工废数量',type:'float',width:110,align:'left'},
                       {field:'invoice_scrap_hours',title:'工废工时',type:'float',width:110,align:'left'},
                       {field:'invoice_m_scrap_qty',title:'料废数量',type:'float',width:110,align:'left'},
                       {field:'invoice_m_scrap_hours',title:'料废工时',type:'float',width:110,align:'left'},
                       {field:'invoice_scrap_pac_flag',title:'产生新件生产任务标记',type:'string',width:110,align:'left'},
                       {field:'invoice_duty_man',title:'操作工',type:'string',width:110,align:'left'},
                       {field:'invoice_duty_name',title:'操作工姓名',type:'string',width:110,align:'left'},
                       {field:'invoice_start_date',title:'实际开工日期',type:'string',width:110,align:'left'},
                       {field:'invoice_end_date',title:'实际完工日期',type:'string',width:110,align:'left'},
                       {field:'invoice_staff_code',title:'统计员',type:'string',width:110,align:'left'},
                       {field:'invoice_staff_name',title:'统计员姓名',type:'string',width:110,align:'left'},
                       {field:'invoice_check_man',title:'检验员',type:'string',width:110,align:'left'},
                       {field:'invoice_check_name',title:'检验员姓名',type:'string',width:110,align:'left'},
                       {field:'invoice_record_date',title:'报工日期',type:'string',width:110,align:'left'},
                       {field:'invoice_kind_code',title:'报工类型',type:'string',width:110,align:'left'},
                       {field:'new_old_flag',title:'旧件标记',type:'string',width:110,align:'left'},
                       {field:'op_class',title:'op_class',type:'string',width:110,hidden:true,align:'left'},
                       {field:'i_qty',title:'接收数量',type:'float',width:110,align:'left'},
                       {field:'o_qty',title:'交出数量',type:'float',width:110,align:'left'},
                       {field:'rate_unit_hours',title:'单位折算工时',type:'float',width:110,align:'left'},
                       {field:'norm_unit_hours',title:'norm_unit_hours',type:'float',width:110,hidden:true,align:'left'},
                       {field:'appoint_accum_qty',title:'已派工量',type:'float',width:110,align:'left'},
                       {field:'mutual_check_flag',title:'互检控制标记',type:'string',width:110,align:'left'},
                       {field:'routing_schedual_unit',title:'编制单位',type:'string',width:110,align:'left'},
                       {field:'order_adjust_flag',title:'order_adjust_flag',type:'string',width:110,hidden:true,align:'left'},
                       {field:'explosion_flag',title:'explosion_flag',type:'string',width:110,hidden:true,align:'left'},
                       {field:'start_shift_seq_s',title:'start_shift_seq_s',type:'string',width:110,hidden:true,align:'left'},
                       {field:'start_shift_desc_s',title:'start_shift_desc_s',type:'string',width:110,hidden:true,align:'left'},
                       {field:'end_shift_seq_s',title:'end_shift_seq_s',type:'string',width:110,hidden:true,align:'left'},
                       {field:'end_shift_desc_s',title:'end_shift_desc_s',type:'string',width:110,hidden:true,align:'left'},
                       {field:'start_shift_seq_e',title:'start_shift_seq_e',type:'string',width:110,hidden:true,align:'left'},
                       {field:'start_shift_desc_e',title:'start_shift_desc_e',type:'string',width:110,hidden:true,align:'left'},
                       {field:'end_shift_seq_e',title:'end_shift_seq_e',type:'string',width:110,hidden:true,align:'left'},
                       {field:'end_shift_desc_e',title:'end_shift_desc_e',type:'string',width:110,hidden:true,align:'left'},
                       {field:'pause_flag',title:'pause_flag',type:'string',width:110,hidden:true,align:'left'},
                       {field:'actual_start_time',title:'actual_start_time',type:'string',width:110,hidden:true,align:'left'},
                       {field:'actual_end_time',title:'actual_end_time',type:'string',width:110,hidden:true,align:'left'},
                       {field:'actual_start_shift',title:'actual_start_shift',type:'string',width:110,hidden:true,align:'left'},
                       {field:'actual_end_shift',title:'actual_end_shift',type:'string',width:110,hidden:true,align:'left'},
                       {field:'return_date',title:'外协工序要求送回日期',type:'string',width:110,align:'left'},
                       {field:'wc_code_old',title:'班组派工原工作中心',type:'string',width:110,align:'left'},
                       {field:'wc_name_old',title:'wc_name_old',type:'string',width:110,hidden:true,align:'left'},
                       {field:'select_id',title:'选择人id',type:'string',width:110,hidden:true,align:'left'},
                       {field:'select_flag',title:'点选标记',type:'string',width:110,hidden:true,align:'left'},
                       {field:'bill_time',title:'交接录入时间--交接单确认时写入',type:'string',width:110,align:'left'},
                       {field:'send_man_code',title:'交出人编码',type:'string',width:110,align:'left'},
                       {field:'send_man_name',title:'交出人姓名',type:'string',width:110,align:'left'},
                       {field:'con_man_code',title:'审核人编码',type:'string',width:110,align:'left'},
                       {field:'con_man_name',title:'审核人姓名',type:'string',width:110,align:'left'},
                       {field:'con_time',title:'审核时间',type:'string',width:110,align:'left'},
                       {field:'send_dept_code_wc',title:'交出工序部门',type:'string',width:110,align:'left'},
                       {field:'send_dept_name_wc',title:'交出工序部门名称',type:'string',width:110,align:'left'},
                       {field:'rece_dept_code_wc',title:'接收工序部门',type:'string',width:110,align:'left'},
                       {field:'rece_dept_name_wc',title:'接收工序部门名称',type:'string',width:110,align:'left'},
                       {field:'send_qty',title:'交接数量',type:'float',width:110,align:'left'},
                       {field:'rece_time',title:'接收确认时间',type:'string',width:110,align:'left'},
                       {field:'rece_man_code',title:'接收人编码',type:'string',width:110,align:'left'},
                       {field:'rece_man_name',title:'接收人姓名',type:'string',width:110,align:'left'},
                       {field:'staff_man_code',title:'记账人编码',type:'string',width:110,align:'left'},
                       {field:'staff_man_name',title:'记账人姓名',type:'string',width:110,align:'left'},
                       {field:'staff_time',title:'记账时间',type:'string',width:110,align:'left'},
                       {field:'app_date',title:'派工日期',type:'string',width:110,align:'left'},
                       {field:'app_period',title:'派工周期',type:'string',width:110,align:'left'},
                       {field:'app_people',title:'派工人',type:'string',width:110,align:'left'},
                       {field:'wc_codebak',title:'wc_codebak',type:'string',width:110,hidden:true,align:'left'},
                       {field:'wc_namebak',title:'wc_namebak',type:'string',width:110,hidden:true,align:'left'},
                       {field:'dept_codewcbak',title:'dept_codewcbak',type:'string',width:110,hidden:true,align:'left'},
                       {field:'dept_namewcbak',title:'dept_namewcbak',type:'string',width:110,hidden:true,align:'left'},
                       {field:'start_people',title:'开工人',type:'string',width:110,align:'left'},
                       {field:'check_jx',title:'check_jx',type:'string',width:110,hidden:true,align:'left'},
                       {field:'rs_char3',title:'下序工作中心',type:'string',width:110,align:'left'},
                       {field:'aps_write',title:'APS写入标记',type:'string',width:110,align:'left'},
                       {field:'write_time',title:'APS写入时间',type:'string',width:110,align:'left'},
                       {field:'aps_remarks',title:'aps_remarks',type:'string',width:110,hidden:true,align:'left'},
                       {field:'print1_flag',title:'打印标记',type:'string',width:110,align:'left'},
                       {field:'aps_up_flag',title:'APS更新时间标记',type:'string',width:110,align:'left'},
                       {field:'aps_up_time',title:'APS更新时间时间',type:'string',width:110,align:'left'},
                       {field:'print1_time',title:'打印时间',type:'string',width:110,align:'left'},
                       {field:'print1_nums',title:'打印次数',type:'float',width:110,align:'left'},
                       {field:'aps_up_remarks',title:'20210321更新数据备注',type:'string',width:110,hidden:true,align:'left'},
                       {field:'latest_date_aps_s',title:'记录3月21同步前日期和APS第一次传入日期',type:'string',width:110,hidden:true,align:'left'},
                       {field:'latest_date_aps_d',title:'记录3月21同步前日期和APS第一次传入日期',type:'string',width:110,hidden:true,align:'left'},
                       {field:'lock_flag',title:'APS锁定标记',type:'string',width:110,align:'left'},
                       {field:'is_material',title:'是否有需求，每天更新',type:'string',width:110,hidden:true,align:'left'},
                       {field:'parent_wc_code',title:'父项工位信息,定时任务，每天晚上更新',type:'string',width:110,hidden:true,align:'left'},
                       {field:'parent_wc_name',title:'父项工位信息',type:'string',width:110,hidden:true,align:'left'},
                       {field:'wc_code3',title:'更改的考核部门，aps手工录入',type:'string',width:110,align:'left'},
                       {field:'op_mz_hours',title:'铆作工时',type:'float',width:110,align:'left'},
                       {field:'op_hj_hours',title:'焊接工时',type:'float',width:110,align:'left'},
                       {field:'op_ql_hours',title:'清理工时',type:'float',width:110,align:'left'},
                       {field:'op_dy_hours',title:'打压工时',type:'float',width:110,align:'left'},
                       {field:'next_start',title:'下序开工日期',type:'string',width:110,align:'left'},
                       {field:'check_quality_qty',title:'抽检数量',type:'float',width:110,align:'left'},
                       {field:'gz_people',title:'改件人员,强传标记',type:'string',width:110,hidden:true,align:'left'},
                       {field:'next_op',title:'下序工序号',type:'string',width:110,align:'left'},
                       {field:'aps_pri',title:'优先度',type:'float',width:110,align:'left'},
                       {field:'inware_flag',title:'涂装入库标记',type:'string',width:110,align:'left'},
                       {field:'item_amt',title:'价值',type:'float',width:110,align:'left'},
                       {field:'coop_demand_date',title:'外协需求日期',type:'string',width:110,align:'left'},
                       {field:'coop_parent_order',title:'外协父项',type:'string',width:110,align:'left'},
                       {field:'coop_parent_wc_code',title:'外协返回工位',type:'string',width:110,align:'left'},
                       {field:'rs_char4',title:'上序部门',type:'string',width:110,align:'left'},
                       {field:'rs_char5',title:'上序工作中心编码',type:'string',width:110,align:'left'},
                       {field:'rs_char5_name',title:'上序工作中心名称',type:'string',width:110,align:'left'},
                       {field:'child_status',title:'外协任务子项状态',type:'string',width:110,align:'left'},
                       {field:'unfinished_op',title:'机加出工段工序前未完工序',type:'string',width:120,align:'left'},
                       {field:'next_status',title:'下序工序状态',type:'string',width:110,align:'left'},
                       {field:'weight_item_amt',title:'加权价值',type:'float',width:110,align:'left'},
                       {field:'weight_mark',title:'加权价值标识，月末锁定后会添加此标识',type:'string',width:110,align:'left'},
                       {field:'next_wc_code',title:'下序工作中心后',type:'string',width:110,align:'left'},
                       {field:'position',title:'派送位置',type:'string',width:110,align:'left'},
                       {field:'father_clode_date',title:'涂装父项首次记账时间',type:'string',width:110,align:'left'}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
