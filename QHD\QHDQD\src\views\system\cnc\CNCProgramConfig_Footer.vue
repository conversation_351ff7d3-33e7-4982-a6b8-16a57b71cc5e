<template>
	<div>
		<h3>
			<i class="ivu-icon ivu-icon-ios-information-circle-outline"></i>
			<div class="desc-text">
				<i class="el-icon-s-grid"></i>
				<span>通知单明细</span>
			</div>
		</h3>
		<!-- url="/api/SellOrder/getDetailPage"-->
		<div style="padding: 10px; background: white; padding-top: 0">
			<vol-table ref="tableList" :loadKey="true" :columns="columns" :pagination-hide="true" :height="420"
				:defaultLoadPage="false" @loadBefore="loadBefore" url="api/CNCProgramConfig/getDetailPage"
				:row-index="true" :index="false" :ck="false"></vol-table>
		</div>
	</div>
</template>
<script>
import VolTable from "@/components/basic/VolTable.vue";
export default {
	components: {
		VolTable,
	},
	methods: {
		loadBefore(params, callback) {
			return callback(true);
		},
	},
    data() {
        return {
           tableData: [],
            //更多table配置见文档：http://doc.volcore.xyz/table
            //明细表格配置，从生成的vue文件中可以复制过来
            columns: [{field:'ID',title:'ID',type:'int',width:110,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'ConfigID',title:'主表ID',type:'int',width:110,hidden:true,require:true,align:'left'},
                       {field:'MachiningProgram',title:'加工程序',type:'string',width:220,align:'left',sort:true},
                       {field:'ToolNumber',title:'刀号',type:'string',width:220,align:'left'},
                       {field:'ToolCode',title:'刀型编号',type:'string',width:220,align:'left'},
                       {field:'ToolDiameter',title:'刀具直径',type:'string',width:220,align:'left'},
                       {field:'ToolSettingLength',title:'装刀长度',type:'string',width:220,align:'left'},
                       {field:'SpindleSpeed',title:'转速',type:'string',width:220,align:'left'},
                       {field:'FeedRate',title:'进给',type:'string',width:220,align:'left'},
                       {field:'AllowanceL',title:'余量侧',type:'string',width:220,align:'left'},
                       {field:'AllowanceLD',title:'余量底',type:'string',width:220,align:'left'},
                       {field:'ToolOffsetNumberH',title:'刀补号H',type:'string',width:220,align:'left'},
                       {field:'ToolOffsetNumberD',title:'刀补号D',type:'string',width:220,align:'left'},
                       {field:'Duration',title:'时间',type:'string',width:220,align:'left'},
                       {field:'Remark',title:'备注',type:'string',width:220,align:'left'},
                       {field:'ZDepth',title:'Z深',type:'string',width:220,align:'left'}],
        }
    }
}
</script>
<style scoped>
h3 {
    font-weight: 500;
    padding-left: 10px;
    background: white;
    margin-top: 8px;
    padding-bottom: 5px;
}
</style>
