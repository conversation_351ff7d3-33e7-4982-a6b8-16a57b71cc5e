<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/warehouse/Workpiece_Notice.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/warehouse/Workpiece_Notice.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'NoticeID',
                footer: "Foots",
                cnName: '通知单管理',
                name: 'warehouse/Workpiece_Notice',
                url: "/Workpiece_Notice/",
                sortName: "NoticeID"
            });
            const editFormFields = ref({"WorkpieceCode":"","WorkpieceNumber":"","WorkpieceName":"","WorkpoecMaterial":"","TrayType":"","ClampType":"","Programmer":"","ProgramTime":"","NoticeStatus":""});
            const editFormOptions = ref([[{"title":"通知单编号","field":"WorkpieceCode"},
                               {"title":"工件编号","field":"WorkpieceNumber"},
                               {"title":"工件名称","field":"WorkpieceName"}],
                              [{"dataKey":"工件材质","data":[],"title":"工件材质","field":"WorkpoecMaterial","type":"select"},
                               {"dataKey":"托盘类型","data":[],"title":"托盘类型","field":"TrayType","type":"select"},
                               {"dataKey":"通知夹具类型","data":[],"title":"夹具类型","field":"ClampType","type":"select"}],
                              [{"title":"编程人员","field":"Programmer"},
                               {"title":"程式时间","field":"ProgramTime"},
                               {"dataKey":"通知单状态","data":[],"title":"状态","field":"NoticeStatus","type":"select"}]]);
            const searchFormFields = ref({"WorkpieceCode":"","WorkpieceName":"","WorkpoecMaterial":"","TrayType":"","ClampType":""});
            const searchFormOptions = ref([[{"title":"通知单编号","field":"WorkpieceCode","type":"text"},{"title":"工件名称","field":"WorkpieceName","type":"text"},{"dataKey":"工件材质","data":[],"title":"工件材质","field":"WorkpoecMaterial","type":"select"},{"dataKey":"托盘类型","data":[],"title":"托盘类型","field":"TrayType","type":"select"},{"dataKey":"通知夹具类型","data":[],"title":"夹具类型","field":"ClampType","type":"select"}]]);
            const columns = ref([{field:'NoticeID',title:'编号',type:'int',width:110,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'WorkpieceCode',title:'通知单编号',type:'string',link:true,width:110,align:'left',sort:true},
                       {field:'WorkpieceNumber',title:'工件编号',type:'string',width:120,align:'left'},
                       {field:'WorkpieceName',title:'工件名称',type:'string',width:110,align:'left'},
                       {field:'WorkpoecMaterial',title:'工件材质',type:'string',bind:{ key:'工件材质',data:[]},width:110,align:'left'},
                       {field:'TrayType',title:'托盘类型',type:'string',bind:{ key:'托盘类型',data:[]},width:120,align:'left'},
                       {field:'ClampType',title:'夹具类型',type:'string',bind:{ key:'通知夹具类型',data:[]},width:120,align:'left'},
                       {field:'Programmer',title:'编程人员',type:'string',width:110,align:'left'},
                       {field:'ProgramTime',title:'程式时间',type:'string',width:110,align:'left'},
                       {field:'NoticeStatus',title:'状态',type:'string',bind:{ key:'通知单状态',data:[]},width:120,align:'left'}]);
            const detail = ref({
                cnName: "通知单明细",
                table: "Workpiece_Notice_Details",
                columns: [{field:'ID',title:'编号',type:'int',width:80,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'NCprogram',title:'加工程序',type:'string',width:120,edit:{type:'text'},require:true,align:'left',sort:true},
                       {field:'ToolNumber',title:'刀号',type:'string',width:120,edit:{type:'text'},require:true,align:'left'},
                       {field:'KnifeshapeNumber',title:'刀型编号',type:'string',width:120,edit:{type:'text'},align:'left'},
                       {field:'CuttingTool_Diameter',title:'刀具直径',type:'string',width:120,edit:{type:'text'},align:'left'},
                       {field:'KnifeLoading_length',title:'装刀长度',type:'string',width:120,edit:{type:'text'},align:'left'},
                       {field:'RotationalSpeed',title:'转速',type:'string',width:120,edit:{type:'text'},align:'left'},
                       {field:'Feed',title:'进给',type:'string',width:120,edit:{type:'text'},align:'left'},
                       {field:'Side_Bottom',title:'侧/底',type:'string',width:110,edit:{type:'text'},align:'left'},
                       {field:'Z_Deep',title:'Z深',type:'string',width:120,edit:{type:'text'},align:'left'},
                       {field:'NoticeID',title:'主表ID',type:'int',width:80,hidden:true,align:'left'}],
                sortName: "ID",
                key: "ID"
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
