<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/product/Assignment_ Detail.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/product/Assignment_ Detail.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'ID',
                footer: "Foots",
                cnName: '任务明细',
                name: 'product/Assignment_ Detail',
                url: "/Assignment_ Detail/",
                sortName: "ID"
            });
            const editFormFields = ref({});
            const editFormOptions = ref([]);
            const searchFormFields = ref({});
            const searchFormOptions = ref([]);
            const columns = ref([{field:'ID',title:'编号',type:'int',width:110,require:true,align:'left',sort:true},
                       {field:'TaskCoding',title:'任务编号',type:'string',width:120,align:'left'},
                       {field:'OP_NAME',title:'任务名称',type:'string',width:120,align:'left'},
                       {field:'TaskType',title:'任务类型',type:'string',width:110,align:'left'},
                       {field:'Priority',title:'优先级',type:'string',width:110,align:'left'},
                       {field:'START_DATE',title:'开始时间',type:'string',width:120,align:'left'},
                       {field:'DUE_DATE',title:'结束时间',type:'string',width:120,align:'left'},
                       {field:'PLAN_HOURS',title:'工时',type:'string',width:110,align:'left'},
                       {field:'OrderNumber',title:'排产编号',type:'string',width:120,hidden:true,align:'left'}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
