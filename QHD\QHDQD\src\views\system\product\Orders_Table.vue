<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/product/Orders_Table.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/product/Orders_Table.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'ID',
                footer: "Foots",
                cnName: '生产订单',
                name: 'product/Orders_Table',
                url: "/Orders_Table/",
                sortName: "ORDER_NO"
            });
            const editFormFields = ref({});
            const editFormOptions = ref([]);
            const searchFormFields = ref({"ORDER_NO":"","OP_NAME":"","ITEM_CODE":""});
            const searchFormOptions = ref([[{"title":"订单编号","field":"ORDER_NO","type":"like"},{"title":"工序名称","field":"OP_NAME","type":"like"},{"title":"物料编码","field":"ITEM_CODE","type":"like"}]]);
            const columns = ref([{field:'ORDER_NO',title:'订单编号',type:'string',width:180,align:'left',sort:true},
                       {field:'OP_NO',title:'工序号',type:'string',width:110,align:'left'},
                       {field:'OP_NAME',title:'工序名称',type:'string',width:110,align:'left'},
                       {field:'ITEM_CODE',title:'物料编码',type:'string',width:160,align:'left'},
                       {field:'ITEM_NAME',title:'物料名称',type:'string',width:180,align:'left'},
                       {field:'ITEM_MODEL',title:'型号',type:'string',width:160,align:'left'},
                       {field:'ITEM_NORM',title:'规格',type:'string',width:160,align:'left'},
                       {field:'PLAN_QTY',title:'数量',type:'string',width:120,align:'left'},
                       {field:'WORK_NO',title:'工作令号',type:'string',width:160,align:'left'},
                       {field:'PLAN_NO',title:'计划号',type:'string',width:160,align:'left'},
                       {field:'DEPT_CODE',title:'任务部门编码',type:'string',width:160,align:'left'},
                       {field:'DEPT_NAME',title:'任务部门名称',type:'string',width:160,align:'left'},
                       {field:'WC_CODE',title:'工作中心编码',type:'string',width:160,align:'left'},
                       {field:'WC_NAME',title:'工作中心名称',type:'string',width:160,align:'left'},
                       {field:'START_DATE',title:'计划开工日期',type:'string',width:180,align:'left'},
                       {field:'DUE_DATE',title:'计划完工日期',type:'string',width:180,align:'left'},
                       {field:'PLAN_HOURS',title:'工时',type:'string',width:110,align:'left'},
                       {field:'JOB_KIND',title:'工种',type:'string',width:110,align:'left'},
                       {field:'QC_FLAG',title:'质检标记',type:'string',width:110,align:'left'},
                       {field:'DEPT_CODE_WC',title:'工序部门',type:'string',width:110,align:'left'},
                       {field:'DEPT_NAME_WC',title:'工序部门名称',type:'string',width:110,align:'left'},
                       {field:'ACCUM_QTY',title:'工艺流转码',type:'string',width:110,align:'left'},
                       {field:'NOTE',title:'累计完成量',type:'string',width:120,align:'left'},
                       {field:'ACTUAL_START_DATE',title:'实际开工时间',type:'string',width:110,align:'left'},
                       {field:'ACTUAL_END_DATE',title:'实际完工时间',type:'string',width:110,align:'left'},
                       {field:'OP_STATUS',title:'工序状态',type:'string',width:110,align:'left'},
                       {field:'STAFF_MAN_CODE',title:'记账人编码',type:'string',width:110,align:'left'},
                       {field:'STAFF_MAN_NAME',title:'记账人姓名',type:'string',width:110,align:'left'},
                       {field:'STAFF_TIME',title:'记账时间',type:'string',width:110,align:'left'},
                       {field:'WHETSCHEDULING',title:'是否排产',type:'string',width:120,hidden:true,align:'left'},
                       {field:'ID',title:'编号',type:'int',width:80,hidden:true,readonly:true,require:true,align:'left'}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
