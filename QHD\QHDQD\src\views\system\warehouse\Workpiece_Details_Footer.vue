<template>
	<div>
		<h3>
			<i class="ivu-icon ivu-icon-ios-information-circle-outline"></i>
			<div class="desc-text">
				<i class="el-icon-s-grid"></i>
				<span>通知单明细</span>
			</div>
		</h3>
		<!-- url="/api/SellOrder/getDetailPage"-->
		<div style="padding: 10px; background: white; padding-top: 0">
			<vol-table ref="tableList" :loadKey="true" :columns="columns" :pagination-hide="true" :height="420"
				:defaultLoadPage="false" @loadBefore="loadBefore" url="api/Workpiece_Notice/getDetailPage"
				:row-index="true" :index="false" :ck="false"></vol-table>
		</div>
	</div>
</template>
<script>
import VolTable from "@/components/basic/VolTable.vue";
export default {
	components: {
		VolTable,
	},
	methods: {
		loadBefore(params, callback) {
			return callback(true);
		},
	},
    data() {
        return {
           tableData: [],
            //更多table配置见文档：http://doc.volcore.xyz/table
            //明细表格配置，从生成的vue文件中可以复制过来
            columns: [{field:'ID',title:'编号',type:'int',width:80,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'NCprogram',title:'加工程序',type:'string',width:120,require:true,align:'left',sort:true},
                       {field:'ToolNumber',title:'刀号',type:'string',width:120,require:true,align:'left'},
                       {field:'KnifeshapeNumber',title:'刀型编号',type:'string',width:120,align:'left'},
                       {field:'CuttingTool_Diameter',title:'刀具直径',type:'string',width:120,align:'left'},
                       {field:'KnifeLoading_length',title:'装刀长度',type:'string',width:120,align:'left'},
                       {field:'RotationalSpeed',title:'转速',type:'string',width:120,align:'left'},
                       {field:'Feed',title:'进给',type:'string',width:120,align:'left'},
                       {field:'Side_Bottom',title:'侧/底',type:'string',width:110,align:'left'},
                       {field:'Z_Deep',title:'Z深',type:'string',width:120,align:'left'},
                       {field:'NoticeID',title:'主表ID',type:'int',width:80,align:'left',hidden:true}],
        }
    }
}
</script>
<style scoped>
h3 {
    font-weight: 500;
    padding-left: 10px;
    background: white;
    margin-top: 8px;
    padding-bottom: 5px;
}
</style>
