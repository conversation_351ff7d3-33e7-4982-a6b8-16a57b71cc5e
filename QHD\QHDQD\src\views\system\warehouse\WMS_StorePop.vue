<template>
  <div>
    <vol-box
      v-model="model"
      :padding="0"
      title="入库"
      :width="1000"
      :height="250"
    >
      <div style="width: 100%;padding: 30px;box-sizing: border-box;">
        <el-row :gutter="58">
            <el-col :span="11">
              <el-form-item label="出库编号" prop="deliveryOrgName">
                <div style="display: flex;width: 100%;">
                  <el-input type="text" disabled v-model="ruleForm.deliveryOrgName" placeholder="" />
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="记录类型" prop="deliveryOrgName">
                <div style="display: flex;width: 100%;">
                  <el-input type="text" disabled v-model="ruleForm.deliveryOrgName" placeholder="" />
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="起始仓库" prop="deliveryOrgName">
                <div style="display: flex;width: 100%;">
                  <el-input type="text" disabled v-model="ruleForm.deliveryOrgName" placeholder="" />
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="目标仓库" prop="deliveryOrgName">
                <div style="display: flex;width: 100%;">
                  <el-input type="text" disabled v-model="ruleForm.deliveryOrgName" placeholder="" />
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="22">
                <el-form-item label="备注" label-width="70px" prop="remark">
                  <el-input
                    v-model="ruleForm.remark"
                    disabled
                    maxlength="300"
                    placeholder="请输入备注"
                    show-word-limit
                    type="textarea"
                    :rows="4"
                  />
                </el-form-item>
              </el-col>
          </el-row>
        </div>
        <!-- <el-divider /> -->
        <div style="border-top: 1px solid #eee;padding-top: 6px;display: flex;align-items: center;font-size: 14px;font-weight: 600;">
          <el-icon size="18px" style="margin: 0 10px;"><Menu /></el-icon><span>出入刀具库明细</span>
        </div>
      <cytable
            :table-data="tableData"
            :column="column"
            :config="tableConfig"
        >
            <template #realNum="{row}">
                <el-input type="number" v-model="row.realNum" placeholder="请输入上货数量" />
            </template>
            <template #storageLocation="{row}">
                <el-tree-select 
                    :props="{ label: 'warehouseName', value: 'id', children: 'children' }"
                    :data="selectOptions.location"
                    v-model="row.storageLocation"
                    placeholder="请选择存储货位"
                    :render-after-expand="false"
                />
            </template>
            
        </cytable>
      <!-- <template #footer>
        <el-button
          type="primary"
          @click="savePwd()"
          >确认</el-button
        >
        <el-button
          @click="model = false"
          >关闭</el-button
        >
      </template> -->
    </vol-box>
  </div>
</template>
<script>
import cytable from '@/components/cytable/cytable.vue'
import { defineComponent, defineAsyncComponent } from "vue";
export default defineComponent({
  components: {
    VolBox: defineAsyncComponent(() => import("@/components/basic/VolBox.vue")),
    cytable
  },
  data() {
    return {
      row: {},
      password: "",
      model: false,
      ruleForm:{},
      tableData :[],
        // 表格配置
        tableConfig :{
            isSelection: false,
            border: true,
            showIndex:false,
            height:'400px',
            pageNum:1,
            pageSize:10,
            total:0,
        },
        column:[
            {
            label: "出库编号",
            prop: "stuName",
            //   width: "210px",
            },
            {
            label: "关联物品",
            prop: "codes",
            // width: "140px",
            },
            {
            label: "数量",
            prop: "initialquantity",
            // width: "80px",
            },
            {
            label: "备注",
            prop: "qualifiedquantity",
            // width: "90px",
            },
        ],
    };
  },
  methods: {
    
    open(row) {
      console.log('?????????',row)
      this.row = row;
      this.model = true;
      //this.getData()
    },
    getData() {
      let url =
        "/api/WMS_InStock/selectMaterialDetail?MID=" +
        this.row.WIID
      this.http.get(url).then((x) => {
        
      });
    },
    savePwd() {
      if (!this.password) return this.$Message.error("请输密码");
      if (this.password.length < 6)
        return this.$Message.error("密码长度至少6位");
      let url =
        "/api/user/modifyUserPwd?password=" +
        this.password +
        "&userName=" +
        this.row.UserName;
      this.http.post(url, {}, true).then((x) => {
        if (!x.status) {
          return this.$message.error(x.message);
        }
        this.model = false;
        this.$Message.success(x.message);
      });
    },
  },
  created() {},
})
</script>
<style lang="less" scoped>
h3 {
  font-weight: 500;
  > span:last-child {
    margin-left: 30px;
  }
}
</style>

