<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/warehouse/Workpiece_Notice_Details.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/warehouse/Workpiece_Notice_Details.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'ID',
                footer: "Foots",
                cnName: '通知单明细',
                name: 'warehouse/Workpiece_Notice_Details',
                url: "/Workpiece_Notice_Details/",
                sortName: "ID"
            });
            const editFormFields = ref({"NCprogram":"","ToolNumber":"","KnifeshapeNumber":"","CuttingTool_Diameter":"","KnifeLoading_length":"","RotationalSpeed":"","Feed":"","Side_Bottom":"","Z_Deep":""});
            const editFormOptions = ref([[{"title":"加工程序","required":true,"field":"NCprogram","type":"text"},
                               {"title":"刀号","required":true,"field":"ToolNumber","type":"text"},
                               {"title":"刀型编号","field":"KnifeshapeNumber","type":"text"},
                               {"title":"刀具直径","field":"CuttingTool_Diameter","type":"text"},
                               {"title":"装刀长度","field":"KnifeLoading_length","type":"text"},
                               {"title":"转速","field":"RotationalSpeed","type":"text"},
                               {"title":"进给","field":"Feed","type":"text"},
                               {"title":"侧/底","field":"Side_Bottom","type":"text"},
                               {"title":"Z深","field":"Z_Deep","type":"text"}]]);
            const searchFormFields = ref({});
            const searchFormOptions = ref([]);
            const columns = ref([{field:'ID',title:'编号',type:'int',width:80,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'NCprogram',title:'加工程序',type:'string',width:120,require:true,align:'left',sort:true},
                       {field:'ToolNumber',title:'刀号',type:'string',width:120,require:true,align:'left'},
                       {field:'KnifeshapeNumber',title:'刀型编号',type:'string',width:120,align:'left'},
                       {field:'CuttingTool_Diameter',title:'刀具直径',type:'string',width:120,align:'left'},
                       {field:'KnifeLoading_length',title:'装刀长度',type:'string',width:120,align:'left'},
                       {field:'RotationalSpeed',title:'转速',type:'string',width:120,align:'left'},
                       {field:'Feed',title:'进给',type:'string',width:120,align:'left'},
                       {field:'Side_Bottom',title:'侧/底',type:'string',width:110,align:'left'},
                       {field:'Z_Deep',title:'Z深',type:'string',width:120,align:'left'},
                       {field:'NoticeID',title:'主表ID',type:'int',width:80,hidden:true,align:'left'}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
