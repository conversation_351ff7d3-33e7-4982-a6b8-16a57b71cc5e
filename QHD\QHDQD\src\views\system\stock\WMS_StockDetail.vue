<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/stock/WMS_StockDetail.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/stock/WMS_StockDetail.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'DetailID',
                footer: "Foots",
                cnName: '库存明细',
                name: 'stock/WMS_StockDetail',
                url: "/WMS_StockDetail/",
                sortName: "DetailID"
            });
            const editFormFields = ref({});
            const editFormOptions = ref([]);
            const searchFormFields = ref({});
            const searchFormOptions = ref([]);
            const columns = ref([{field:'DetailID',title:'序号',type:'int',width:110,require:true,align:'left',sort:true},
                       {field:'ID',title:'库存序号',type:'int',width:110,hidden:true,align:'left'},
                       {field:'ItemType',title:'物料类型',type:'int',bind:{ key:'物料类型',data:[]},width:110,align:'left'},
                       {field:'ItemName',title:'物料名称',type:'string',width:110,align:'left'},
                       {field:'ItemCode',title:'物料编号',type:'string',width:110,align:'left'},
                       {field:'ItemState',title:'物料状态',type:'string',width:110,align:'left'},
                       {field:'lifetime',title:'可使用时间',type:'float',width:110,align:'left'},
                       {field:'OrderID',title:'订单编号',type:'string',width:120,hidden:true,align:'left'}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
