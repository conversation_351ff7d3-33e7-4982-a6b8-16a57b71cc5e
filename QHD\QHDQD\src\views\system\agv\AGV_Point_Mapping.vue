<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/agv/AGV_Point_Mapping.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/agv/AGV_Point_Mapping.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'id',
                footer: "Foots",
                cnName: 'AGV点位映射',
                name: 'agv/AGV_Point_Mapping',
                url: "/AGV_Point_Mapping/",
                sortName: "PointCode",
                order: "asc"
            });
            const editFormFields = ref({"PointCode":"","LocationArea":"","AgvPointCode":"","AgvLocationArea":"","Remark":""});
            const editFormOptions = ref([[{"title":"点位","required":true,"field":"PointCode","disabled":true},
                               {"title":"点位所属区域","field":"LocationArea","disabled":true}],
                              [{"title":"AGV对应点位","required":true,"field":"AgvPointCode"},
                               {"title":"AGV对应点位所属区域","field":"AgvLocationArea"}],
                              [{"title":"备注","field":"Remark"}]]);
            const searchFormFields = ref({"PointCode":""});
            const searchFormOptions = ref([[{"title":"点位","field":"PointCode","type":"like"}]]);
            const columns = ref([{field:'id',title:'id',type:'int',width:110,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'PointCode',title:'点位',type:'string',link:true,width:110,readonly:true,require:true,align:'left',sort:true},
                       {field:'LocationArea',title:'点位所属区域',type:'string',width:110,readonly:true,align:'left'},
                       {field:'AgvPointCode',title:'AGV对应点位',type:'string',width:110,require:true,align:'left'},
                       {field:'AgvLocationArea',title:'AGV对应点位所属区域',type:'string',width:110,align:'left'},
                       {field:'Remark',title:'备注',type:'string',width:180,align:'left'}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
