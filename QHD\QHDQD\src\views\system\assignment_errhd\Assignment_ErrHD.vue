<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/assignment_errhd/Assignment_ErrHD.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/assignment_errhd/Assignment_ErrHD.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'id',
                footer: "Foots",
                cnName: '异常排产列表',
                name: 'assignment_errhd/Assignment_ErrHD',
                url: "/Assignment_ErrHD/",
                sortName: "id"
            });
            const editFormFields = ref({});
            const editFormOptions = ref([]);
            const searchFormFields = ref({});
            const searchFormOptions = ref([]);
            const columns = ref([{field:'id',title:'id',type:'int',width:110,require:true,align:'left',sort:true},
                       {field:'AssignmentTime',title:'排产时间',type:'string',width:110,align:'left'},
                       {field:'TotalOrders',title:'排产订单总数',type:'string',width:110,align:'left'},
                       {field:'SuccessfulOrders',title:'成功排产数',type:'string',width:110,align:'left'},
                       {field:'FailedOrders',title:'异常排产数',type:'string',width:110,align:'left'},
                       {field:'Sources',title:'成功排产订单',type:'string',width:220,align:'left'},
                       {field:'ErrOrder',title:'异常排产订单',type:'string',width:220,align:'left'},
                       {field:'ErrContext',title:'异常排产原因',type:'string',width:700,align:'left'}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
