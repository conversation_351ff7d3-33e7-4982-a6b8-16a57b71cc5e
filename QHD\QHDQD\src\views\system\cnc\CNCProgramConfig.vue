<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/cnc/CNCProgramConfig.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/cnc/CNCProgramConfig.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'ConfigID',
                footer: "Foots",
                cnName: 'CNC程序配置主表',
                name: 'cnc/CNCProgramConfig',
                url: "/CNCProgramConfig/",
                sortName: "ConfigID"
            });
            const editFormFields = ref({"WorkpieceName":"","BlankSize":"","Programmer":"","ProcessStepID":"","SequentialProcess":"","DrawingFilePath":"","FixtureSetupNotes":"","XYZeroReference":"","XOffsetValueX2":"","YOffsetValueY2":"","ZZeroReference":"","PalletHeightOffset24":"","ProbeProgram":"","MachiningCS":"","Attach":""});
            const editFormOptions = ref([[{"title":"物料编号","field":"WorkpieceName","colSize":3},
                               {"title":"毛坯尺寸","field":"BlankSize","colSize":3},
                               {"title":"编程人员","field":"Programmer","colSize":3},
                               {"title":"工序编号","field":"ProcessStepID","colSize":3}],
                              [{"title":"串联工序","field":"SequentialProcess","colSize":3},
                               {"title":"图档位置","field":"DrawingFilePath","colSize":3},
                               {"title":"装夹说明","field":"FixtureSetupNotes","colSize":3},
                               {"title":"XY零点方式","field":"XYZeroReference","colSize":3}],
                              [{"title":"x偏置值(x2)","field":"XOffsetValueX2","colSize":3},
                               {"title":"Y偏置值(Y2)","field":"YOffsetValueY2","colSize":3},
                               {"title":"z零点方式","field":"ZZeroReference","colSize":3},
                               {"title":"工件零点至托盘平面高度(24)","field":"PalletHeightOffset24","colSize":3}],
                              [{"title":"探头程序","field":"ProbeProgram","colSize":3},
                               {"title":"加工坐标系","field":"MachiningCS","colSize":3},
                               {"title":"附件","field":"Attach","colSize":3,"type":"file"}]]);
            const searchFormFields = ref({"WorkpieceName":"","Programmer":""});
            const searchFormOptions = ref([[{"title":"物料编号","field":"WorkpieceName"},{"title":"编程人员","field":"Programmer"}]]);
            const columns = ref([{field:'ConfigID',title:'ConfigID',type:'int',width:110,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'WorkpieceName',title:'物料编号',type:'string',width:220,align:'left',sort:true},
                       {field:'BlankSize',title:'毛坯尺寸',type:'string',width:220,align:'left'},
                       {field:'Programmer',title:'编程人员',type:'string',width:220,align:'left'},
                       {field:'ProcessStepID',title:'工序编号',type:'string',width:220,align:'left'},
                       {field:'SequentialProcess',title:'串联工序',type:'string',width:220,align:'left'},
                       {field:'DrawingFilePath',title:'图档位置',type:'string',width:220,align:'left'},
                       {field:'FixtureSetupNotes',title:'装夹说明',type:'string',width:220,align:'left'},
                       {field:'XYZeroReference',title:'XY零点方式',type:'string',width:220,align:'left'},
                       {field:'XOffsetValueX2',title:'x偏置值(x2)',type:'string',width:220,align:'left'},
                       {field:'YOffsetValueY2',title:'Y偏置值(Y2)',type:'string',width:220,align:'left'},
                       {field:'ZZeroReference',title:'z零点方式',type:'string',width:220,align:'left'},
                       {field:'PalletHeightOffset24',title:'工件零点至托盘平面高度(24)',type:'string',width:220,align:'left'},
                       {field:'ProbeProgram',title:'探头程序',type:'string',width:220,align:'left'},
                       {field:'MachiningCS',title:'加工坐标系',type:'string',width:220,align:'left'},
                       {field:'Attach',title:'附件',type:'file',width:220,align:'left'},
                       {field:'WorkpieceCode',title:'工件编号',type:'string',width:220,align:'left'},
                       {field:'WorkpieceMaterial',title:'工件材质',type:'string',width:220,align:'left'},
                       {field:'MachineTool',title:'机床组别',type:'string',width:220,align:'left'},
                       {field:'UpDateTime',title:'更新时间',type:'string',width:220,align:'left'},
                       {field:'FixtureSetupType',title:'夹具类型',type:'string',width:220,align:'left'},
                       {field:'FixtureSetupNumber',title:'单夹数量',type:'string',width:220,align:'left'}]);
            const detail = ref({
                cnName: "CNC程序配置明细表",
                table: "CNCProgramConfig_Detail",
                columns: [{field:'ID',title:'ID',type:'int',width:110,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'ConfigID',title:'主表ID',type:'int',width:110,hidden:true,edit:{type:''},require:true,align:'left'},
                       {field:'MachiningProgram',title:'加工程序',type:'string',width:220,edit:{type:''},align:'left',sort:true},
                       {field:'ToolNumber',title:'刀号',type:'string',width:220,edit:{type:''},align:'left'},
                       {field:'ToolCode',title:'刀型编号',type:'string',width:220,edit:{type:''},align:'left'},
                       {field:'ToolDiameter',title:'刀具直径',type:'string',width:220,edit:{type:''},align:'left'},
                       {field:'ToolSettingLength',title:'装刀长度',type:'string',width:220,edit:{type:''},align:'left'},
                       {field:'SpindleSpeed',title:'转速',type:'string',width:220,edit:{type:''},align:'left'},
                       {field:'FeedRate',title:'进给',type:'string',width:220,edit:{type:''},align:'left'},
                       {field:'AllowanceL',title:'余量侧',type:'string',width:220,edit:{type:''},align:'left'},
                       {field:'AllowanceLD',title:'余量底',type:'string',width:220,edit:{type:''},align:'left'},
                       {field:'ToolOffsetNumberH',title:'刀补号H',type:'string',width:220,edit:{type:''},align:'left'},
                       {field:'ToolOffsetNumberD',title:'刀补号D',type:'string',width:220,edit:{type:''},align:'left'},
                       {field:'Duration',title:'时间',type:'string',width:220,edit:{type:''},align:'left'},
                       {field:'Remark',title:'备注',type:'string',width:220,edit:{type:''},align:'left'},
                       {field:'ZDepth',title:'Z深',type:'string',width:220,edit:{type:''},align:'left'}],
                sortName: "ID",
                key: "ID"
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
