<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/product/Assignment_Table.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/product/Assignment_Table.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'ASSID',
                footer: "Foots",
                cnName: '订单排产',
                name: 'product/Assignment_Table',
                url: "/Assignment_Table/",
                sortName: "ASSID"
            });
            const editFormFields = ref({"OrderNumber":"","START_DATE":"","DUE_DATE":"","PLAN_QTY":"","ITEM_MODEL":"","ITEM_NORM":"","Priority":"","SerialNumber":""});
            const editFormOptions = ref([[{"title":"订单编号","field":"OrderNumber","colSize":3,"type":"text"},
                               {"title":"计划开工日期","field":"START_DATE","colSize":3,"type":"datetime"},
                               {"title":"计划完工日期","field":"DUE_DATE","colSize":3,"type":"datetime"},
                               {"title":"数量","field":"PLAN_QTY","colSize":3,"type":"number"}],
                              [{"title":"物料编号","field":"ITEM_MODEL","colSize":3,"type":"text"},
                               {"title":"规格","field":"ITEM_NORM","colSize":3,"type":"text"},
                               {"dataKey":"生产排单优先级","data":[],"title":"优先级","field":"Priority","colSize":3,"type":"select"},
                               {"title":"排序","field":"SerialNumber","colSize":3,"type":"text"}]]);
            const searchFormFields = ref({});
            const searchFormOptions = ref([]);
            const columns = ref([{field:'ASSID',title:'编号',type:'int',width:80,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'OrderNumber',title:'订单编号',type:'string',width:180,align:'left',sort:true},
                       {field:'START_DATE',title:'计划开工日期',type:'string',width:180,align:'left'},
                       {field:'DUE_DATE',title:'计划完工日期',type:'string',width:180,align:'left'},
                       {field:'PLAN_QTY',title:'数量',type:'string',width:120,align:'left'},
                       {field:'ITEM_MODEL',title:'物料编号',type:'string',width:110,align:'left'},
                       {field:'ITEM_NORM',title:'规格',type:'string',width:110,align:'left'},
                       {field:'Priority',title:'优先级',type:'string',bind:{ key:'生产排单优先级',data:[]},width:120,align:'left'},
                       {field:'SerialNumber',title:'排序',type:'int',width:80,align:'left'},
                       {field:'State',title:'状态',type:'string',width:120,align:'left'}]);
            const detail = ref({
                cnName: "订单排产明细",
                table: "Assignment_Detail",
                columns: [{field:'ID',title:'编号',type:'int',width:110,hidden:true,require:true,align:'left'},
                       {field:'TaskCoding',title:'任务编号',type:'string',width:120,edit:{type:''},require:true,align:'left',sort:true},
                       {field:'OP_NAME',title:'任务名称',type:'string',width:120,edit:{type:''},require:true,align:'left'},
                       {field:'TaskType',title:'任务类型',type:'string',bind:{ key:'任务类型',data:[]},width:110,edit:{type:'select'},require:true,align:'left'},
                       {field:'Priority',title:'优先级',type:'string',bind:{ key:'生产排单优先级',data:[]},width:110,edit:{type:'select'},require:true,align:'left'},
                       {field:'START_DATE',title:'开始时间',type:'string',width:120,edit:{type:'date'},require:true,align:'left'},
                       {field:'DUE_DATE',title:'结束时间',type:'string',width:120,edit:{type:'date'},require:true,align:'left'},
                       {field:'PLAN_HOURS',title:'任务状态',type:'string',bind:{ key:'任务状态',data:[]},width:110,edit:{type:'select'},align:'left'},
                       {field:'ASSID',title:'排产id',type:'int',width:120,hidden:true,align:'left'}],
                sortName: "OrderNumber",
                key: "ID"
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
