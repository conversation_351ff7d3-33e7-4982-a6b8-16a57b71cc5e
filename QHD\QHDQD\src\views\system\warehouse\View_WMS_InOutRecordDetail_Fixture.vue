<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/warehouse/View_WMS_InOutRecordDetail_Fixture.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/warehouse/View_WMS_InOutRecordDetail_Fixture.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'DetailID',
                footer: "Foots",
                cnName: '夹具出入库明细New',
                name: 'warehouse/View_WMS_InOutRecordDetail_Fixture',
                url: "/View_WMS_InOutRecordDetail_Fixture/",
                sortName: "DetailID"
            });
            const editFormFields = ref({});
            const editFormOptions = ref([]);
            const searchFormFields = ref({});
            const searchFormOptions = ref([]);
            const columns = ref([{field:'DetailID',title:'明细ID，主键',type:'int',width:110,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'RecordCode',title:'RecordCode',type:'string',width:180,require:true,align:'left',sort:true},
                       {field:'RecordID',title:'关联出入库记录ID',type:'int',width:110,require:true,align:'left'},
                       {field:'ItemID',title:'关联物品ID',type:'int',width:110,require:true,align:'left'},
                       {field:'Qty',title:'数量',type:'int',width:110,require:true,align:'left'},
                       {field:'CreateID',title:'CreateID',type:'int',width:100,hidden:true,align:'left'},
                       {field:'Creator',title:'Creator',type:'string',width:100,align:'left'},
                       {field:'CreateDate',title:'CreateDate',type:'datetime',width:150,align:'left',sort:true},
                       {field:'ModifyID',title:'ModifyID',type:'int',width:100,hidden:true,align:'left'},
                       {field:'Modifier',title:'Modifier',type:'string',width:100,align:'left'},
                       {field:'ModifyDate',title:'ModifyDate',type:'datetime',width:150,align:'left',sort:true},
                       {field:'Remark',title:'Remark',type:'string',width:110,align:'left'}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
