<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/warehouse/WMS_Fixture.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/warehouse/WMS_Fixture.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'FixtureID',
                footer: "Foots",
                cnName: '夹具管理',
                name: 'warehouse/WMS_Fixture',
                url: "/WMS_Fixture/",
                sortName: "FixtureID"
            });
            const editFormFields = ref({"FixtureCode":"","FixtureName":"","FixtureType":"","Model":"","Material":"","Remark":""});
            const editFormOptions = ref([[{"title":"夹具编码","required":true,"field":"FixtureCode"},
                               {"title":"夹具名称","required":true,"field":"FixtureName"}],
                              [{"dataKey":"夹具类型","data":[],"title":"夹具类型","required":true,"field":"FixtureType","type":"select"}],
                              [{"title":"模型","field":"Model"},
                               {"title":"材料","field":"Material"}],
                              [{"title":"备注","field":"Remark"}]]);
            const searchFormFields = ref({});
            const searchFormOptions = ref([]);
            const columns = ref([{field:'FixtureID',title:'夹具ID',type:'int',width:80,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'ItemID',title:'关联物品基础表',type:'int',width:80,hidden:true,require:true,align:'left'},
                       {field:'FixtureCode',title:'夹具编码',type:'string',width:120,require:true,align:'left',sort:true},
                       {field:'FixtureName',title:'夹具名称',type:'string',width:110,require:true,align:'left'},
                       {field:'FixtureType',title:'夹具类型',type:'byte',bind:{ key:'夹具类型',data:[]},width:110,require:true,align:'left'},
                       {field:'Model',title:'模型',type:'string',width:120,align:'left'},
                       {field:'Material',title:'材料',type:'string',width:120,align:'left'},
                       {field:'Specification',title:'Specification',type:'string',width:220,hidden:true,align:'left'},
                       {field:'CreateID',title:'创建人id',type:'int',width:80,hidden:true,align:'left'},
                       {field:'Creator',title:'创建人',type:'string',width:130,align:'left'},
                       {field:'CreateDate',title:'创建时间',type:'datetime',width:150,align:'left',sort:true},
                       {field:'ModifyID',title:'修改人id',type:'int',width:80,hidden:true,align:'left'},
                       {field:'Modifier',title:'修改人',type:'string',width:130,align:'left'},
                       {field:'ModifyDate',title:'修改时间',type:'datetime',width:150,align:'left',sort:true},
                       {field:'Remark',title:'备注',type:'string',width:110,align:'left'}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
