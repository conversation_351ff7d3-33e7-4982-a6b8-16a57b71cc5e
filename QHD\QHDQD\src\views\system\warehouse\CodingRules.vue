<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/warehouse/CodingRules.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/warehouse/CodingRules.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'ID',
                footer: "Foots",
                cnName: '编码规则',
                name: 'warehouse/CodingRules',
                url: "/CodingRules/",
                sortName: "ID"
            });
            const editFormFields = ref({"CodingRules_Code":"","CodingRules_Name":"","CodingRules_Prefix":"","CodeLength":"","SerialNumber":"","SerialLength":"","DateFormat":"","Separator":"","Current_value":"","Reset_daily":"","Create_time":"","Status":"","Remark":""});
            const editFormOptions = ref([[{"title":"编码规则ID","field":"CodingRules_Code"},
                               {"title":"编码规则名称","field":"CodingRules_Name"},
                               {"title":"编码前缀","field":"CodingRules_Prefix"}],
                              [{"title":"编码长度","field":"CodeLength"},
                               {"title":"流水号起始值","field":"SerialNumber"},
                               {"title":"流水号长度","field":"SerialLength"}],
                              [{"title":"日期格式","field":"DateFormat"},
                               {"title":"分隔符","field":"Separator"},
                               {"title":"当前值","field":"Current_value"}],
                              [{"dataKey":"enable","data":[],"title":"是否按日重置","field":"Reset_daily","type":"select"},
                               {"title":"创建时间","field":"Create_time"},
                               {"dataKey":"enable","data":[],"title":"状态","field":"Status","type":"select"}],
                              [{"title":"备注","field":"Remark","colSize":12,"type":"textarea"}]]);
            const searchFormFields = ref({"CodingRules_Code":"","CodingRules_Name":"","CodingRules_Prefix":"","CodeLength":"","SerialNumber":"","SerialLength":""});
            const searchFormOptions = ref([[{"title":"编码规则ID","field":"CodingRules_Code"},{"title":"编码规则名称","field":"CodingRules_Name"},{"title":"编码前缀","field":"CodingRules_Prefix"}],[{"title":"编码长度","field":"CodeLength"},{"title":"流水号起始值","field":"SerialNumber"},{"title":"流水号长度","field":"SerialLength"}]]);
            const columns = ref([{field:'ID',title:'编号',type:'int',width:110,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'CodingRules_Code',title:'编码规则ID',type:'string',width:160,align:'left',sort:true},
                       {field:'CodingRules_Name',title:'编码规则名称',type:'string',width:180,align:'left'},
                       {field:'CodingRules_Prefix',title:'编码前缀',type:'string',width:160,align:'left'},
                       {field:'CodeLength',title:'编码长度',type:'string',width:160,align:'left'},
                       {field:'SerialNumber',title:'流水号起始值',type:'string',width:160,align:'left'},
                       {field:'SerialLength',title:'流水号长度',type:'string',width:160,align:'left'},
                       {field:'DateFormat',title:'日期格式',type:'string',width:160,align:'left'},
                       {field:'Separator',title:'分隔符',type:'string',width:160,align:'left'},
                       {field:'Current_value',title:'当前值',type:'string',width:160,align:'left'},
                       {field:'Reset_daily',title:'是否按日重置',type:'int',bind:{ key:'enable',data:[]},width:160,align:'left'},
                       {field:'Create_time',title:'创建时间',type:'datetime',width:160,align:'left',sort:true},
                       {field:'Status',title:'状态',type:'int',bind:{ key:'enable',data:[]},width:160,align:'left'},
                       {field:'Remark',title:'备注',type:'string',width:220,align:'left'}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
