<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/warehouse/WMS_ToolInfo.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/warehouse/WMS_ToolInfo.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'Id',
                footer: "Foots",
                cnName: '刀具入库',
                name: 'warehouse/WMS_ToolInfo',
                url: "/WMS_ToolInfo/",
                sortName: "Id"
            });
            const editFormFields = ref({"Name":"","Code":"","Specification":"","Brand":"","SupplierCode":"","Qty":"","WarehouseId":"","ToolCategory":"","CuttingParameters":"","CoatingProcess":"","LifeExpectancy":"","Remark":""});
            const editFormOptions = ref([[{"dataKey":"远程 物料 刀具","data":[],"title":"物品编码","required":true,"field":"Code","type":"remoteSearch"},
                               {"title":"物品名称","required":true,"field":"Name"},
                               {"title":"型号/规格","field":"Specification"}],
                              [{"title":"品牌/制造商","field":"Brand"},
                               {"title":"供应商编码","field":"SupplierCode"},
                               {"title":"入库数量","required":true,"field":"Qty","type":"number"}],
                              [{"dataKey":"仓库库位","data":[],"title":"库位编号","field":"WarehouseId","type":"select"},
                               {"dataKey":"刀具类型","data":[],"title":"刀具类型","field":"ToolCategory","type":"select"}],
                              [{"title":"切削参数","field":"CuttingParameters"},
                               {"title":"涂层工艺","field":"CoatingProcess"},
                               {"title":"使用寿命（小时）","field":"LifeExpectancy","type":"number"}],
                              [{"title":"备注，记录特殊要求（如\"需干燥存放\"\"禁止磕碰\"）","field":"Remark","type":"textarea"}]]);
            const searchFormFields = ref({"Name":"","Code":"","Specification":"","WarehouseId":"","InventoryStatus":"","ToolCategory":""});
            const searchFormOptions = ref([[{"dataKey":"远程 物料 刀具","data":[],"title":"物品编码","field":"Code"},{"title":"物品名称","field":"Name"},{"title":"型号/规格","field":"Specification"}],[{"dataKey":"仓库库位","data":[],"title":"库位编号","field":"WarehouseId"},{"dataKey":"库存状态","data":[],"title":"库存状态","field":"InventoryStatus","type":"number"},{"dataKey":"刀具类型","data":[],"title":"刀具类型","field":"ToolCategory"}]]);
            const columns = ref([{field:'Id',title:'刀具ID，自增主键',type:'int',width:80,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'Name',title:'物品名称',type:'string',link:true,width:110,require:true,align:'left',sort:true},
                       {field:'Code',title:'物品编码',type:'string',bind:{ key:'远程 物料 刀具',data:[]},width:120,require:true,align:'left'},
                       {field:'Specification',title:'型号/规格',type:'string',width:220,align:'left'},
                       {field:'Brand',title:'品牌/制造商',type:'string',width:110,align:'left'},
                       {field:'SupplierCode',title:'供应商编码',type:'string',width:120,align:'left'},
                       {field:'Qty',title:'入库数量',type:'int',width:80,require:true,align:'left'},
                       {field:'InDate',title:'入库日期',type:'datetime',width:150,align:'left',sort:true},
                       {field:'WarehouseId',title:'库位编号',type:'string',bind:{ key:'仓库库位',data:[]},width:120,align:'left'},
                       {field:'InventoryStatus',title:'库存状态',type:'int',bind:{ key:'库存状态',data:[]},width:80,hidden:true,require:true,align:'left'},
                       {field:'ToolCategory',title:'刀具类型',type:'string',bind:{ key:'刀具类型',data:[]},width:120,align:'left'},
                       {field:'CuttingParameters',title:'切削参数',type:'string',width:120,align:'left'},
                       {field:'CoatingProcess',title:'涂层工艺',type:'string',width:120,align:'left'},
                       {field:'LifeExpectancy',title:'使用寿命（小时）',type:'int',width:80,align:'left'},
                       {field:'InspectionResult',title:'检验结果',type:'int',width:80,align:'left'},
                       {field:'Inspector',title:'检验员',type:'string',width:120,align:'left'},
                       {field:'Remark',title:'备注，记录特殊要求（如"需干燥存放""禁止磕碰"）',type:'string',width:220,align:'left'},
                       {field:'CreateID',title:'CreateID',type:'int',width:80,hidden:true,align:'left'},
                       {field:'Creator',title:'创建人',type:'string',width:130,align:'left'},
                       {field:'CreateDate',title:'创建日期',type:'datetime',width:150,align:'left',sort:true},
                       {field:'ModifyID',title:'ModifyID',type:'int',width:80,hidden:true,align:'left'},
                       {field:'Modifier',title:'修改人',type:'string',width:130,align:'left'},
                       {field:'ModifyDate',title:'修改日期',type:'datetime',width:150,align:'left',sort:true}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
