<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/warehouse/WMS_InStock.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/warehouse/WMS_InStock.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'WIID',
                footer: "Foots",
                cnName: '入库管理',
                name: 'warehouse/WMS_InStock',
                url: "/WMS_InStock/",
                sortName: "WIID"
            });
            const editFormFields = ref({"Name":"","Type":"","BatchNumber":"","CreateDate":"","Static":""});
            const editFormOptions = ref([[{"title":"单据编号","field":"Name"},
                               {"dataKey":"DocumentsType","data":[],"title":"单据类型","field":"Type","type":"select"}],
                              [{"title":"批次号","field":"BatchNumber"},
                               {"title":"创建日期","field":"CreateDate","type":"datetime"}],
                              [{"dataKey":"入库状态","data":[],"title":"状态","field":"Static","type":"select"}]]);
            const searchFormFields = ref({});
            const searchFormOptions = ref([]);
            const columns = ref([{field:'Name',title:'单据编号',type:'string',link:true,width:120,align:'left',sort:true},
                       {field:'Type',title:'单据类型',type:'int',bind:{ key:'DocumentsType',data:[]},width:80,align:'left'},
                       {field:'BatchNumber',title:'批次号',type:'string',width:120,align:'left'},
                       {field:'SN',title:'SN号',type:'string',width:120,hidden:true,align:'left'},
                       {field:'StuId',title:'物料名称',type:'string',width:120,hidden:true,align:'left'},
                       {field:'Model',title:'规格',type:'string',width:120,hidden:true,align:'left'},
                       {field:'IsAssembly',title:'IsAssembly',type:'string',width:120,hidden:true,align:'left'},
                       {field:'WarehouseName',title:'仓库名称',type:'string',width:120,hidden:true,align:'left'},
                       {field:'CreateTime',title:'创建时间',type:'datetime',width:150,hidden:true,align:'left',sort:true},
                       {field:'Static',title:'状态',type:'string',bind:{ key:'入库状态',data:[]},width:120,align:'left'},
                       {field:'Qty',title:'数量',type:'decimal',width:110,hidden:true,align:'left'},
                       {field:'Sguid',title:'Sguid',type:'string',width:120,hidden:true,align:'left'},
                       {field:'WarehouseId',title:'仓库',type:'string',bind:{ key:'仓库库位',data:[]},width:120,hidden:true,align:'left'},
                       {field:'CreateID',title:'CreateID',type:'int',width:80,hidden:true,align:'left'},
                       {field:'Creator',title:'Creator',type:'string',width:130,hidden:true,align:'left'},
                       {field:'CreateDate',title:'创建日期',type:'datetime',width:150,align:'left',sort:true},
                       {field:'ModifyID',title:'ModifyID',type:'int',width:80,hidden:true,align:'left'},
                       {field:'Modifier',title:'Modifier',type:'string',width:130,hidden:true,align:'left'},
                       {field:'ModifyDate',title:'ModifyDate',type:'datetime',width:150,hidden:true,align:'left',sort:true},
                       {field:'WIID',title:'WIID',type:'int',width:80,hidden:true,readonly:true,require:true,align:'left'}]);
            const detail = ref({
                cnName: "入库明细表",
                table: "WMS_InStockDetails",
                columns: [{field:'ID',title:'id',type:'int',width:110,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'SGUID',title:'SGUID',type:'string',width:220,hidden:true,align:'left'},
                       {field:'Tenant',title:'租户id',type:'string',width:220,hidden:true,align:'left'},
                       {field:'CreatedBy',title:'创建人',type:'int',width:110,hidden:true,align:'left'},
                       {field:'CreatedTime',title:'创建时间',type:'datetime',width:150,hidden:true,align:'left',sort:true},
                       {field:'UpdatedBy',title:'更新人',type:'int',width:110,hidden:true,align:'left'},
                       {field:'UpdatedTime',title:'更新时间',type:'datetime',width:150,hidden:true,align:'left',sort:true},
                       {field:'WIID',title:'出入库表主表',type:'int',width:110,hidden:true,align:'left'},
                       {field:'StuId',title:'物料',type:'int',bind:{ key:'物料编号',data:[]},width:110,edit:{type:'select'},align:'left',sort:true},
                       {field:'Static',title:'状态',type:'string',width:220,align:'left'},
                       {field:'qty',title:'数量',type:'decimal',width:110,edit:{type:''},align:'left'},
                       {field:'pice',title:'单价',type:'decimal',width:110,edit:{type:''},align:'left'},
                       {field:'remark',title:'备注',type:'string',width:220,edit:{type:'textarea'},align:'left'},
                       {field:'CreateID',title:'CreateID',type:'int',width:100,hidden:true,align:'left'},
                       {field:'Creator',title:'Creator',type:'string',width:100,hidden:true,align:'left'},
                       {field:'CreateDate',title:'CreateDate',type:'datetime',width:150,hidden:true,align:'left',sort:true},
                       {field:'ModifyID',title:'ModifyID',type:'int',width:100,hidden:true,align:'left'},
                       {field:'Modifier',title:'Modifier',type:'string',width:100,hidden:true,align:'left'},
                       {field:'ModifyDate',title:'ModifyDate',type:'datetime',width:150,hidden:true,align:'left',sort:true}],
                sortName: "ID",
                key: "ID"
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
