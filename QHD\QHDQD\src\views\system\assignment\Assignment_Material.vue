<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/assignment/Assignment_Material.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/assignment/Assignment_Material.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'ID',
                footer: "Foots",
                cnName: '排产物料',
                name: 'assignment/Assignment_Material',
                url: "/Assignment_Material/",
                sortName: "ID"
            });
            const editFormFields = ref({});
            const editFormOptions = ref([]);
            const searchFormFields = ref({"AssID":""});
            const searchFormOptions = ref([[{"title":"排产订单编号","field":"AssID","type":"number"}]]);
            const columns = ref([{field:'ID',title:'ID',type:'int',width:110,require:true,align:'left',sort:true},
                       {field:'Materialtype',title:'物料类型',type:'string',width:110,align:'left'},
                       {field:'MaterialCode',title:'物料编号',type:'string',width:110,align:'left'},
                       {field:'MaterialName',title:'物料名字',type:'string',width:110,align:'left'},
                       {field:'MaterialNumber',title:'物料数量',type:'int',width:110,align:'left'},
                       {field:'MaterialAdd',title:'物料所在位置',type:'string',width:110,align:'left'},
                       {field:'AssID',title:'排产订单编号',type:'int',width:110,align:'left'}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
