*{
    box-sizing:border-box;
    -moz-box-sizing:border-box; /* Firefox */
    -webkit-box-sizing:border-box; /* Safari */
}
.el-pager li{
    font-weight: 100;
    margin-right: 9px;
    border: 1px solid #eee;
    border-radius: 3px;
    min-width: 28px;
}
.el-pager li.active,.el-pager li:hover{
    background: #ed4014;
    color: white;
}
.el-pagination__editor.el-input .el-input__inner{
    height: 23px;
}


.animated {
    -webkit-animation-duration: 0.5s;
    animation-duration: 0.5s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
  }
  
  @media (print), (prefers-reduced-motion) {
    .animated {
      -webkit-animation: unset !important;
      animation: unset !important;
      -webkit-transition: none !important;
      transition: none !important;
    }
  }
  
  @-webkit-keyframes fadeInDown {
    from {
      opacity: 1;
      -webkit-transform: translate3d(0, -100%, 0);
      transform: translate3d(0, -100%, 0);
    }
  
    to {
      opacity: 1;
      -webkit-transform: translate3d(0, 0, 0);
      transform: translate3d(0, 0, 0);
    }
  }
  
  @keyframes fadeInDown {
    from {
      opacity: 0;
      -webkit-transform: translate3d(0, -100%, 0);
      transform: translate3d(0, -100%, 0);
    }
  
    to {
      opacity: 1;
      -webkit-transform: translate3d(0, 0, 0);
      transform: translate3d(0, 0, 0);
    }
  }
  
  .fadeInDown {
    -webkit-animation-name: fadeInDown;
    animation-name: fadeInDown;
  }

  /* 二维码弹窗样式 */
  .qr-code-dialog {
    .el-message-box {
      width: 400px !important;

      .el-message-box__header {
        padding: 20px 20px 10px;
        text-align: center;

        .el-message-box__title {
          font-size: 16px;
          font-weight: 500;
          color: #303133;
        }
      }

      .el-message-box__content {
        padding: 10px 20px 20px;

        .el-message-box__message {
          margin: 0;

          img {
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;

            &:hover {
              transform: scale(1.05);
              box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
            }
          }

          p {
            margin: 0;
            line-height: 1.5;
          }
        }
      }

      .el-message-box__btns {
        padding: 10px 20px 20px;
        text-align: center;

        .el-button {
          margin: 0 5px;
          min-width: 80px;
        }
      }
    }
  }
  .ivu-message{
    z-index: 999999999 !important;
  }
  .ivu-form-item-content{
    text-align: left;
  }