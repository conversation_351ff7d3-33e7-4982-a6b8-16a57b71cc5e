<template>
	<div>
		<h3>
			<i class="ivu-icon ivu-icon-ios-information-circle-outline"></i>
			<div class="desc-text">
				<i class="el-icon-s-grid"></i>
				<span>排产任务</span>
			</div>
		</h3>
		<!-- url="/api/SellOrder/getDetailPage"-->
		<div style="padding: 10px; background: white; padding-top: 0">
			<vol-table ref="tableList" :loadKey="true" :columns="columns" :pagination-hide="true" :height="420"
				:defaultLoadPage="false" @loadBefore="loadBefore" url="api/Assignment_Table/getDetailPage"
				:row-index="true" :index="false" :ck="false"></vol-table>
		</div>
	</div>
</template>
<script>
import VolTable from "@/components/basic/VolTable.vue";
export default {
	components: {
		VolTable,
	},
	methods: {
		loadBefore(params, callback) {
			return callback(true);
		},
	},
    data() {
        return {
           tableData: [],
            //更多table配置见文档：http://doc.volcore.xyz/table
            //明细表格配置，从生成的vue文件中可以复制过来
            columns: [{field:'ID',title:'编号',type:'int',width:110,require:true,align:'left',sort:true,hidden:true},
                       {field:'TaskCoding',title:'任务编号',type:'string',width:120,align:'left'},
                       {field:'OP_NAME',title:'任务名称',type:'string',width:120,align:'left'},
                       {field:'TaskType',title:'任务类型',type:'string',width:110,align:'left'},
                       {field:'Priority',title:'优先级',type:'string',width:110,align:'left'},
                       {field:'START_DATE',title:'开始时间',type:'string',width:120,align:'left'},
                       {field:'DUE_DATE',title:'结束时间',type:'string',width:120,align:'left'},
                       {field:'PLAN_HOURS',title:'任务状态',type:'string',width:110,align:'left'},
                       {field:'ASSID',title:'排产ID',type:'string',width:120,hidden:true,align:'left'}],
        }
    }
}
</script>
<style scoped>
h3 {
    font-weight: 500;
    padding-left: 10px;
    background: white;
    margin-top: 8px;
    padding-bottom: 5px;
}
</style>
