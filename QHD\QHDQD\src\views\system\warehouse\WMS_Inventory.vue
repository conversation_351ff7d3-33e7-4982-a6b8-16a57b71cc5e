<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/warehouse/WMS_Inventory.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/warehouse/WMS_Inventory.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'InventoryID',
                footer: "Foots",
                cnName: '库存管理',
                name: 'warehouse/WMS_Inventory',
                url: "/WMS_Inventory/",
                sortName: "InventoryID"
            });
            const editFormFields = ref({"ItemID":"","WarehouseId":"","Qty":"","Status":""});
            const editFormOptions = ref([[{"dataKey":"物料编号","data":[],"title":"物品","required":true,"field":"ItemID","type":"select"}],
                              [{"dataKey":"仓库库位","data":[],"title":"仓库","field":"WarehouseId","type":"select"}],
                              [{"title":"库存数量","required":true,"field":"Qty","type":"number"}],
                              [{"dataKey":"库存状态","data":[],"title":"库存状态","required":true,"field":"Status","type":"select"}]]);
            const searchFormFields = ref({"ItemID":"","WarehouseId":"","Status":""});
            const searchFormOptions = ref([[{"dataKey":"物料编号","data":[],"title":"物品","field":"ItemID","type":"select"},{"dataKey":"仓库库位","data":[],"title":"仓库","field":"WarehouseId","type":"select"},{"dataKey":"库存状态","data":[],"title":"库存状态","field":"Status","type":"select"}]]);
            const columns = ref([{field:'InventoryID',title:'库存ID，主键',type:'int',width:80,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'ItemID',title:'物品',type:'int',bind:{ key:'物料编号',data:[]},width:180,require:true,align:'left',sort:true},
                       {field:'WarehouseId',title:'仓库',type:'int',bind:{ key:'仓库库位',data:[]},width:140,align:'left'},
                       {field:'Qty',title:'库存数量',type:'int',width:80,require:true,align:'left'},
                       {field:'Status',title:'库存状态',type:'byte',bind:{ key:'库存状态',data:[]},width:80,require:true,align:'left'},
                       {field:'CreateID',title:'CreateID',type:'int',width:80,hidden:true,align:'left'},
                       {field:'Creator',title:'创建人',type:'string',width:130,align:'left'},
                       {field:'CreateDate',title:'创建时间',type:'datetime',width:150,align:'left',sort:true},
                       {field:'ModifyID',title:'ModifyID',type:'int',width:80,hidden:true,align:'left'},
                       {field:'Modifier',title:'修改人',type:'string',width:130,align:'left'},
                       {field:'ModifyDate',title:'修改时间',type:'datetime',width:150,align:'left',sort:true}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
