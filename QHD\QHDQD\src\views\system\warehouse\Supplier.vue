<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/warehouse/Supplier.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/warehouse/Supplier.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'SupplierID',
                footer: "Foots",
                cnName: '供应商管理',
                name: 'warehouse/Supplier',
                url: "/Supplier/",
                sortName: "SupplierID"
            });
            const editFormFields = ref({"SupplierCode":"","SupplierName":"","ContactPerson":"","ContactPhone":"","ContactEmail":"","ContactAddress":"","Status":"","Description":"","Remark":""});
            const editFormOptions = ref([[{"title":"供应商编号","required":true,"field":"SupplierCode"},
                               {"title":"供应商名称","required":true,"field":"SupplierName"}],
                              [{"title":"联系人姓名","field":"ContactPerson"},
                               {"title":"联系人电话","field":"ContactPhone"}],
                              [{"title":"联系人邮箱","field":"ContactEmail"},
                               {"title":"联系人地址","field":"ContactAddress"}],
                              [{"dataKey":"status","data":[],"title":"状态","field":"Status","type":"select"}],
                              [{"title":"描述","field":"Description","colSize":12,"type":"textarea"}],
                              [{"title":"备注","field":"Remark","colSize":12,"type":"textarea"}]]);
            const searchFormFields = ref({});
            const searchFormOptions = ref([]);
            const columns = ref([{field:'SupplierID',title:'SupplierID',type:'int',width:110,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'SupplierCode',title:'供应商编号',type:'string',link:true,width:180,require:true,align:'left',sort:true},
                       {field:'SupplierName',title:'供应商名称',type:'string',width:200,require:true,align:'left'},
                       {field:'ShortName',title:'供应商简称',type:'string',width:110,hidden:true,align:'left'},
                       {field:'SupplierType',title:'供应商类别',type:'byte',width:110,hidden:true,align:'left'},
                       {field:'Industry',title:'供应商行业',type:'string',width:110,hidden:true,align:'left'},
                       {field:'ContactPerson',title:'联系人姓名',type:'string',width:110,align:'left'},
                       {field:'ContactPhone',title:'联系人电话',type:'string',width:140,align:'left'},
                       {field:'ContactEmail',title:'联系人邮箱',type:'string',width:180,align:'left'},
                       {field:'ContactAddress',title:'联系人地址',type:'string',width:260,align:'left'},
                       {field:'Score',title:'Score',type:'decimal',width:110,hidden:true,align:'left'},
                       {field:'Level',title:'等级',type:'byte',width:110,hidden:true,align:'left'},
                       {field:'Status',title:'状态',type:'byte',bind:{ key:'status',data:[]},width:110,align:'left'},
                       {field:'Description',title:'描述',type:'string',width:260,align:'left'},
                       {field:'Remark',title:'备注',type:'string',width:260,align:'left'},
                       {field:'CreateID',title:'CreateID',type:'int',width:80,hidden:true,align:'left'},
                       {field:'Creator',title:'创建人',type:'string',width:130,hidden:true,align:'left'},
                       {field:'CreateDate',title:'创建时间',type:'date',width:180,align:'left',sort:true},
                       {field:'ModifyID',title:'ModifyID',type:'int',width:80,hidden:true,align:'left'},
                       {field:'Modifier',title:'修改人',type:'string',width:130,hidden:true,align:'left'},
                       {field:'ModifyDate',title:'修改时间',type:'datetime',width:150,hidden:true,align:'left',sort:true}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
