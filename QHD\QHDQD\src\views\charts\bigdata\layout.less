
.big-data-container {
  position: absolute;
  overflow: hidden;
  height: 100%;
  width: 100%;
  background-color: #1400a8;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100%25' height='100%25' viewBox='0 0 1200 800'%3E%3Cdefs%3E%3CradialGradient id='a' cx='0' cy='800' r='800' gradientUnits='userSpaceOnUse'%3E%3Cstop offset='0' stop-color='%230e0077'/%3E%3Cstop offset='1' stop-color='%230e0077' stop-opacity='0'/%3E%3C/radialGradient%3E%3CradialGradient id='b' cx='1200' cy='800' r='800' gradientUnits='userSpaceOnUse'%3E%3Cstop offset='0' stop-color='%2314057c'/%3E%3Cstop offset='1' stop-color='%2314057c' stop-opacity='0'/%3E%3C/radialGradient%3E%3CradialGradient id='c' cx='600' cy='0' r='600' gradientUnits='userSpaceOnUse'%3E%3Cstop offset='0' stop-color='%230d0524'/%3E%3Cstop offset='1' stop-color='%230d0524' stop-opacity='0'/%3E%3C/radialGradient%3E%3CradialGradient id='d' cx='600' cy='800' r='600' gradientUnits='userSpaceOnUse'%3E%3Cstop offset='0' stop-color='%231400a8'/%3E%3Cstop offset='1' stop-color='%231400a8' stop-opacity='0'/%3E%3C/radialGradient%3E%3CradialGradient id='e' cx='0' cy='0' r='800' gradientUnits='userSpaceOnUse'%3E%3Cstop offset='0' stop-color='%23000000'/%3E%3Cstop offset='1' stop-color='%23000000' stop-opacity='0'/%3E%3C/radialGradient%3E%3CradialGradient id='f' cx='1200' cy='0' r='800' gradientUnits='userSpaceOnUse'%3E%3Cstop offset='0' stop-color='%23130733'/%3E%3Cstop offset='1' stop-color='%23130733' stop-opacity='0'/%3E%3C/radialGradient%3E%3C/defs%3E%3Crect fill='url(%23a)' width='1200' height='800'/%3E%3Crect fill='url(%23b)' width='1200' height='800'/%3E%3Crect fill='url(%23c)' width='1200' height='800'/%3E%3Crect fill='url(%23d)' width='1200' height='800'/%3E%3Crect fill='url(%23e)' width='1200' height='800'/%3E%3Crect fill='url(%23f)' width='1200' height='800'/%3E%3C/svg%3E");
  background-attachment: fixed;
  background-size: cover;
  .head {
    height: 75px;
    /* height: 1.05rem; */
    background: url(./head_bg.png) no-repeat center center;
    background-size: 100% 100%;
    position: relative;
    z-index: 100;
  }
}

.head h1 {
  margin: 0;
  color: #fff;
  text-align: center;
  /* font-size: .4rem; */
  /* line-height: .8rem; */
  line-height: 71px;
}

.data-container {
  /* margin: 5px 15px;
        height:100%; */

  margin: 0px 15px;
  position: absolute;
  left: 0;
  right: 0;
  top: 76px;
  bottom: 0;
}

.data-container > div {
  float: left;
  /* border: 1px solid white; */
  height: 100%;
}

.data-center {
  padding: 0 0.9rem;
  width: 40%;
  display: flex;
  flex-direction: column;
  // .center-top{
  //   height: 210px;
  //   background: red;
  // }
.chart-center{
  flex: 1;
}
}
.chart-center{
  width: 100%;
display: flex;
//  background: white;
}
.data-left,
.data-right {
  width: 30%;
  display: flex;

  flex-direction: column;
}

.data-left-item,
.data-right-item,.center-top,.center-top-num,.chart-center {
  border: 1px solid rgba(25, 186, 139, 0.17);
  padding: 0 0.2rem 0.4rem 0.15rem;
  background: rgba(255, 255, 255, 0.04);
  background-size: 100% auto;
  position: relative;
  margin-bottom: 0.15rem;
  z-index: 10;
}

.data-foot-line {
  position: absolute;
  bottom: 0;
  width: 100%;
  left: 0;
}

.data-foot-line:before,
.data-foot-line:after {
  position: absolute;
  width: 10px;
  height:10px;
  content: "";
  border-bottom: 2px solid #02a6b5;
  bottom: 0;
}

.boxall:before,
.data-foot-line:before {
  border-left: 2px solid #02a6b5;
  left: 0;
}

.boxall:after,
.data-foot-line:after {
  border-right: 2px solid #02a6b5;
  right: 0;
}

.boxall:before,
.boxall:after {
  position: absolute;
  width: 10px;
  height: 10px;
  content: "";
  border-top: 2px solid #02a6b5;
  top: 0;
}

.data-left-item:before,
.data-right-item:before,
.center-top-num:before,
.center-top:before{
  border-left: 2px solid #02a6b5;
  left: 0;
  position: absolute;
  width: 10px;
  height:10px;
  content: "";
  border-top: 2px solid #02a6b5;
  top: 0;
}

.data-left-item:after,
.data-right-item:after,
.center-top-num:after,
.center-top:after {
  border-right: 2px solid #02a6b5;
  right: 0;
  position: absolute;
  width: 10px;
  height: 10px;
  content: "";
  border-top: 2px solid #02a6b5;
  top: 0;
}

.data-left,
.data-right {
  /* display: flex; */
}

.data-left > .data-left-item,
.data-right > .data-right-item {
  flex: 1;
  margin-bottom: 0.9rem;
}

.data-center  .title,
.data-left > .data-left-item .title,
.data-right > .data-right-item .title {
  /* font-size: .2rem; */
  font-size: 1rem;
  padding: 7px 0;
  color: #fff;
  text-align: center;
  /* line-height: .5rem; */
}

.data-center .chart-center{
  width: 100%;
}

.center-top-num{
  height: 80px;
  padding-top: 7px;
  margin-bottom: 0.8rem;
  display: flex;
  .item{
    flex: 1;
    text-align: center;
  }
  .text{
    color: #fcf0d8;
    font-size: 14px;
  }
  .num{
    font-size: 34px;
    font-family: -apple-system, BlinkMacSystemFont, Segoe UI, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Helvetica Neue, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
    font-weight: bold;
    color: #67caca;
  }
}
